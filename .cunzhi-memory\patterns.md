# 常用模式和最佳实践

- Go项目模块化最佳实践：使用pkg/目录组织包，按功能职责分离(client/config/ecs)，每个包单一职责，添加单元测试，完善错误处理和参数验证，提供使用文档和示例脚本。
- Go项目配置文件最佳实践：使用YAML格式配置文件，支持多路径查找(当前目录、用户目录)，提供LoadFromFileOrArgs函数优雅降级到命令行参数，使用结构体标签映射YAML字段，设置合理默认值，支持dry_run预检模式。
- Go交互式CLI应用最佳实践：使用bufio.Scanner处理用户输入，提供友好的菜单驱动界面，实现输入验证和默认值处理，支持多种输入类型(字符串、整数、布尔、选择)，提供清晰的错误提示和成功反馈，使用emoji增强用户体验。
- Go Web应用最佳实践：使用net/http构建Web服务器，内嵌HTML模板提供单页面应用，使用JavaScript处理前端交互，提供RESTful API接口，支持JSON数据交换，使用响应式CSS设计，提供实时反馈和加载状态。
- Web前端代码分离最佳实践：将HTML模板从Go代码中分离到web/templates目录，CSS样式分离到web/static/css目录，JavaScript代码分离到web/static/js目录。采用模块化设计，通用样式和脚本复用，页面专用样式和脚本独立。提供更好的代码维护性和可扩展性。
- 阿里云管理器JavaScript错误处理最佳实践：统一使用common.js中的showLoading(show, text)函数控制加载状态，show参数为true显示加载，false隐藏加载。避免直接调用hideLoading()等不存在的函数。在异步操作中使用try-catch-finally结构，确保在finally块中调用showLoading(false)隐藏加载状态。
- 阿里云ECS实例管理系统脚本文件上传和执行功能开发完成：1.在instances.html中添加了完整的脚本上传界面（拖拽上传区域、文件信息显示、脚本预览、执行参数输入）；2.在instances.css中添加了丰富的样式支持（拖拽效果、进度条、文件预览等）；3.在instances.js中实现了完整的前端逻辑（文件验证、上传进度、脚本预览、自动执行器识别、安全确认）；4.在server.go中添加了脚本上传API接口；5.支持多种脚本类型（.sh/.py/.ps1/.bat/.js/.pl）的自动识别和执行；6.实现了完整的安全验证（文件大小限制10MB、类型验证、执行确认对话框）；7.测试验证功能完全正常，脚本可以成功上传到ECS实例并执行。
- 阿里云ECS实例管理系统FTP文件下载功能开发完成：1.在instances.html中添加了下载方式选择器和完整的FTP配置界面（主机、端口、用户名、密码、被动模式等）；2.在instances.css中添加了FTP配置区域的样式支持（表单布局、动画效果等）；3.在instances.js中实现了toggleDownloadMethod、startFTPDownload、validateFTPConfig等完整的前端逻辑；4.在server.go中添加了handleFTPDownload API接口和buildFTPDownloadCommand函数，支持构建完整的FTP下载Shell脚本；5.FTP下载脚本支持自动安装ftp客户端、被动模式配置、错误处理等功能；6.测试验证所有功能完全正常，界面切换流畅，配置验证准确。
- 阿里云ECS实例管理系统批量文件下载功能开发完成：1.调研发现阿里云OpenAPI没有专门的FTP管理接口，但可通过云助手API实现文件传输；2.实现了三种下载方案：直接下载（最简单）、Base64编码下载、FTP下载；3.直接下载方案：通过云助手执行cat命令获取文件内容，等待3秒后查询结果，直接通过HTTP响应返回文件内容，适合小文件快速下载；4.Base64编码下载方案：通过云助手执行base64编码命令，处理阿里云API的双重Base64编码问题，支持任意文件类型；5.前端界面支持下载方式选择器，根据选择动态显示相应配置选项；6.所有方案都通过云助手API实现，无需在ECS实例上安装额外软件，安全性高。
