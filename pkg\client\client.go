package client

import (
	env "github.com/alibabacloud-go/darabonba-env/client"
	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	ecs "github.com/alibabacloud-go/ecs-20140526/v3/client"
	"github.com/alibabacloud-go/tea/tea"
	vpc "github.com/alibabacloud-go/vpc-20160428/v6/client"
)

// ECSClient 封装阿里云ECS客户端
type ECSClient struct {
	client *ecs.Client
}

// NewECSClient 创建新的ECS客户端实例
func NewECSClient(regionId string) (*ECSClient, error) {
	config := &openapi.Config{
		// 从环境变量获取AccessKey ID
		AccessKeyId: env.GetEnv(tea.String("ACCESS_KEY_ID")),
		// 从环境变量获取AccessKey Secret
		AccessKeySecret: env.GetEnv(tea.String("ACCESS_KEY_SECRET")),
		// 设置地域ID
		RegionId: tea.String(regionId),
	}

	client, err := ecs.NewClient(config)
	if err != nil {
		return nil, err
	}

	return &ECSClient{
		client: client,
	}, nil
}

// GetClient 获取原始的ECS客户端
func (c *ECSClient) GetClient() *ecs.Client {
	return c.client
}

// VPCClient 封装阿里云VPC客户端
type VPCClient struct {
	client *vpc.Client
}

// NewVPCClient 创建新的VPC客户端实例
func NewVPCClient(regionId string) (*VPCClient, error) {
	config := &openapi.Config{
		// 从环境变量获取AccessKey ID
		AccessKeyId: env.GetEnv(tea.String("ACCESS_KEY_ID")),
		// 从环境变量获取AccessKey Secret
		AccessKeySecret: env.GetEnv(tea.String("ACCESS_KEY_SECRET")),
		// 设置地域ID
		RegionId: tea.String(regionId),
	}

	client, err := vpc.NewClient(config)
	if err != nil {
		return nil, err
	}

	return &VPCClient{
		client: client,
	}, nil
}

// GetClient 获取原始的VPC客户端
func (c *VPCClient) GetClient() *vpc.Client {
	return c.client
}
