# 阿里云ECS管理器

这是一个用于管理阿里云ECS实例的Go应用程序，经过重构优化，采用了模块化的代码结构。

## 项目结构

```
aliyun-manager/
├── main.go                 # 程序入口点
├── go.mod                  # Go模块定义
├── go.sum                  # 依赖版本锁定
├── README.md               # 项目说明文档
├── web/                    # Web界面资源
│   ├── templates/          # HTML模板
│   │   ├── index.html      # 创建实例页面
│   │   ├── release.html    # 释放实例页面
│   │   ├── instances.html  # 实例管理页面
│   │   └── settings.html   # 设置页面
│   └── static/             # 静态资源
│       ├── css/            # 样式文件
│       └── js/             # JavaScript文件
└── pkg/                    # 包目录
    ├── client/             # 客户端管理
    │   └── client.go       # 阿里云ECS客户端封装
    ├── config/             # 配置管理
    │   └── config.go       # 参数解析和验证
    ├── ecs/                # ECS实例管理
    │   └── instance.go     # ECS实例操作（含云助手功能）
    ├── interactive/        # 交互式界面
    │   └── ...             # 交互式组件
    └── web/                # Web服务器
        └── server.go       # Web服务器实现
```

## 代码重构优化

### 重构前的问题
- 所有代码都在一个 `main.go` 文件中，代码结构混乱
- 缺乏模块化设计，难以维护和扩展
- 参数处理和业务逻辑耦合
- 错误处理不够完善

### 重构后的改进
1. **模块化设计**: 将代码按功能拆分到不同的包中
2. **职责分离**: 每个包都有明确的职责
3. **配置管理**: 独立的配置解析和验证逻辑
4. **客户端封装**: 统一的阿里云客户端管理
5. **错误处理**: 更完善的错误处理和日志记录

## 包说明

### pkg/client
- **职责**: 管理阿里云ECS客户端的创建和配置
- **主要功能**:
  - 从环境变量读取AccessKey
  - 创建和配置ECS客户端
  - 提供客户端访问接口

### pkg/config
- **职责**: 处理命令行参数的解析和验证
- **主要功能**:
  - 解析命令行参数到结构化配置
  - 验证参数的有效性
  - 提供配置数据结构

### pkg/ecs
- **职责**: 管理ECS实例的操作
- **主要功能**:
  - 创建ECS实例
  - 处理实例创建过程中的错误
  - 支持扩展更多ECS操作（查询、启动、停止等）

## 使用方法

### 环境变量设置
```bash
export ACCESS_KEY_ID="your_access_key_id"
export ACCESS_KEY_SECRET="your_access_key_secret"
```

### 运行程序

#### 方式1: 使用配置文件（推荐）
```bash
# 使用默认配置文件 aliyun-manager.yaml
go run main.go

# 指定配置文件路径
go run main.go --config /path/to/your/config.yaml
```

#### 方式2: 使用命令行参数
```bash
go run main.go [regionId] [imageId] [instanceType] [securityGroupId] [vSwitchId] [internetMaxBandwidthOut] [internetChargeType] [systemDiskSize] [systemDiskCategory] [instanceChargeType]
```

### 参数说明
1. `regionId`: 地域ID
2. `imageId`: 镜像ID
3. `instanceType`: 实例规格
4. `securityGroupId`: 安全组ID
5. `vSwitchId`: 虚拟交换机ID
6. `internetMaxBandwidthOut`: 公网出带宽最大值 (0-100 Mbit/s)
7. `internetChargeType`: 网络计费类型 (PayByBandwidth/PayByTraffic)
8. `systemDiskSize`: 系统盘大小
9. `systemDiskCategory`: 系统盘类型
10. `instanceChargeType`: 实例计费方式 (PrePaid/PostPaid)

### 配置文件说明

项目支持使用YAML格式的配置文件，这比命令行参数更方便管理。

#### 配置文件查找顺序
程序会按以下顺序查找配置文件：
1. 通过 `--config` 参数指定的文件
2. 当前目录下的 `aliyun-manager.yaml`
3. 当前目录下的 `aliyun-manager.yml`
4. 当前目录下的 `config.yaml`
5. 当前目录下的 `config.yml`
6. 用户主目录下的 `.aliyun-manager.yaml`
7. 用户主目录下的 `.aliyun-manager.yml`

#### 配置文件示例
```yaml
ecs:
  region_id: "cn-hangzhou"
  image_id: "ubuntu_20_04_x64_20G_alibase_20210420.vhd"
  instance_type: "ecs.t5-lc1m1.small"
  security_group_id: "sg-bp1fg655nh68xyz9****"
  vswitch_id: "vsw-bp1s5fnvk4gn2tws0****"
  internet_max_bandwidth_out: 1
  internet_charge_type: "PayByTraffic"
  system_disk_size: "40"
  system_disk_category: "cloud_efficiency"
  instance_charge_type: "PostPaid"
  instance_name: "MyEcsInstance"
  description: "Created by aliyun-manager"
  dry_run: false  # 预检模式，不实际创建实例
```

#### 配置文件优势
- **易于管理**: 避免长长的命令行参数
- **可重用**: 保存常用配置，避免重复输入
- **版本控制**: 可以将配置文件加入版本控制
- **默认值**: 支持设置默认值，简化配置
- **预检模式**: 支持 `dry_run` 选项进行参数验证

## 扩展性

新的代码结构支持轻松扩展：

1. **添加新的ECS操作**: 在 `pkg/ecs/instance.go` 中添加新方法
2. **支持其他阿里云服务**: 在 `pkg/client/` 中添加新的客户端
3. **增加配置选项**: 在 `pkg/config/config.go` 中扩展配置结构
4. **改进错误处理**: 在各个包中完善错误处理逻辑

## Web界面功能

### 启动Web服务器
```bash
go run main.go --port 8080
```

### 功能页面

#### 1. 创建实例页面 (`/`)
- 表单驱动的ECS实例创建
- 实时参数验证
- 配置保存和加载
- 批量创建支持

#### 2. 释放实例页面 (`/release`)
- 实例列表展示
- 批量选择和释放
- 删除保护处理
- 预检模式支持

#### 3. 实例管理页面 (`/instances`) - 🆕
- **实例列表展示**:
  - 显示所有ECS实例的详细信息
  - 支持按状态、名称筛选
  - 多选实例支持
  - 实时刷新功能

- **Shell命令执行**:
  - 在选定实例上执行Shell命令
  - 常用命令快捷按钮
  - 实时显示执行结果
  - 命令历史记录

- **支持的常用命令**:
  - `ls -la` - 列出文件详情
  - `df -h` - 查看磁盘使用情况
  - `free -m` - 查看内存使用情况
  - `ps aux` - 查看进程列表
  - `top -n 1` - 查看系统状态
  - `uname -a` - 查看系统信息
  - `uptime` - 查看系统运行时间
  - `whoami` - 查看当前用户

#### 4. 设置页面 (`/settings`)
- 阿里云访问密钥配置
- 系统参数设置
- 配置文件管理

### API端点

#### 实例管理API
- `GET /api/instances` - 获取实例列表
- `POST /api/instances/run-command` - 执行Shell命令
- `GET /api/instances/get-command-result` - 获取命令执行结果

#### 配置管理API
- `POST /api/config/save` - 保存配置
- `POST /api/config/load` - 加载配置
- `GET /api/config/list` - 列出配置文件
- `POST /api/config/delete` - 删除配置文件

### 使用示例

#### 执行Shell命令
```bash
curl -X POST http://localhost:8080/api/instances/run-command \
  -H "Content-Type: application/json" \
  -d '{
    "regionId": "cn-hangzhou",
    "instanceIds": ["i-bp1234567890abcdef"],
    "commandContent": "ls -la",
    "commandType": "RunShellScript"
  }'
```

#### 获取命令执行结果
```bash
curl "http://localhost:8080/api/instances/get-command-result?regionId=cn-hangzhou&invokeId=t-bp1234567890abcdef"
```

## 开发建议

1. 遵循Go的包命名约定
2. 保持每个包的职责单一
3. 添加单元测试
4. 完善错误处理和日志记录
5. 考虑添加配置文件支持

## 注意事项

1. **环境变量**: 确保设置了`ACCESS_KEY_ID`和`ACCESS_KEY_SECRET`环境变量
2. **云助手**: 目标ECS实例需要安装并运行云助手客户端
3. **权限**: 确保阿里云账号有执行云助手命令的权限
4. **网络**: 确保ECS实例可以访问阿里云云助手服务
