package web

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestHandleInstancesPage(t *testing.T) {
	server := NewServer(8080)

	req, err := http.NewRequest("GET", "/instances", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(server.handleInstancesPage)

	handler.ServeHTTP(rr, req)

	// 如果模板文件不存在，会返回500错误，这在测试环境中是正常的
	if status := rr.Code; status != http.StatusOK && status != http.StatusInternalServerError {
		t.Errorf("handler returned unexpected status code: got %v want %v or %v",
			status, http.StatusOK, http.StatusInternalServerError)
	}

	// 只有在成功加载模板时才检查内容
	if rr.Code == http.StatusOK {
		if !bytes.Contains(rr.Body.Bytes(), []byte("ECS实例管理")) {
			t.Error("Response should contain '实例管理' title")
		}
	}
}

func TestHandleRunCommandValidation(t *testing.T) {
	server := NewServer(8080)

	// 测试空请求体
	req, err := http.NewRequest("POST", "/api/instances/run-command", bytes.NewBuffer([]byte("{}")))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("Content-Type", "application/json")

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(server.handleRunCommand)

	handler.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	var response APIResponse
	if err := json.Unmarshal(rr.Body.Bytes(), &response); err != nil {
		t.Fatal("Failed to parse JSON response")
	}

	if response.Success {
		t.Error("Expected validation to fail for empty request")
	}

	if response.Error == "" {
		t.Error("Expected error message for validation failure")
	}
}

func TestHandleRunCommandMissingParams(t *testing.T) {
	server := NewServer(8080)

	testCases := []struct {
		name        string
		requestBody map[string]interface{}
		expectError string
	}{
		{
			name: "Missing regionId",
			requestBody: map[string]interface{}{
				"instanceIds":    []string{"i-test123"},
				"commandContent": "ls -la",
			},
			expectError: "地域ID不能为空",
		},
		{
			name: "Missing instanceIds",
			requestBody: map[string]interface{}{
				"regionId":       "cn-hangzhou",
				"commandContent": "ls -la",
			},
			expectError: "实例ID列表不能为空",
		},
		{
			name: "Missing commandContent",
			requestBody: map[string]interface{}{
				"regionId":    "cn-hangzhou",
				"instanceIds": []string{"i-test123"},
			},
			expectError: "命令内容不能为空",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			jsonData, _ := json.Marshal(tc.requestBody)
			req, err := http.NewRequest("POST", "/api/instances/run-command", bytes.NewBuffer(jsonData))
			if err != nil {
				t.Fatal(err)
			}
			req.Header.Set("Content-Type", "application/json")

			rr := httptest.NewRecorder()
			handler := http.HandlerFunc(server.handleRunCommand)

			handler.ServeHTTP(rr, req)

			var response APIResponse
			if err := json.Unmarshal(rr.Body.Bytes(), &response); err != nil {
				t.Fatal("Failed to parse JSON response")
			}

			if response.Success {
				t.Error("Expected validation to fail")
			}

			if response.Error != tc.expectError {
				t.Errorf("Expected error '%s', got '%s'", tc.expectError, response.Error)
			}
		})
	}
}

func TestHandleGetCommandResultValidation(t *testing.T) {
	server := NewServer(8080)

	testCases := []struct {
		name        string
		queryParams string
		expectError string
	}{
		{
			name:        "Missing regionId",
			queryParams: "invokeId=test123",
			expectError: "地域ID不能为空",
		},
		{
			name:        "Missing invokeId",
			queryParams: "regionId=cn-hangzhou",
			expectError: "执行ID不能为空",
		},
		{
			name:        "Missing both params",
			queryParams: "",
			expectError: "地域ID不能为空",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			url := "/api/instances/get-command-result"
			if tc.queryParams != "" {
				url += "?" + tc.queryParams
			}

			req, err := http.NewRequest("GET", url, nil)
			if err != nil {
				t.Fatal(err)
			}

			rr := httptest.NewRecorder()
			handler := http.HandlerFunc(server.handleGetCommandResult)

			handler.ServeHTTP(rr, req)

			var response APIResponse
			if err := json.Unmarshal(rr.Body.Bytes(), &response); err != nil {
				t.Fatal("Failed to parse JSON response")
			}

			if response.Success {
				t.Error("Expected validation to fail")
			}

			if response.Error != tc.expectError {
				t.Errorf("Expected error '%s', got '%s'", tc.expectError, response.Error)
			}
		})
	}
}
