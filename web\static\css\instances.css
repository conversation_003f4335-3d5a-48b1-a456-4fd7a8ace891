/* 实例管理页面样式 */

.header-instances {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

/* 实例列表容器 - 参考释放页面样式 */
.instances-list-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    padding: 15px;
    background: #f8f9fa;
}

/* 实例卡片样式 */
.instance-card {
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 10px;
    background: white;
    transition: box-shadow 0.2s;
}

.instance-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.instance-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.instance-header input[type="checkbox"] {
    margin-right: 10px;
}

.instance-name {
    font-weight: bold;
    margin-right: 10px;
}

.instance-status {
    padding: 2px 8px;
    color: white;
    border-radius: 4px;
    font-size: 12px;
    margin-left: 10px;
}

.status-running {
    background: #28a745;
}

.status-stopped {
    background: #6c757d;
}

.status-starting {
    background: #ffc107;
    color: #212529;
}

.status-stopping {
    background: #fd7e14;
}

/* 实例详情行样式 */
.instance-details {
    margin-top: 10px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    font-size: 0.9em;
}

.detail-label {
    color: #6c757d;
    font-weight: 500;
}

.detail-value {
    color: #495057;
    font-weight: 400;
}

/* 主要内容区域 - 左右分栏布局 */
.main-content {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

/* 左侧面板 - 实例列表 */
.left-panel {
    flex: 1;
    min-width: 0; /* 防止flex项目溢出 */
    max-width: 70%;
}

/* 右侧面板 - Shell命令执行 */
.right-panel {
    flex: 0 0 400px; /* 固定宽度400px，不伸缩 */
    min-width: 350px;
    max-width: 450px;
}

/* 确保卡片在分栏布局中正确显示 */
.left-panel .card,
.right-panel .card {
    height: fit-content;
    margin-bottom: 0;
}

/* 右侧面板的特殊样式调整 */
.right-panel .command-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.right-panel .btn-sm {
    padding: 6px 8px;
    font-size: 0.8em;
    text-align: center;
}

/* 右侧面板的命令结果区域优化 */
.right-panel .command-results,
.right-panel .command-history {
    max-height: 400px;
    overflow-y: auto;
}

.right-panel .result-output {
    max-height: 200px;
    overflow-y: auto;
    font-size: 0.8em;
}

/* 控制面板样式 */
.control-panel {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.filter-panel {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
}

/* 实例列表样式 */
.instances-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 15px;
}

/* 视图控制按钮 */
.view-controls {
    display: flex;
    gap: 5px;
}

.view-btn {
    padding: 6px 12px;
    font-size: 0.85em;
    border: 1px solid #dee2e6;
    background: white;
    color: #6c757d;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.view-btn:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
}

.view-btn.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.batch-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.batch-actions input[type="checkbox"] {
    transform: scale(1.2);
}

.batch-actions label {
    font-weight: 500;
    margin: 0;
}

#selectedCount {
    color: #6c757d;
    font-size: 0.9em;
}

.instances-list {
    display: grid;
    gap: 15px;
}

.instance-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
    cursor: pointer;
    user-select: none;
}

.instance-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-color: #007bff;
    transform: translateY(-1px);
}

.instance-card.selected {
    border-color: #007bff;
    background: #f8f9ff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.2);
}

.instance-card:active {
    transform: translateY(0);
    transition: transform 0.1s ease;
}

.instance-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.instance-select {
    position: absolute;
    top: 15px;
    left: 15px;
}

.instance-select input[type="checkbox"] {
    transform: scale(1.2);
}

.instance-info {
    margin-left: 30px;
    flex: 1;
}

.instance-name {
    font-size: 1.1em;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.instance-id {
    font-size: 0.9em;
    color: #6c757d;
    font-family: 'Courier New', monospace;
}

.instance-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    font-weight: 500;
    text-transform: uppercase;
}

.status-running { background: #d4edda; color: #155724; }
.status-stopped { background: #f8d7da; color: #721c24; }
.status-starting { background: #fff3cd; color: #856404; }
.status-stopping { background: #f1c0c7; color: #721c24; }

.instance-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.detail-item {
    display: flex;
    flex-direction: column;
}

.detail-label {
    font-size: 0.8em;
    color: #6c757d;
    margin-bottom: 2px;
}

.detail-value {
    font-weight: 500;
    color: #333;
}

/* 紧凑视图样式 */
.instances-list.compact-view .instance-card {
    padding: 12px 15px;
    margin-bottom: 8px;
}

.instances-list.compact-view .instance-header {
    margin-bottom: 8px;
}

.instances-list.compact-view .instance-name {
    font-size: 1em;
    margin-bottom: 3px;
}

.instances-list.compact-view .instance-id {
    font-size: 0.8em;
}

.instances-list.compact-view .instance-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 8px;
    margin-top: 8px;
}

.instances-list.compact-view .detail-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 5px;
}

.instances-list.compact-view .detail-label {
    font-size: 0.75em;
    margin-bottom: 0;
    min-width: 60px;
}

.instances-list.compact-view .detail-value {
    font-size: 0.8em;
    font-weight: 400;
}

/* 紧凑视图中隐藏次要信息 */
.instances-list.compact-view .detail-item:nth-child(n+3) {
    display: none;
}

/* 紧凑视图只显示核心信息：实例规格和CPU/内存 */
.instances-list.compact-view .detail-item:nth-child(1),
.instances-list.compact-view .detail-item:nth-child(2) {
    display: flex;
}

/* 命令面板样式 - 优化版 */
.command-panel {
    background: linear-gradient(135deg, #f8fafb 0%, #f1f5f9 100%);
    padding: 24px;
    border-radius: 12px;
    margin-bottom: 24px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.quick-commands {
    margin-bottom: 24px;
    background: white;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #e8f0fe;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.quick-commands h4 {
    margin-bottom: 16px;
    color: #2c3e50;
    font-size: 1.1em;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.quick-commands h4::before {
    content: '⚡';
    font-size: 1.2em;
}

.command-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.85em;
}

.command-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

/* 命令结果样式 */
.command-results {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.command-results h3 {
    margin-bottom: 15px;
    color: #333;
}

.result-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.result-instance {
    font-weight: 600;
    color: #333;
}

.result-status {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8em;
    font-weight: 500;
}

.status-success { background: #d4edda; color: #155724; }
.status-failed { background: #f8d7da; color: #721c24; }
.status-running { background: #fff3cd; color: #856404; }

.result-output {
    background: #2d3748;
    color: #e2e8f0;
    padding: 15px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    white-space: pre-wrap;
    overflow-x: auto;
    max-height: 300px;
    overflow-y: auto;
}

.result-error {
    background: #fed7d7;
    color: #c53030;
    padding: 10px;
    border-radius: 4px;
    margin-top: 10px;
    font-size: 0.9em;
}

/* 命令历史样式 */
.command-history {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.history-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.history-item:hover {
    border-color: #007bff;
    background: #f8f9ff;
}

.history-command {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.history-time {
    font-size: 0.8em;
    color: #6c757d;
}

/* 响应式设计 */
@media (max-width: 768px) {
    /* 移动端：左右分栏改为上下堆叠 */
    .main-content {
        flex-direction: column;
        gap: 15px;
    }

    .left-panel,
    .right-panel {
        flex: none;
        max-width: none;
        min-width: auto;
    }

    .right-panel {
        flex: none;
        width: 100%;
    }

    /* 移动端命令按钮布局调整 */
    .right-panel .command-buttons {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .right-panel .btn-sm {
        padding: 8px 12px;
        font-size: 0.85em;
    }

    .instance-details {
        grid-template-columns: 1fr;
    }

    .command-buttons {
        justify-content: center;
    }

    .btn-sm {
        flex: 1;
        min-width: 120px;
    }

    .command-actions {
        flex-direction: column;
        gap: 10px;
    }

    .instances-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    /* 移动端结果显示区域优化 */
    .right-panel .command-results,
    .right-panel .command-history {
        max-height: 300px;
    }

    .right-panel .result-output {
        max-height: 150px;
        font-size: 0.85em;
    }
}

/* 中等屏幕尺寸优化 */
@media (min-width: 769px) and (max-width: 1024px) {
    .right-panel {
        flex: 0 0 350px;
        min-width: 300px;
        max-width: 380px;
    }

    .right-panel .command-buttons {
        grid-template-columns: 1fr 1fr;
        gap: 6px;
    }

    .right-panel .btn-sm {
        padding: 5px 6px;
        font-size: 0.75em;
    }
}

/* 大屏幕优化 */
@media (min-width: 1200px) {
    .main-content {
        gap: 30px;
    }

    .right-panel {
        flex: 0 0 450px;
        max-width: 500px;
    }

    .right-panel .command-buttons {
        grid-template-columns: 1fr 1fr;
        gap: 10px;
    }

    .right-panel .command-results,
    .right-panel .command-history {
        max-height: 500px;
    }

    .right-panel .result-output {
        max-height: 250px;
    }
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-state-icon {
    font-size: 3em;
    margin-bottom: 15px;
    opacity: 0.5;
}

/* 加载状态 */
.loading-instances {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.loading-instances .spinner {
    width: 40px;
    height: 40px;
    margin: 0 auto 15px;
}

/* 创建实例模态框样式 */
.create-instance-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(2px);
}

.create-instance-modal .modal-content {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    border: 1px solid rgba(255,255,255,0.2);
}

.create-instance-modal .modal-header {
    padding: 24px 24px 0;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.create-instance-modal .modal-title {
    font-size: 1.3em;
    font-weight: 600;
    margin: 0 0 20px 0;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 8px;
}

.create-instance-modal .close {
    font-size: 28px;
    font-weight: bold;
    color: #aaa;
    cursor: pointer;
    line-height: 1;
    margin-bottom: 20px;
}

.create-instance-modal .close:hover {
    color: #000;
}

.create-instance-modal .modal-body {
    padding: 20px 24px;
}

.create-instance-modal .modal-footer {
    padding: 20px 24px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.create-instance-modal .form-section {
    margin-bottom: 25px;
    padding: 20px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: #f8f9fa;
}

.create-instance-modal .form-section h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 1.1em;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.create-instance-modal .eip-config {
    margin-top: 15px;
    padding: 15px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: white;
}

.create-instance-modal .form-help {
    display: block;
    margin-top: 5px;
    font-size: 0.85em;
    color: #6c757d;
}

.create-instance-modal .checkbox-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.create-instance-modal .checkbox-group input[type="checkbox"] {
    margin: 0;
}

.create-instance-modal .loading-spinner {
    display: inline-block;
    width: 14px;
    height: 14px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 释放实例相关样式 - 优化版 */
.operation-section {
    margin-bottom: 25px;
    padding: 24px;
    border: 1px solid #e1e8ed;
    border-radius: 12px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafb 100%);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.operation-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #00c853, #4caf50);
    border-radius: 12px 12px 0 0;
}

.operation-section:hover {
    border-color: #d1d9e0;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

.operation-title {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 1.2em;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;
    padding-bottom: 12px;
    border-bottom: 2px solid #f1f3f4;
    position: relative;
}

.operation-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, #00c853, #4caf50);
    border-radius: 1px;
}

.warning-box {
    background: linear-gradient(135deg, #fff8e1 0%, #fff3cd 100%);
    border: 1px solid #ffcc02;
    color: #8b6914;
    padding: 18px;
    border-radius: 10px;
    margin-bottom: 24px;
    position: relative;
    box-shadow: 0 2px 6px rgba(255, 193, 7, 0.15);
}

.warning-box::before {
    content: '⚠️';
    position: absolute;
    top: 18px;
    left: 18px;
    font-size: 1.2em;
}

.warning-box strong {
    color: #d84315;
    font-weight: 600;
    margin-left: 30px;
}

.warning-box {
    padding-left: 50px;
}

.release-options {
    margin-bottom: 24px;
    background: #f8fafb;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #e8f0fe;
}

.release-options .form-group {
    margin-bottom: 18px;
    position: relative;
}

.release-options .form-group:last-child {
    margin-bottom: 0;
}

.release-options label {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    font-weight: 500;
    color: #374151;
    cursor: pointer;
    padding: 12px 16px;
    border-radius: 8px;
    transition: all 0.2s ease;
    background: white;
    border: 1px solid #e5e7eb;
    position: relative;
}

.release-options label:hover {
    background: #f9fafb;
    border-color: #d1d5db;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.release-options input[type="checkbox"] {
    margin: 0;
    width: 18px;
    height: 18px;
    accent-color: #00c853;
    cursor: pointer;
    flex-shrink: 0;
    margin-top: 2px;
}

.release-options .form-help {
    display: block;
    margin-top: 6px;
    color: #6b7280;
    font-size: 0.875em;
    line-height: 1.4;
    font-weight: 400;
}

.release-actions {
    margin-bottom: 24px;
    padding-top: 20px;
    border-top: 1px solid #f1f3f4;
    text-align: center;
}

/* 释放按钮特殊样式 */
#releaseBtn {
    background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
    color: white;
    padding: 14px 28px;
    font-size: 15px;
    font-weight: 700;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(255, 71, 87, 0.3);
    position: relative;
    overflow: hidden;
}

#releaseBtn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s;
}

#releaseBtn:hover::before {
    left: 100%;
}

#releaseBtn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 24px rgba(255, 71, 87, 0.4);
}

#releaseBtn:disabled {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    color: #6c757d;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    cursor: not-allowed;
}

#releaseBtn:disabled::before {
    display: none;
}

/* 创建实例按钮特殊样式 */
.btn-success {
    position: relative;
}

.btn-success::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn-success:hover::after {
    width: 300px;
    height: 300px;
}

.selected-summary {
    padding: 20px;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border-radius: 12px;
    border: 1px solid #bbdefb;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);
    position: relative;
    overflow: hidden;
}

.selected-summary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, #2196f3, #9c27b0);
}

.summary-header {
    margin-bottom: 16px;
    color: #1565c0;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.summary-header::before {
    content: '📋';
    font-size: 1.1em;
}

.summary-list {
    max-height: 240px;
    overflow-y: auto;
    padding-right: 8px;
}

.summary-list::-webkit-scrollbar {
    width: 6px;
}

.summary-list::-webkit-scrollbar-track {
    background: #f1f3f4;
    border-radius: 3px;
}

.summary-list::-webkit-scrollbar-thumb {
    background: #c1c8cd;
    border-radius: 3px;
}

.summary-list::-webkit-scrollbar-thumb:hover {
    background: #a8b2b8;
}

.summary-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    margin-bottom: 8px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e1e8ed;
    font-size: 0.9em;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.summary-item:hover {
    border-color: #bbdefb;
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.summary-item:last-child {
    margin-bottom: 0;
}

.summary-item .instance-name {
    font-weight: 600;
    color: #1565c0;
    flex-shrink: 0;
}

.summary-item .instance-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 500;
    flex-shrink: 0;
}

.summary-item .instance-id {
    color: #6b7280;
    font-size: 0.8em;
    font-family: 'Courier New', monospace;
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 4px;
    margin-left: auto;
}

.protection-warning {
    margin-top: 16px;
    padding: 12px 16px;
    background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
    border: 1px solid #ffb74d;
    border-radius: 8px;
    color: #e65100;
    font-size: 0.9em;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.protection-warning::before {
    content: '🛡️';
    font-size: 1.1em;
}

/* 通配符相关样式 - 优化版 */
.wildcard-help {
    margin-top: 12px;
    padding: 16px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.wildcard-help small {
    color: #495057;
    font-weight: 500;
}

.wildcard-help code {
    background: #fff;
    color: #e83e8c;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
    border: 1px solid #f8d7da;
}

.wildcard-examples {
    margin-top: 12px;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.example-btn {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #dee2e6;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #495057;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.example-btn:hover {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-color: #007bff;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(0, 123, 255, 0.3);
}

.name-match-preview {
    margin-top: 16px;
    padding: 20px;
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
    border: 1px solid #c3e6c3;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #c3e6c3;
}

.preview-title {
    font-weight: 700;
    color: #2e7d32;
    display: flex;
    align-items: center;
    gap: 8px;
}

.preview-title::before {
    content: '🔍';
    font-size: 1.1em;
}

.match-count {
    font-size: 0.9em;
    color: #4caf50;
    font-weight: 600;
    background: white;
    padding: 4px 12px;
    border-radius: 12px;
    border: 1px solid #c3e6c3;
}

.matched-instances {
    max-height: 180px;
    overflow-y: auto;
    padding-right: 8px;
}

.matched-instances::-webkit-scrollbar {
    width: 6px;
}

.matched-instances::-webkit-scrollbar-track {
    background: #f1f3f4;
    border-radius: 3px;
}

.matched-instances::-webkit-scrollbar-thumb {
    background: #c3e6c3;
    border-radius: 3px;
}

.matched-instances::-webkit-scrollbar-thumb:hover {
    background: #a5d6a7;
}

.matched-instance {
    padding: 10px 14px;
    margin: 6px 0;
    background: white;
    border: 1px solid #e8f5e8;
    border-radius: 8px;
    font-size: 0.9em;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.matched-instance:hover {
    border-color: #4caf50;
    transform: translateX(4px);
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.2);
}

.name-match-confirm {
    margin-top: 16px;
    padding: 20px;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 1px solid #90caf9;
    border-radius: 10px;
    box-shadow: 0 3px 12px rgba(33, 150, 243, 0.15);
    animation: slideIn 0.3s ease-out;
    position: relative;
}

.name-match-confirm::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #2196f3, #9c27b0);
    border-radius: 10px 10px 0 0;
}

.confirm-header {
    margin-bottom: 12px;
    color: #1565c0;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 8px;
}

.confirm-header::before {
    content: '⚡';
    font-size: 1.2em;
}

.confirm-content {
    margin-bottom: 18px;
    font-size: 0.9em;
    color: #424242;
    line-height: 1.5;
    background: white;
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid #e1f5fe;
}

.confirm-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

/* 拖拽选择状态 */
.drag-select-status {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 123, 255, 0.9);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    display: none;
    z-index: 1000;
}

/* 实例列表容器 - 复刻释放页面样式 */
.instances-list-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    padding: 15px;
    background: #f8f9fa;
    position: relative;
}

/* 实例卡片 - 复刻释放页面样式 */
.instance-card {
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 10px;
    background: white;
    transition: box-shadow 0.2s;
    cursor: pointer;
    position: relative;
}

.instance-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.instance-card:last-child {
    margin-bottom: 0;
}

.instance-select {
    position: absolute;
    top: 15px;
    left: 15px;
}

.instance-select input[type="checkbox"] {
    margin: 0;
    cursor: pointer;
}

.instance-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    margin-left: 30px; /* 为复选框留出空间 */
}

.instance-info {
    flex: 1;
}

.instance-name {
    font-weight: bold;
    font-size: 16px;
    color: #333;
    margin-bottom: 2px;
}

.instance-id {
    font-size: 12px;
    color: #666;
    font-family: 'Courier New', monospace;
}

.instance-status {
    padding: 4px 8px;
    color: white;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
}

.status-running {
    background: #28a745;
}

.status-stopped {
    background: #6c757d;
}

.status-starting {
    background: #17a2b8;
}

.status-stopping {
    background: #ffc107;
    color: #212529;
}

.status-unknown {
    background: #dc3545;
}

.instance-details {
    font-size: 14px;
    color: #666;
    line-height: 1.4;
    margin-left: 30px; /* 为复选框留出空间 */
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 8px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
}

.detail-label {
    font-weight: 500;
    color: #495057;
    margin-right: 10px;
}

.detail-value {
    color: #333;
    font-weight: 400;
}

.protection-icon {
    margin-left: 5px;
    font-size: 14px;
}

.protection-warning {
    color: #dc3545;
    font-weight: bold;
}

/* 空状态 */
.empty-state {
    text-align: center;
    color: #666;
    padding: 40px 20px;
    font-style: italic;
}

/* 实例操作按钮组 */
.instance-actions {
    margin-top: 10px;
    display: flex;
    gap: 10px;
    margin-left: 30px; /* 为复选框留出空间 */
}

.instance-actions .btn {
    padding: 6px 12px;
    font-size: 14px;
    margin-right: 0;
}

/* 选择框样式 */
.selection-box {
    position: fixed;
    border: 2px dashed #007bff;
    background: rgba(0, 123, 255, 0.1);
    pointer-events: none;
    z-index: 1000;
    border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .instances-list-container {
        max-height: 300px;
    }

    .instance-header {
        flex-direction: column;
        align-items: flex-start;
        margin-left: 30px;
    }

    .instance-details {
        grid-template-columns: 1fr;
        margin-left: 30px;
    }

    .instance-status {
        margin-top: 5px;
        align-self: flex-start;
    }
}

/* 密码设置面板样式 */
.password-panel {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 20px;
    margin-top: 16px;
}

.password-panel .form-group {
    margin-bottom: 16px;
}

.password-panel .form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    color: #495057;
}

.password-panel .form-group input,
.password-panel .form-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.password-panel .form-group input:focus,
.password-panel .form-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.password-panel .form-group small {
    display: block;
    margin-top: 4px;
    color: #6c757d;
    font-size: 12px;
}

.password-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.password-input-container input {
    flex: 1;
    padding-right: 45px;
}

.password-toggle {
    position: absolute;
    right: 10px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
    color: #6c757d;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.password-toggle:hover {
    background-color: #f8f9fa;
    color: #495057;
}

.password-actions {
    display: flex;
    gap: 12px;
    margin-top: 20px;
}

.password-actions .btn {
    flex: 1;
    padding: 12px 20px;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
}

/* 文件下载面板样式 */
.download-panel {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 20px;
    margin-top: 16px;
}

.download-panel .form-group {
    margin-bottom: 16px;
}

.download-panel .form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    color: #495057;
}

.download-panel .form-group input,
.download-panel .form-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.download-panel .form-group input:focus,
.download-panel .form-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.download-panel .form-group small {
    display: block;
    margin-top: 4px;
    color: #6c757d;
    font-size: 12px;
}

.download-actions {
    display: flex;
    gap: 12px;
    margin-top: 20px;
    justify-content: flex-end;
}

.download-actions .btn {
    padding: 10px 20px;
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.download-actions .btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    color: white;
    box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3);
}

.download-actions .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

.download-actions .btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #545b62 100%);
    border: none;
    color: white;
    box-shadow: 0 2px 6px rgba(108, 117, 125, 0.3);
}

.download-actions .btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4);
}

.download-actions .btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: none;
    color: white;
    box-shadow: 0 2px 6px rgba(220, 53, 69, 0.3);
}

.download-actions .btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
}

/* 下载进度显示样式 */
.download-progress-container {
    margin-top: 20px;
    padding: 20px;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    background: #f8f9fa;
    display: none;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.progress-header h4 {
    margin: 0;
    color: #333;
    font-size: 16px;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 14px;
    color: #666;
}

.file-info {
    display: flex;
    gap: 20px;
}

.download-stats {
    display: flex;
    gap: 20px;
}

.progress-bar-container {
    margin-bottom: 10px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-text {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #666;
}

/* 折叠功能样式 */
.operation-title.collapsible {
    cursor: pointer;
    user-select: none;
    display: flex;
    align-items: center;
    transition: color 0.2s;
}

.operation-title.collapsible:hover {
    color: #007bff;
}

.collapse-icon {
    margin-right: 8px;
    font-size: 12px;
    transition: transform 0.2s;
    display: inline-block;
    width: 12px;
}

.operation-content {
    transition: all 0.3s ease;
    overflow: hidden;
}

/* 脚本上传样式 */
.script-upload-section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #f9f9f9;
}

.script-upload-section h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
}

/* 拖拽上传区域 */
.upload-drop-zone {
    border: 2px dashed #ccc;
    border-radius: 8px;
    padding: 30px;
    text-align: center;
    background-color: #fafafa;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.upload-drop-zone:hover {
    border-color: #007bff;
    background-color: #f0f8ff;
}

.upload-drop-zone.drag-over {
    border-color: #28a745;
    background-color: #f0fff0;
    transform: scale(1.02);
}

.upload-drop-content {
    pointer-events: none;
}

.upload-icon {
    font-size: 48px;
    margin-bottom: 10px;
    opacity: 0.6;
}

.upload-text p {
    margin: 5px 0;
    color: #666;
}

.upload-text strong {
    color: #333;
}

.btn-link {
    background: none;
    border: none;
    color: #007bff;
    text-decoration: underline;
    cursor: pointer;
    padding: 0;
    font-size: inherit;
}

.btn-link:hover {
    color: #0056b3;
}

/* 上传进度 */
.upload-progress {
    margin: 15px 0;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-text {
    text-align: center;
    margin-top: 5px;
    font-size: 14px;
    color: #666;
}

/* 文件信息 */
.file-info {
    margin: 15px 0;
    padding: 10px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
}

.file-details {
    display: flex;
    align-items: center;
    gap: 10px;
}

.file-icon {
    font-size: 24px;
    flex-shrink: 0;
}

.file-meta {
    flex: 1;
}

.file-name {
    font-weight: bold;
    color: #333;
    margin-bottom: 2px;
}

.file-size, .file-type {
    font-size: 12px;
    color: #666;
}

/* 脚本预览 */
.script-preview {
    margin: 15px 0;
}

.script-preview h5 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 14px;
}

.script-preview pre {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 10px;
    font-size: 12px;
    max-height: 200px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.preview-actions {
    margin-top: 10px;
}

/* 脚本参数 */
.script-params {
    margin: 15px 0;
}

.script-params .form-group {
    margin-bottom: 0;
}

/* 分隔线 */
.section-divider {
    text-align: center;
    margin: 20px 0;
    position: relative;
}

.section-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background-color: #ddd;
}

.section-divider span {
    background-color: #fff;
    padding: 0 15px;
    color: #666;
    font-size: 14px;
    position: relative;
}

/* HTTP下载样式 */
.http-download-info {
    background-color: #f0f8ff;
    border: 1px solid #4a90e2;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
}

.http-download-info h5 {
    margin: 0 0 15px 0;
    color: #2c5aa0;
    font-size: 16px;
    font-weight: 600;
}

.http-download-info ul {
    margin: 0;
    padding-left: 20px;
    font-size: 13px;
    line-height: 1.5;
}

.http-download-info li {
    margin-bottom: 5px;
    color: #666;
}

.status-info {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
}

.status-info h4 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 16px;
}

.status-pending {
    color: #ffc107;
    font-weight: 500;
}

.status-success {
    color: #28a745;
    font-weight: 500;
}

.status-error {
    color: #dc3545;
    font-weight: 500;
}

/* 网络状态检查样式 */
.network-summary {
    background: #e3f2fd;
    border: 1px solid #2196f3;
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
}

.network-details {
    background: #f5f5f5;
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
}

.network-item {
    padding: 8px;
    margin: 5px 0;
    border-left: 3px solid #ddd;
    background: white;
}

.network-item .instance-name {
    font-weight: bold;
    color: #333;
}

.network-item .instance-id {
    color: #666;
    font-size: 0.9em;
}

.network-item .network-status {
    display: block;
    margin-top: 4px;
    font-size: 0.9em;
}

.network-suggestions {
    background: #fff3cd;
    border: 1px solid #ffc107;
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
}

/* 下载报告样式 */
.download-report {
    background: #f8f9fa;
    border-radius: 4px;
    padding: 15px;
    margin: 10px 0;
}

.download-report h5 {
    margin-top: 0;
    color: #333;
}

.download-report h6 {
    margin: 15px 0 10px 0;
    color: #555;
}

.success-section {
    margin: 10px 0;
}

.failed-section {
    margin: 10px 0;
}

.skipped-section {
    margin: 10px 0;
}

.report-item {
    padding: 8px;
    margin: 5px 0;
    border-radius: 4px;
    border-left: 4px solid;
}

.report-item.success {
    background: #d4edda;
    border-left-color: #28a745;
}

.report-item.failed {
    background: #f8d7da;
    border-left-color: #dc3545;
}

.report-item.skipped {
    background: #fff3cd;
    border-left-color: #ffc107;
}

.report-item .instance-name {
    font-weight: bold;
}

.report-item .instance-id {
    color: #666;
    font-size: 0.9em;
}

.report-item .error-message,
.report-item .skip-reason {
    display: block;
    margin-top: 4px;
    font-size: 0.85em;
    color: #666;
}

/* 进度条样式增强 */
.progress-container {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.progress-filename {
    font-weight: bold;
    color: #333;
}

.progress-stats {
    color: #666;
    font-size: 0.9em;
}

.progress-bar-container {
    background: #e9ecef;
    border-radius: 4px;
    height: 8px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-bar {
    background: linear-gradient(90deg, #28a745, #20c997);
    height: 100%;
    width: 0%;
    transition: width 0.3s ease;
}

.progress-details {
    font-size: 0.9em;
    color: #666;
    max-height: 200px;
    overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
    }

    .left-panel, .right-panel {
        width: 100%;
        margin-bottom: 20px;
    }

    .instances-list {
        grid-template-columns: 1fr;
    }

    .instance-details {
        grid-template-columns: 1fr;
    }

    .form-row {
        flex-direction: column;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .progress-info {
        flex-direction: column;
        align-items: flex-start;
    }

    .progress-stats {
        margin-top: 5px;
    }
}
