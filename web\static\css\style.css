/* 通用样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f5f5f5;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 15px;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #00c853, #4caf50);
    color: white;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 200, 83, 0.3);
}

/* 统一的header标签样式 */
header {
    background: linear-gradient(135deg, #00c853, #4caf50);
    color: white;
    padding: 1.5rem 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0, 200, 83, 0.3);
}

header h1 {
    margin: 0;
    font-size: 2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

header nav {
    margin-top: 1rem;
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

header nav a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-weight: 500;
}

header nav a:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

header nav a.active {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    font-weight: 600;
}

.header h1 {
    font-size: 2.2em;
    margin-bottom: 8px;
}

.header p {
    font-size: 1.2em;
    opacity: 0.9;
}

/* 导航链接 */
.nav-link {
    color: white;
    text-decoration: none;
    margin-right: 15px;
    padding: 8px 16px;
    background: rgba(255,255,255,0.2);
    border-radius: 4px;
    transition: background 0.3s;
}

.nav-link:hover {
    background: rgba(255,255,255,0.3);
}

.nav-active {
    background: rgba(255,255,255,0.4) !important;
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 表单样式 */
.form-group {
    margin-bottom: 12px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 15px;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
}

/* 按钮样式 - 现代化优化版 */
.btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin-right: 10px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    min-height: 44px;
    justify-content: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    text-decoration: none;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn:active {
    transform: translateY(-1px);
    box-shadow: 0 3px 12px rgba(102, 126, 234, 0.5);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn:disabled::before {
    display: none;
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
}

.btn-secondary:hover {
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #00c853 0%, #4caf50 100%);
    box-shadow: 0 2px 8px rgba(0, 200, 83, 0.3);
}

.btn-success:hover {
    box-shadow: 0 6px 20px rgba(0, 200, 83, 0.4);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%);
    color: #212529;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.btn-warning:hover {
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.btn-danger:hover {
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

.btn-sm {
    padding: 8px 16px;
    font-size: 13px;
    min-height: 36px;
    border-radius: 6px;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.btn-primary:hover {
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

/* 标题样式 */
.section-title {
    font-size: 1.3em;
    color: #333;
    margin-bottom: 12px;
    padding-bottom: 6px;
    border-bottom: 2px solid #667eea;
}

/* 提醒框样式 */
.alert {
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.alert-info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* 现代化加载动画 */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9998;
    backdrop-filter: blur(2px);
}

.loading.show {
    display: flex;
}

.spinner-container {
    text-align: center;
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

.loading-text {
    color: #666;
    font-size: 16px;
    font-weight: 500;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 复选框组 */
.checkbox-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
}

/* 小字说明 */
small {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 14px;
}

/* 现代化通知系统 */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
}

.toast {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    margin-bottom: 10px;
    padding: 16px 20px;
    border-left: 4px solid;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease-in-out;
    position: relative;
    overflow: hidden;
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast.hide {
    opacity: 0;
    transform: translateX(100%);
}

.toast-success {
    border-left-color: #28a745;
    background: linear-gradient(90deg, rgba(40,167,69,0.1) 0%, white 100%);
}

.toast-error {
    border-left-color: #dc3545;
    background: linear-gradient(90deg, rgba(220,53,69,0.1) 0%, white 100%);
}

.toast-warning {
    border-left-color: #ffc107;
    background: linear-gradient(90deg, rgba(255,193,7,0.1) 0%, white 100%);
}

.toast-info {
    border-left-color: #17a2b8;
    background: linear-gradient(90deg, rgba(23,162,184,0.1) 0%, white 100%);
}

.toast-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.toast-title {
    font-weight: 600;
    font-size: 14px;
    margin: 0;
}

.toast-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #999;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toast-close:hover {
    color: #666;
}

.toast-body {
    font-size: 14px;
    line-height: 1.4;
    color: #666;
}

.toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(0,0,0,0.1);
    border-radius: 0 0 8px 8px;
    animation: toast-progress 5s linear forwards;
}

@keyframes toast-progress {
    from { width: 100%; }
    to { width: 0%; }
}

/* 确认对话框 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.6);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-in-out;
    backdrop-filter: blur(2px);
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    transform: scale(0.8);
    transition: transform 0.3s ease-in-out;
    border: 1px solid rgba(255,255,255,0.2);
}

.modal-overlay.show .modal {
    transform: scale(1);
}

.modal-header {
    padding: 24px 24px 0;
    border-bottom: 1px solid #e9ecef;
}

.modal-title {
    font-size: 1.3em;
    font-weight: 600;
    margin: 0 0 20px 0;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 8px;
}

.modal-body {
    padding: 20px 24px;
}

.modal-footer {
    padding: 0 24px 20px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.modal-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.modal-btn-primary {
    background: #28a745;
    color: white;
}

.modal-btn-primary:hover {
    background: #218838;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.modal-btn-danger {
    background: #dc3545;
    color: white;
}

.modal-btn-danger:hover {
    background: #c82333;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.modal-btn-secondary {
    background: #6c757d;
    color: white;
}

.modal-btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .header {
        padding: 15px;
    }

    .header h1 {
        font-size: 1.8em;
    }

    .card {
        padding: 15px;
    }

    .nav-link {
        margin-right: 8px;
        padding: 6px 12px;
        font-size: 14px;
    }

    .toast-container {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .modal {
        margin: 20px;
        width: calc(100% - 40px);
    }
}
