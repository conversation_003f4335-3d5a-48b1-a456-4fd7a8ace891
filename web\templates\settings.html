{{template "header" .}}



        <div class="card">
            <h2 class="section-title">🔑 阿里云访问密钥</h2>
            <div class="info-box">
                <strong>ℹ️ 说明：</strong>
                <ul style="margin-top: 10px; margin-left: 20px;">
                    <li>访问密钥用于调用阿里云API，请妥善保管</li>
                    <li>可以选择保存到本地文件或仅在当前会话中使用</li>
                    <li>本地存储的密钥会加密保存</li>
                    <li>如需获取访问密钥，请访问阿里云控制台</li>
                </ul>
            </div>
            
            <div id="connectionStatus" style="margin-bottom: 20px;">
                <span class="status-indicator status-disconnected"></span>
                <span>未连接到阿里云</span>
            </div>

            <form id="settingsForm">
                <div class="form-group">
                    <label for="accessKeyId">Access Key ID *</label>
                    <input type="text" id="accessKeyId" name="accessKeyId" placeholder="LTAI...">
                </div>
                
                <div class="form-group">
                    <label for="accessKeySecret">Access Key Secret *</label>
                    <div class="password-input">
                        <input type="password" id="accessKeySecret" name="accessKeySecret" placeholder="请输入Access Key Secret">
                        <span class="password-toggle" onclick="togglePassword()">👁️</span>
                    </div>
                </div>

                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="saveToFile" name="saveToFile" checked>
                        <label for="saveToFile">保存到本地文件（推荐）</label>
                    </div>
                    <small style="color: #666;">保存后下次启动时自动加载</small>
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <button type="button" class="btn btn-warning" onclick="testConnection()">测试连接</button>
                    <button type="submit" class="btn">保存设置</button>
                    <button type="button" class="btn btn-danger" onclick="clearSettings()">清除设置</button>
                </div>
            </form>
        </div>

        <div class="card">
            <h2 class="section-title">📊 当前状态</h2>
            <div id="currentSettings">
                <p>正在加载当前设置...</p>
            </div>
        </div>

        <div class="loading" id="loading">
            <div class="spinner-container">
                <div class="spinner"></div>
                <div class="loading-text">正在处理请求，请稍候...</div>
            </div>
        </div>
{{template "footer" .}}
