// 创建实例页面JavaScript

// 验证配置
async function validateConfig() {
    showLoading(true);
    try {
        const data = getFormData('ecsForm');
        const result = await apiRequest('/api/validate', {
            method: 'POST',
            body: JSON.stringify(data)
        });
        
        if (result.success) {
            showAlert('✅ 配置验证通过！', 'success');
        } else {
            showAlert('❌ 配置验证失败: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('❌ 验证请求失败: ' + error.message, 'error');
    }
    showLoading(false);
}

// 保存配置
async function saveConfig() {
    showLoading(true);
    try {
        const data = getFormData('ecsForm');
        const result = await apiRequest('/api/config/save', {
            method: 'POST',
            body: JSON.stringify(data)
        });
        
        if (result.success) {
            showAlert('✅ 配置已保存到文件: ' + result.filename, 'success');
        } else {
            showAlert('❌ 保存配置失败: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('❌ 保存请求失败: ' + error.message, 'error');
    }
    showLoading(false);
}

// 创建实例
async function createInstance(data) {
    showLoading(true);
    try {
        // 处理批量创建的实例名称
        if (data.instanceNamePrefix && data.amount > 1) {
            // 批量创建时，后端会自动处理名称前缀
            data.instanceName = data.instanceNamePrefix;
        } else if (data.instanceNamePrefix) {
            // 单个创建时，直接使用前缀作为名称
            data.instanceName = data.instanceNamePrefix;
        }

        const result = await apiRequest('/api/create', {
            method: 'POST',
            body: JSON.stringify(data)
        });

        if (result.success) {
            if (data.dryRun) {
                showAlert('✅ 预检验证通过！您可以取消预检模式来实际创建 ' + data.amount + ' 台实例', 'success');
            } else {
                const instanceText = data.amount > 1 ?
                    `${data.amount} 台ECS实例（${data.instanceNamePrefix}-001 到 ${data.instanceNamePrefix}-${String(data.amount).padStart(3, '0')}）` :
                    `1 台ECS实例（${data.instanceNamePrefix}）`;
                showAlert('✅ 成功创建 ' + instanceText + '！', 'success');
            }
        } else {
            showAlert('❌ 创建失败: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('❌ 创建请求失败: ' + error.message, 'error');
    }
    showLoading(false);
}

// 验证密码复杂度
function validatePassword(password) {
    if (!password) {
        return null; // 密码为空时不验证
    }

    if (password.length < 8 || password.length > 30) {
        return '密码长度必须在8-30位之间';
    }

    if (!/[A-Z]/.test(password)) {
        return '密码必须包含至少一个大写字母';
    }

    if (!/[a-z]/.test(password)) {
        return '密码必须包含至少一个小写字母';
    }

    if (!/[0-9]/.test(password)) {
        return '密码必须包含至少一个数字';
    }

    return null;
}

// 验证表单数据
function validateCreateForm(data) {
    // 验证必填字段
    const requiredFields = ['regionId', 'imageId', 'instanceType', 'securityGroupId', 'vSwitchId'];
    const error = validateRequired(data, requiredFields);
    if (error) {
        showAlert('❌ ' + error, 'error');
        return false;
    }

    // 验证密码复杂度
    const passwordError = validatePassword(data.password);
    if (passwordError) {
        showAlert('❌ ' + passwordError, 'error');
        return false;
    }

    // 验证数量关系
    if (data.minAmount > data.amount) {
        showAlert('❌ 最少创建数量不能超过创建数量', 'error');
        return false;
    }

    // // 验证带宽范围
    // if (data.internetMaxBandwidthOut < 0 || data.internetMaxBandwidthOut > 100) {
    //     showAlert('❌ 公网带宽必须在0-100之间', 'error');
    //     return false;
    // }

    // 验证系统盘大小
    if (data.systemDiskSize < 20 || data.systemDiskSize > 500) {
        showAlert('❌ 系统盘大小必须在20-500GB之间', 'error');
        return false;
    }

    // 验证创建数量
    if (data.amount < 1 || data.amount > 100) {
        showAlert('❌ 创建数量必须在1-100之间', 'error');
        return false;
    }

    return true;
}

// 数量验证
function setupAmountValidation() {
    const amountInput = document.getElementById('amount');
    const minAmountInput = document.getElementById('minAmount');
    
    amountInput.addEventListener('change', function() {
        const amount = parseInt(this.value);
        const minAmount = parseInt(minAmountInput.value);
        
        if (minAmount > amount) {
            minAmountInput.value = amount;
        }
        minAmountInput.max = amount;
    });

    minAmountInput.addEventListener('change', function() {
        const minAmount = parseInt(this.value);
        const amount = parseInt(amountInput.value);
        
        if (minAmount > amount) {
            this.value = amount;
            showAlert('⚠️ 最少创建数量不能超过创建数量', 'warning');
        }
    });
}

// 表单提交处理
function setupFormSubmission() {
    document.getElementById('ecsForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const data = getFormData('ecsForm');
        
        // 验证表单
        if (!validateCreateForm(data)) {
            return;
        }
        
        // 确认创建
        if (!data.dryRun) {
            const instanceText = data.amount > 1 ? `${data.amount} 台ECS实例` : '1 台ECS实例';
            const nameText = data.instanceNamePrefix ?
                (data.amount > 1 ? `名称前缀: ${data.instanceNamePrefix}` : `实例名称: ${data.instanceNamePrefix}`) :
                '使用默认名称';

            const details = `地域: ${data.regionId}\n规格: ${data.instanceType}\n镜像: ${data.imageId}\n${nameText}\n带宽: ${data.internetMaxBandwidthOut}Mbps`;

            const confirmed = await confirmAction({
                title: '确认创建ECS实例',
                message: `您即将创建 ${instanceText}，此操作将产生费用。`,
                details: details,
                confirmText: '确认创建',
                cancelText: '取消',
                type: 'primary'
            });

            if (!confirmed) {
                return;
            }
        }
        
        // 创建实例
        await createInstance(data);
    });
}

// 加载保存的配置
function loadSavedConfig() {
    const savedConfig = storage.get('lastCreateConfig');
    if (savedConfig) {
        // 恢复表单数据
        Object.keys(savedConfig).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = savedConfig[key];
                } else {
                    element.value = savedConfig[key];
                }
            }
        });
        
        showAlert('✅ 已恢复上次的配置', 'info');
    }
}

// 保存当前配置到本地存储
function saveCurrentConfig() {
    const data = getFormData('ecsForm');
    storage.set('lastCreateConfig', data);
}

// 自动保存配置
function setupAutoSave() {
    const form = document.getElementById('ecsForm');
    const inputs = form.querySelectorAll('input, select');
    
    inputs.forEach(input => {
        input.addEventListener('change', debounce(saveCurrentConfig, 1000));
    });
}

// 切换密码显示/隐藏
function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const button = input.nextElementSibling;

    if (input.type === 'password') {
        input.type = 'text';
        button.textContent = '🙈';
    } else {
        input.type = 'password';
        button.textContent = '👁️';
    }
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    setupAmountValidation();
    setupFormSubmission();
    setupAutoSave();
    loadSavedConfig();
    
    // 设置默认值
    const dryRunCheckbox = document.getElementById('dryRun');
    if (dryRunCheckbox) {
        dryRunCheckbox.checked = false;
    }
});
