// 设置页面JavaScript

// 切换密码显示/隐藏
function togglePassword() {
    const input = document.getElementById('accessKeySecret');
    const toggle = document.querySelector('.password-toggle');
    if (input.type === 'password') {
        input.type = 'text';
        toggle.textContent = '🙈';
    } else {
        input.type = 'password';
        toggle.textContent = '👁️';
    }
}

// 更新连接状态显示
function updateConnectionStatus(connected) {
    const status = document.getElementById('connectionStatus');
    const indicator = status.querySelector('.status-indicator');
    const text = status.querySelector('span:last-child');
    
    if (connected) {
        indicator.className = 'status-indicator status-connected';
        text.textContent = '已连接到阿里云';
    } else {
        indicator.className = 'status-indicator status-disconnected';
        text.textContent = '未连接到阿里云';
    }
}

// 加载当前设置
async function loadCurrentSettings() {
    try {
        const result = await apiRequest('/api/settings/load', {
            method: 'GET'
        });
        
        if (result.success) {
            const settings = result.settings || {};
            document.getElementById('accessKeyId').value = settings.accessKeyId || '';
            
            // 如果有密钥，显示占位符
            if (settings.accessKeySecret) {
                document.getElementById('accessKeySecret').placeholder = '已设置（点击显示按钮查看）';
            }
            
            document.getElementById('saveToFile').checked = settings.saveToFile !== false;
            
            updateConnectionStatus(settings.connected || false);
            updateCurrentSettingsDisplay(settings);
        }
    } catch (error) {
        console.error('加载设置失败:', error);
        showAlert('❌ 加载设置失败: ' + error.message, 'error');
    }
}

// 更新当前设置显示
function updateCurrentSettingsDisplay(settings) {
    const container = document.getElementById('currentSettings');
    let html = '<div style="line-height: 1.6;">';
    html += '<div><strong>Access Key ID:</strong> ' + (settings.accessKeyId ? settings.accessKeyId.substring(0, 8) + '...' : '未设置') + '</div>';
    html += '<div><strong>Access Key Secret:</strong> ' + (settings.accessKeySecret ? '已设置' : '未设置') + '</div>';
    html += '<div><strong>保存方式:</strong> ' + (settings.saveToFile ? '本地文件' : '仅会话') + '</div>';
    html += '<div><strong>连接状态:</strong> ' + (settings.connected ? '✅ 已连接' : '❌ 未连接') + '</div>';
    html += '</div>';
    container.innerHTML = html;
}

// 测试连接
async function testConnection() {
    const accessKeyId = document.getElementById('accessKeyId').value;
    const accessKeySecret = document.getElementById('accessKeySecret').value;
    
    if (!accessKeyId || !accessKeySecret) {
        showAlert('❌ 请先输入Access Key ID和Secret', 'error');
        return;
    }

    showLoading(true);
    try {
        // 先保存设置到内存
        await apiRequest('/api/settings/save', {
            method: 'POST',
            body: JSON.stringify({
                accessKeyId: accessKeyId,
                accessKeySecret: accessKeySecret,
                saveToFile: false // 测试时不保存到文件
            })
        });
        
        // 然后测试连接
        const result = await apiRequest('/api/instances?regionId=cn-hangzhou', {
            method: 'GET'
        });
        
        if (result.success) {
            updateConnectionStatus(true);
            showAlert('✅ 连接测试成功！', 'success');
        } else {
            updateConnectionStatus(false);
            showAlert('❌ 连接测试失败: ' + result.error, 'error');
        }
    } catch (error) {
        updateConnectionStatus(false);
        showAlert('❌ 连接测试失败: ' + error.message, 'error');
    }
    showLoading(false);
}

// 清除设置
async function clearSettings() {
    if (!confirm('确认要清除所有设置吗？此操作不可撤销。')) {
        return;
    }

    showLoading(true);
    try {
        const result = await apiRequest('/api/settings/save', {
            method: 'POST',
            body: JSON.stringify({
                accessKeyId: '',
                accessKeySecret: '',
                saveToFile: false,
                clear: true
            })
        });
        
        if (result.success) {
            document.getElementById('accessKeyId').value = '';
            document.getElementById('accessKeySecret').value = '';
            document.getElementById('accessKeySecret').placeholder = '请输入Access Key Secret';
            updateConnectionStatus(false);
            updateCurrentSettingsDisplay({});
            showAlert('✅ 设置已清除', 'success');
        } else {
            showAlert('❌ 清除设置失败: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('❌ 清除设置失败: ' + error.message, 'error');
    }
    showLoading(false);
}

// 保存设置
async function saveSettings(data) {
    showLoading(true);
    try {
        const result = await apiRequest('/api/settings/save', {
            method: 'POST',
            body: JSON.stringify(data)
        });
        
        if (result.success) {
            showAlert('✅ 设置保存成功！', 'success');
            loadCurrentSettings();
        } else {
            showAlert('❌ 保存设置失败: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('❌ 保存设置失败: ' + error.message, 'error');
    }
    showLoading(false);
}

// 验证设置表单
function validateSettingsForm(data) {
    if (!data.accessKeyId || !data.accessKeySecret) {
        showAlert('❌ 请输入完整的访问密钥信息', 'error');
        return false;
    }
    
    // 验证Access Key ID格式
    if (!data.accessKeyId.startsWith('LTAI')) {
        showAlert('⚠️ Access Key ID格式可能不正确，通常以LTAI开头', 'warning');
    }
    
    // 验证Access Key Secret长度
    if (data.accessKeySecret.length < 20) {
        showAlert('⚠️ Access Key Secret长度可能不正确', 'warning');
    }
    
    return true;
}

// 设置表单提交处理
function setupSettingsFormSubmission() {
    document.getElementById('settingsForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const data = getFormData('settingsForm');
        
        if (!validateSettingsForm(data)) {
            return;
        }
        
        await saveSettings(data);
    });
}

// 自动保存功能
function setupAutoSave() {
    const accessKeyIdInput = document.getElementById('accessKeyId');
    const saveToFileCheckbox = document.getElementById('saveToFile');
    
    // 保存Access Key ID的变化
    accessKeyIdInput.addEventListener('blur', function() {
        const value = this.value.trim();
        if (value) {
            storage.set('tempAccessKeyId', value);
        }
    });
    
    // 保存保存选项的变化
    saveToFileCheckbox.addEventListener('change', function() {
        storage.set('tempSaveToFile', this.checked);
    });
}

// 恢复临时保存的数据
function restoreTempData() {
    const tempAccessKeyId = storage.get('tempAccessKeyId');
    const tempSaveToFile = storage.get('tempSaveToFile');
    
    if (tempAccessKeyId) {
        document.getElementById('accessKeyId').value = tempAccessKeyId;
    }
    
    if (tempSaveToFile !== null) {
        document.getElementById('saveToFile').checked = tempSaveToFile;
    }
}

// 清除临时数据
function clearTempData() {
    storage.remove('tempAccessKeyId');
    storage.remove('tempSaveToFile');
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    setupSettingsFormSubmission();
    setupAutoSave();
    restoreTempData();
    loadCurrentSettings();
    
    // 成功保存后清除临时数据
    document.getElementById('settingsForm').addEventListener('submit', function() {
        setTimeout(clearTempData, 1000);
    });
});
