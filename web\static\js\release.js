// 释放实例页面JavaScript

let instancesData = [];
let dragSelectManager = null;

// 获取选中的实例ID
function getSelectedInstances() {
    const checkboxes = document.querySelectorAll('input[name="selectedInstances"]:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// 处理复选框变化
function handleCheckboxChange(checkbox) {
    // 更新选中计数
    updateSelectedCount();

    // 更新选中实例汇总
    updateSelectedSummary();

    // 同步到拖拽选择管理器
    if (dragSelectManager) {
        dragSelectManager.syncFromCheckboxes();

        // 更新最后选中的索引（用于Shift选择）
        const instanceCard = checkbox.closest('.instance-card');
        if (instanceCard) {
            const items = Array.from(document.querySelectorAll('.instance-card'));
            const index = items.indexOf(instanceCard);
            if (index !== -1 && checkbox.checked) {
                dragSelectManager.lastSelectedIndex = index;
            }
        }
    }
}

// 更新选中数量显示
function updateSelectedCount() {
    const selected = getSelectedInstances();
    const countElement = document.getElementById('selectedCount');
    if (countElement) {
        const countBadge = countElement.querySelector('.count-badge');
        if (countBadge) {
            countBadge.textContent = selected.length;
        } else {
            countElement.textContent = '已选择 ' + selected.length + ' 个实例';
        }
    }

    // 更新卡片选中样式
    selected.forEach(instanceId => {
        const card = document.querySelector(`[data-instance-id="${instanceId}"]`);
        if (card) {
            card.classList.add('selected');
        }
    });

    // 移除未选中卡片的选中样式
    document.querySelectorAll('.instance-card').forEach(card => {
        const instanceId = card.getAttribute('data-instance-id');
        if (!selected.includes(instanceId)) {
            card.classList.remove('selected');
        }
    });
}

// 加载实例列表
async function loadInstances() {
    const regionId = document.getElementById('regionId').value;
    if (!regionId) {
        showAlert('❌ 请先选择地域', 'error');
        return;
    }

    showLoading(true);
    try {
        const result = await apiRequest('/api/instances?regionId=' + encodeURIComponent(regionId), {
            method: 'GET'
        });
        
        if (result.success) {
            instancesData = result.data || [];
            renderInstancesList();
            document.getElementById('instancesContainer').style.display = 'block';
            showAlert('✅ 成功加载 ' + instancesData.length + ' 个实例', 'success');
        } else {
            showAlert('❌ 加载实例失败: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('❌ 加载实例请求失败: ' + error.message, 'error');
    }
    showLoading(false);
}

// 渲染实例列表
function renderInstancesList() {
    const container = document.getElementById('instancesList');
    if (!container) return;

    if (instancesData.length === 0) {
        container.innerHTML = '<div class="empty-state">当前地域没有找到实例</div>';
        return;
    }

    let html = '';
    instancesData.forEach(instance => {
        const statusClass = getStatusClass(instance.status);
        const protectionIcon = instance.deletionProtection ? '🛡️' : '❌';

        html += `
            <div class="instance-card"
                 data-instance-id="${instance.instanceId}"
                 onclick="handleCardClick(event, '${instance.instanceId}')">
                <div class="instance-select">
                    <input type="checkbox"
                           name="selectedInstances"
                           value="${instance.instanceId}"
                           id="instance-${instance.instanceId}"
                           onchange="handleCheckboxChange(this)"
                           onclick="event.stopPropagation()">
                </div>
                <div class="instance-header">
                    <div class="instance-info">
                        <div class="instance-name">${instance.instanceName || instance.hostName || '未命名实例'}</div>
                        <div class="instance-id">${instance.instanceId}</div>
                    </div>
                    <div class="instance-status ${statusClass}">${getStatusText(instance.status)}</div>
                </div>
                <div class="instance-details">
                    <div class="detail-item">
                        <div class="detail-label">实例规格</div>
                        <div class="detail-value">${instance.instanceType}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">CPU/内存</div>
                        <div class="detail-value">${instance.cpu}核/${instance.memory}MB</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">删除保护</div>
                        <div class="detail-value">${protectionIcon} ${instance.deletionProtection ? '已启用' : '未启用'}</div>
                    </div>
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
    updateSelectedCount();
    updateSelectedSummary();

    // 初始化拖拽选择功能
    initDragSelect();
}

// 获取状态样式类
function getStatusClass(status) {
    const statusMap = {
        'Running': 'status-running',
        'Stopped': 'status-stopped',
        'Starting': 'status-starting',
        'Stopping': 'status-stopping'
    };
    return statusMap[status] || 'status-stopped';
}

// 获取状态文本
function getStatusText(status) {
    const statusMap = {
        'Running': '运行中',
        'Stopped': '已停止',
        'Starting': '启动中',
        'Stopping': '停止中'
    };
    return statusMap[status] || status;
}

// 获取状态颜色（保持向后兼容）
function getStatusColor(status) {
    switch (status) {
        case 'Running': return '#28a745';
        case 'Stopped': return '#6c757d';
        case 'Starting': return '#17a2b8';
        case 'Stopping': return '#ffc107';
        default: return '#dc3545';
    }
}

// 初始化拖拽选择功能
function initDragSelect() {
    // 检查是否支持拖拽选择
    if (typeof DragSelectManager === 'undefined') {
        console.warn('DragSelectManager 未加载，使用传统选择模式');
        return;
    }

    // 销毁之前的实例
    if (dragSelectManager) {
        dragSelectManager.destroy();
    }

    try {
        // 创建新的拖拽选择管理器
        dragSelectManager = new DragSelectManager({
            containerSelector: '.instances-list-container',
            itemSelector: '.instance-card',
            checkboxSelector: 'input[name="selectedInstances"]'
        });

        // 初始化
        if (dragSelectManager.init()) {
            // 监听选择变化事件
            const container = document.querySelector('.instances-list-container');
            container.addEventListener('dragselect:change', function(e) {
                console.log('拖拽选择变化:', e.detail);
                // updateSelectedCount 会在 onSelectionChange 中自动调用

                // 显示选择统计
                const stats = dragSelectManager.getSelectionStats();
                if (stats.selected > 0) {
                    console.log(`已选择 ${stats.selected}/${stats.total} 个实例 (${stats.percentage}%)`);
                }
            });

            // 从现有复选框同步状态
            dragSelectManager.syncFromCheckboxes();

            console.log('拖拽选择功能已启用');
        } else {
            console.warn('拖拽选择功能初始化失败，回退到传统模式');
            dragSelectManager = null;
        }
    } catch (error) {
        console.error('拖拽选择功能初始化出错:', error);
        dragSelectManager = null;
    }
}

// 全选
function selectAll() {
    const checkboxes = document.querySelectorAll('input[name="selectedInstances"]');
    checkboxes.forEach(cb => cb.checked = true);

    // 同步到拖拽选择管理器
    if (dragSelectManager) {
        dragSelectManager.syncFromCheckboxes();
    }

    updateSelectedCount();
}

// 取消全选
function selectNone() {
    const checkboxes = document.querySelectorAll('input[name="selectedInstances"]');
    checkboxes.forEach(cb => cb.checked = false);

    // 同步到拖拽选择管理器
    if (dragSelectManager) {
        dragSelectManager.syncFromCheckboxes();
    }

    updateSelectedCount();
}

// 获取释放表单数据
function getReleaseFormData() {
    const data = getFormData('releaseForm');

    // 手动获取regionId（因为它在左侧面板，不在releaseForm表单内）
    const regionIdElement = document.getElementById('regionId');
    if (regionIdElement) {
        data.regionId = regionIdElement.value;
    }

    // 获取选中的实例ID
    const selectedInstances = getSelectedInstances();
    if (selectedInstances.length > 0) {
        data.instanceIds = selectedInstances.join(',');
    }

    // 获取手动输入的实例ID和实例名称
    // const manualInstanceIds = document.getElementById('instanceIds').value.trim();
    const instanceNamePattern = document.getElementById('instanceName').value.trim();

    // if (manualInstanceIds) {
    //     // 如果已经有选中的实例，则合并
    //     if (data.instanceIds) {
    //         data.instanceIds += ',' + manualInstanceIds;
    //     } else {
    //         data.instanceIds = manualInstanceIds;
    //     }
    // }

    if (instanceNamePattern) {
        data.instanceName = instanceNamePattern;
    }

    return data;
}

// 验证释放表单
function validateReleaseForm(data) {
    const selectedInstances = getSelectedInstances();
    if (selectedInstances.length === 0 && !data.instanceIds && !data.instanceName) {
        showAlert('❌ 请选择要释放的实例，或手动输入实例ID/名称', 'error');
        return false;
    }
    return true;
}

// 查询实例（预检模式）
async function queryInstances() {
    showLoading(true);
    try {
        const data = getReleaseFormData();
        if (!validateReleaseForm(data)) {
            showLoading(false);
            return;
        }
        
        // 强制设置为预检模式
        data.dryRun = true;
        
        const result = await apiRequest('/api/release', {
            method: 'POST',
            body: JSON.stringify(data)
        });
        
        if (result.success) {
            showAlert('✅ 实例查询完成！请查看控制台输出了解详细信息', 'success');
        } else {
            showAlert('❌ 查询失败: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('❌ 查询请求失败: ' + error.message, 'error');
    }
    showLoading(false);
}

// 释放实例
async function releaseInstances(data) {
    showLoading(true);
    try {
        const result = await apiRequest('/api/release', {
            method: 'POST',
            body: JSON.stringify(data)
        });
        
        if (result.success) {
            if (data.dryRun) {
                showAlert('✅ 预检验证通过！您可以取消预检模式来实际释放实例', 'success');
            } else {
                showAlert('✅ ECS实例释放成功！', 'success');
                // 清空选择
                selectNone();
                // 重新加载实例列表
                if (instancesData.length > 0) {
                    setTimeout(() => loadInstances(), 2000);
                }
            }
        } else {
            showAlert('❌ 释放失败: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('❌ 释放请求失败: ' + error.message, 'error');
    }
    showLoading(false);
}

// 设置表单提交处理
function setupReleaseFormSubmission() {
    document.getElementById('releaseForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const data = getReleaseFormData();
        if (!validateReleaseForm(data)) {
            return;
        }
        
        // 确认操作
        if (!data.dryRun) {
            const selectedInstances = getSelectedInstances();
            const instanceInfo = selectedInstances.length > 0
                ? `选中的 ${selectedInstances.length} 个实例`
                : (data.instanceIds || '通过名称匹配的实例');

            const details = `实例: ${instanceInfo}\n删除保护: ${data.deleteProtected ? '是' : '否'}\n强制删除: ${data.force ? '是' : '否'}`;

            const confirmed = await confirmAction({
                title: '⚠️ 确认释放ECS实例',
                message: '您即将释放ECS实例，此操作不可逆！实例释放后将无法恢复，请确认操作。',
                details: details,
                confirmText: '确认释放',
                cancelText: '取消',
                type: 'danger'
            });

            if (!confirmed) {
                return;
            }
        }
        
        await releaseInstances(data);
    });
}

// 处理卡片点击事件
function handleCardClick(event, instanceId) {
    event.stopPropagation();
    if (event.target.type === 'checkbox') return;

    const checkbox = document.getElementById(`instance-${instanceId}`);
    if (checkbox) {
        checkbox.checked = !checkbox.checked;
        handleCheckboxChange(checkbox);
    }

    // 添加点击动画效果
    const card = event.currentTarget;
    card.style.transform = 'scale(0.98)';
    setTimeout(() => {
        card.style.transform = '';
    }, 150);
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('input[name="selectedInstances"]');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateSelectedCount();
    updateSelectedSummary();

    // 同步到拖拽选择管理器
    if (dragSelectManager) {
        dragSelectManager.syncFromCheckboxes();
    }
}

// 更新选中实例汇总信息
function updateSelectedSummary() {
    const selectedIds = getSelectedInstances();
    const summaryContainer = document.getElementById('selectedSummary');
    const summaryContent = document.getElementById('summaryContent');

    if (selectedIds.length === 0) {
        summaryContainer.style.display = 'none';
        return;
    }

    const selectedInstances = instancesData.filter(instance =>
        selectedIds.includes(instance.instanceId)
    );

    let html = `
        <div class="summary-stats">
            <div class="stat-item">
                <span class="stat-label">选中实例:</span>
                <span class="stat-value">${selectedInstances.length} 个</span>
            </div>
    `;

    // 统计状态分布
    const statusCounts = {};
    selectedInstances.forEach(instance => {
        statusCounts[instance.status] = (statusCounts[instance.status] || 0) + 1;
    });

    Object.entries(statusCounts).forEach(([status, count]) => {
        html += `
            <div class="stat-item">
                <span class="stat-label">${getStatusText(status)}:</span>
                <span class="stat-value">${count} 个</span>
            </div>
        `;
    });

    // 检查删除保护
    const protectedInstances = selectedInstances.filter(instance => instance.deletionProtection);
    if (protectedInstances.length > 0) {
        html += `
            <div class="stat-item warning">
                <span class="stat-label">🛡️ 有删除保护:</span>
                <span class="stat-value">${protectedInstances.length} 个</span>
            </div>
        `;
    }

    html += '</div>';

    summaryContent.innerHTML = html;
    summaryContainer.style.display = 'block';
}

// 通配符匹配相关变量
let matchedInstancesByName = [];
let nameMatchTimeout = null;

// 处理实例名称输入
function handleInstanceNameInput() {
    const input = document.getElementById('instanceName');
    const pattern = input.value.trim();

    // 清除之前的定时器
    if (nameMatchTimeout) {
        clearTimeout(nameMatchTimeout);
    }

    // 如果输入为空，隐藏预览
    if (!pattern) {
        hideNameMatchPreview();
        return;
    }

    // 延迟执行匹配，避免频繁计算
    nameMatchTimeout = setTimeout(() => {
        performNameMatching(pattern);
    }, 300);
}

// 处理实例名称输入失焦
function handleInstanceNameBlur() {
    // 延迟隐藏预览，给用户时间点击预览中的内容
    setTimeout(() => {
        const preview = document.getElementById('nameMatchPreview');
        const confirm = document.getElementById('nameMatchConfirm');
        if (preview && !preview.matches(':hover') && confirm && !confirm.matches(':hover')) {
            // 如果鼠标不在预览区域内，则隐藏
            // hideNameMatchPreview();
        }
    }, 200);
}

// 设置实例名称模式
function setInstanceNamePattern(pattern) {
    const input = document.getElementById('instanceName');
    input.value = pattern;
    input.focus();
    handleInstanceNameInput();
}

// 执行名称匹配
function performNameMatching(pattern) {
    if (!instancesData || instancesData.length === 0) {
        showNameMatchError('请先查询实例列表');
        return;
    }

    // 验证通配符模式
    if (!validateWildcardPattern(pattern)) {
        showNameMatchError('无效的通配符模式');
        return;
    }

    // 执行匹配
    const matches = matchInstancesByName(pattern);

    if (matches.length === 0) {
        showNameMatchError('没有匹配到任何实例');
        return;
    }

    // 显示匹配结果
    showNameMatchPreview(matches, pattern);
    matchedInstancesByName = matches;
}

// 验证通配符模式
function validateWildcardPattern(pattern) {
    // 基本验证：不能只包含通配符
    if (/^[\*\?]+$/.test(pattern)) {
        return false;
    }

    // 检查是否包含无效字符
    if (/[<>:"/\\|]/.test(pattern)) {
        return false;
    }

    return true;
}

// 通过名称匹配实例
function matchInstancesByName(pattern) {
    const regex = createWildcardRegex(pattern);

    return instancesData.filter(instance => {
        const instanceName = instance.instanceName || instance.hostName || '';
        return regex.test(instanceName);
    });
}

// 创建通配符正则表达式
function createWildcardRegex(pattern) {
    // 转义特殊字符，但保留 * 和 ?
    let regexPattern = pattern
        .replace(/[.+^${}()|[\]\\]/g, '\\$&') // 转义正则特殊字符
        .replace(/\*/g, '.*')                  // * 替换为 .*
        .replace(/\?/g, '.');                  // ? 替换为 .

    return new RegExp('^' + regexPattern + '$', 'i'); // 不区分大小写
}

// 显示名称匹配预览
function showNameMatchPreview(matches, pattern) {
    const preview = document.getElementById('nameMatchPreview');
    const matchCount = document.getElementById('matchCount');
    const matchedInstances = document.getElementById('matchedInstances');

    // 更新匹配数量
    matchCount.textContent = `${matches.length} 个匹配`;

    // 生成匹配实例列表
    let html = '';
    matches.forEach(instance => {
        const instanceName = instance.instanceName || instance.hostName || '未命名';
        const highlightedName = highlightMatchedText(instanceName, pattern);
        const statusClass = getStatusClass(instance.status);
        const statusText = getStatusText(instance.status);

        let warningText = '';
        if (instance.deletionProtection) {
            warningText += '🛡️ 删除保护 ';
        }
        if (instance.status === 'Running') {
            warningText += '⚡ 运行中';
        }

        html += `
            <div class="matched-instance-item">
                <div>
                    <div class="matched-instance-name">${highlightedName}</div>
                    <div class="matched-instance-id">${instance.instanceId}</div>
                </div>
                <div>
                    <div class="matched-instance-status ${statusClass}">${statusText}</div>
                    ${warningText ? `<div class="matched-instance-warning">${warningText}</div>` : ''}
                </div>
            </div>
        `;
    });

    matchedInstances.innerHTML = html;
    preview.style.display = 'block';

    // 如果匹配数量较多或包含风险实例，显示确认区域
    if (matches.length > 1 || hasRiskyInstances(matches)) {
        showNameMatchConfirm(matches);
    } else {
        hideNameMatchConfirm();
    }
}

// 高亮匹配的文本
function highlightMatchedText(text, pattern) {
    const regex = createWildcardRegex(pattern);

    // 简单的高亮实现，实际匹配整个文本
    if (regex.test(text)) {
        // 这里可以实现更复杂的部分高亮逻辑
        return `<span class="highlight-match">${text}</span>`;
    }

    return text;
}

// 检查是否包含风险实例
function hasRiskyInstances(instances) {
    return instances.some(instance =>
        instance.deletionProtection || instance.status === 'Running'
    );
}

// 显示名称匹配确认
function showNameMatchConfirm(matches) {
    const confirm = document.getElementById('nameMatchConfirm');
    const confirmContent = document.getElementById('confirmContent');

    const riskyCount = matches.filter(instance =>
        instance.deletionProtection || instance.status === 'Running'
    ).length;

    let html = `
        <div>即将选择 <strong>${matches.length}</strong> 个实例进行释放操作。</div>
    `;

    if (riskyCount > 0) {
        html += `
            <div style="margin-top: 8px; color: #dc3545;">
                <strong>⚠️ 警告：</strong>其中 <strong>${riskyCount}</strong> 个实例有删除保护或正在运行中！
            </div>
        `;
    }

    confirmContent.innerHTML = html;
    confirm.style.display = 'block';
}

// 隐藏名称匹配确认
function hideNameMatchConfirm() {
    const confirm = document.getElementById('nameMatchConfirm');
    confirm.style.display = 'none';
}

// 显示名称匹配错误
function showNameMatchError(message) {
    const preview = document.getElementById('nameMatchPreview');
    const matchCount = document.getElementById('matchCount');
    const matchedInstances = document.getElementById('matchedInstances');

    matchCount.textContent = '0 个匹配';
    matchedInstances.innerHTML = `
        <div style="text-align: center; padding: 20px; color: #6c757d;">
            <div style="font-size: 2em; margin-bottom: 10px;">🔍</div>
            <div>${message}</div>
        </div>
    `;

    preview.style.display = 'block';
    hideNameMatchConfirm();
}

// 隐藏名称匹配预览
function hideNameMatchPreview() {
    const preview = document.getElementById('nameMatchPreview');
    preview.style.display = 'none';
    hideNameMatchConfirm();
    matchedInstancesByName = [];
}

// 取消名称匹配
function cancelNameMatch() {
    hideNameMatchPreview();
    document.getElementById('instanceName').value = '';
}

// 确认名称匹配
function confirmNameMatch() {
    if (matchedInstancesByName.length === 0) {
        return;
    }

    // 选中匹配的实例
    const checkboxes = document.querySelectorAll('input[name="selectedInstances"]');
    checkboxes.forEach(checkbox => {
        const instanceId = checkbox.value;
        const isMatched = matchedInstancesByName.some(instance => instance.instanceId === instanceId);
        checkbox.checked = isMatched;
    });

    // 更新选中状态
    updateSelectedCount();
    updateSelectedSummary();

    // 同步到拖拽选择管理器
    if (dragSelectManager) {
        dragSelectManager.syncFromCheckboxes();
    }

    // 隐藏预览
    hideNameMatchPreview();

    // 显示成功提示
    showAlert(`✅ 已选择 ${matchedInstancesByName.length} 个匹配的实例`, 'success');
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    setupReleaseFormSubmission();

    // 设置默认值
    const dryRunCheckbox = document.getElementById('dryRun');
    if (dryRunCheckbox) {
        dryRunCheckbox.checked = false;
    }

    // 自动加载默认地域的实例列表
    const regionSelect = document.getElementById('regionId');
    if (regionSelect && regionSelect.value) {
        loadInstances();
    }
});
