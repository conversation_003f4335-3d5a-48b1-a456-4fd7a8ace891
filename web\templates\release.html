{{template "header" .}}


        <!-- 主要内容区域 - 左右分栏布局 -->
        <div class="main-content">
            <!-- 左侧区域：实例列表 -->
            <div class="left-panel">
                <div class="card">
                    <h2 class="section-title">🎯 实例选择</h2>

                    <!-- 基本配置 -->
                    <div class="control-panel">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="regionId">地域ID *</label>
                                <select id="regionId" name="regionId" required>
                                    <option value="cn-hongkong">香港</option>
                                    <option value="cn-hangzhou">杭州</option>
                                    <option value="cn-shanghai">上海</option>
                                    <option value="cn-beijing">北京</option>
                                    <option value="cn-shenzhen">深圳</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <button type="button" class="btn btn-primary" onclick="loadInstances()">🔍 查询实例列表</button>
                            </div>
                            <div class="form-group">
                                <label for="instanceName">实例名称模式（可选）</label>
                                <input type="text" id="instanceName" name="instanceName" placeholder="web-*-prod"
                                       oninput="handleInstanceNameInput()" onblur="handleInstanceNameBlur()">
    
                                <!-- 通配符说明 -->
                                <div class="wildcard-help">
                                    <small style="color: #666;">
                                        <strong>通配符支持：</strong>
                                        <code>*</code> 匹配任意字符序列，
                                        <code>?</code> 匹配单个字符
                                    </small>
                                    <div class="wildcard-examples">
                                        <button type="button" class="example-btn" onclick="setInstanceNamePattern('web-*')">web-*</button>
                                        <button type="button" class="example-btn" onclick="setInstanceNamePattern('*-prod')">*-prod</button>
                                        <button type="button" class="example-btn" onclick="setInstanceNamePattern('test-?')">test-?</button>
                                        <button type="button" class="example-btn" onclick="setInstanceNamePattern('*-prod-*')">*-prod-*</button>
                                    </div>
                                </div>
    
                                <!-- 实时匹配预览 -->
                                <div id="nameMatchPreview" class="name-match-preview" style="display: none;">
                                    <div class="preview-header">
                                        <span class="preview-title">匹配预览</span>
                                        <span id="matchCount" class="match-count">0 个匹配</span>
                                    </div>
                                    <div id="matchedInstances" class="matched-instances">
                                        <!-- 匹配的实例将在这里显示 -->
                                    </div>
                                </div>
    
                                <!-- 匹配确认区域 -->
                                <div id="nameMatchConfirm" class="name-match-confirm" style="display: none;">
                                    <div class="confirm-header">
                                        <strong>⚠️ 批量释放确认</strong>
                                    </div>
                                    <div id="confirmContent" class="confirm-content">
                                        <!-- 确认信息将在这里显示 -->
                                    </div>
                                    <div class="confirm-actions">
                                        <button type="button" class="btn btn-secondary btn-sm" onclick="cancelNameMatch()">取消</button>
                                        <button type="button" class="btn btn-warning btn-sm" onclick="confirmNameMatch()">确认选择</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 实例列表 -->
                    <div id="instancesContainer" style="display: none;">
                        <div class="instances-header">
                            <div class="batch-actions">
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                <label for="selectAll">全选</label>
                                <span id="selectedCount">已选择 0 个实例</span>
                            </div>
                        </div>

                        <div class="instances-list-container" id="instancesListContainer">
                            <div class="drag-select-status">拖拽选择中...</div>
                            <div id="instancesList" class="instances-list compact-view">
                                <!-- 实例列表将在这里动态生成 -->
                            </div>
                        </div>
                    </div>

                
                </div>
            </div>

            <!-- 右侧区域：释放操作面板 -->
            <div class="right-panel">
                <div class="card">
                    <h2 class="section-title">⚙️ 释放操作</h2>

                    <form id="releaseForm">
                        <!-- 选中实例汇总 -->
                        <div id="selectedSummary" class="selected-summary" style="display: none;">
                            <h3>选中实例汇总</h3>
                            <div id="summaryContent">
                                <!-- 汇总信息将在这里显示 -->
                            </div>
                        </div>

                        <!-- 释放选项 -->
                        <div class="release-options">
                            <h3>释放选项</h3>
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="deleteProtected" name="deleteProtected">
                                    <label for="deleteProtected">删除有保护的实例</label>
                                </div>
                                <small style="color: #666;">自动取消删除保护并删除实例</small>
                            </div>

                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="force" name="force" checked>
                                    <label for="force">强制删除运行中的实例</label>
                                </div>
                                <small style="color: #666;">强制删除正在运行的实例</small>
                            </div>

                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="dryRun" name="dryRun">
                                    <label for="dryRun">预检模式 (只查询实例，不实际删除)</label>
                                </div>
                                <small style="color: #666;">建议首次使用时启用预检模式</small>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="action-buttons">
                            <!-- <button type="button" class="btn btn-warning" onclick="queryInstances()">🔍 查询实例</button> -->
                            <button type="submit" class="btn btn-danger">🗑️ 释放实例</button>
                        </div>

                        <!-- 操作结果显示区域 -->
                        <div id="operationResults" class="operation-results" style="display: none;">
                            <h3>操作结果</h3>
                            <div id="resultsContent">
                                <!-- 操作结果将在这里显示 -->
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="loading" id="loading">
            <div class="spinner-container">
                <div class="spinner"></div>
                <div class="loading-text">正在处理请求，请稍候...</div>
            </div>
        </div>
{{template "footer" .}}
