{{template "header" .}}

        <!-- 主要内容区域 - 左右分栏布局 -->
        <div class="main-content">
            <!-- 左侧区域：实例列表 -->
            <div class="left-panel">
                <div class="card">
                    <h2 class="section-title">实例管理</h2>

                    <!-- 控制面板 -->
                    <div class="control-panel">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="regionId">地域ID *</label>
                                <select id="regionId" name="regionId" required>
                                    <option value="cn-hongkong">香港</option>
                                    <option value="cn-hangzhou">杭州</option>
                                    <option value="cn-shanghai">上海</option>
                                    <option value="cn-beijing">北京</option>
                                    <option value="cn-shenzhen">深圳</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <button type="button" class="btn btn-primary" onclick="loadInstances()">查询实例列表</button>
                            </div>
                            <div class="form-group">
                                <button type="button" class="btn btn-success" onclick="openCreateInstanceModal()">
                                    创建实例</button>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="instanceName">实例名称模式（可选）</label>
                                <input type="text" id="instanceName" name="instanceName" placeholder="web-*-prod"
                                       oninput="handleInstanceNameInput()" onblur="handleInstanceNameBlur()">

                                <!-- 通配符说明 -->
                                <!-- <div class="wildcard-help">
                                    <small style="color: #666;">
                                        <strong>通配符支持：</strong>
                                        <code>*</code> 匹配任意字符序列，
                                        <code>?</code> 匹配单个字符
                                    </small>
                                    <div class="wildcard-examples">
                                        <button type="button" class="example-btn" onclick="setInstanceNamePattern('web-*')">web-*</button>
                                        <button type="button" class="example-btn" onclick="setInstanceNamePattern('*-prod')">*-prod</button>
                                        <button type="button" class="example-btn" onclick="setInstanceNamePattern('test-?')">test-?</button>
                                        <button type="button" class="example-btn" onclick="setInstanceNamePattern('*-prod-*')">*-prod-*</button>
                                    </div>
                                </div> -->

                                <!-- 实时匹配预览 -->
                                <div id="nameMatchPreview" class="name-match-preview" style="display: none;">
                                    <div class="preview-header">
                                        <span class="preview-title">匹配预览</span>
                                        <span id="matchCount" class="match-count">0 个匹配</span>
                                    </div>
                                    <div id="matchedInstances" class="matched-instances">
                                        <!-- 匹配的实例将在这里显示 -->
                                    </div>
                                </div>

                                <!-- 匹配确认区域 -->
                                <div id="nameMatchConfirm" class="name-match-confirm" style="display: none;">
                                    <div class="confirm-header">
                                        <strong>⚡ 批量选择确认</strong>
                                    </div>
                                    <div id="confirmContent" class="confirm-content">
                                        <!-- 确认信息将在这里显示 -->
                                    </div>
                                    <div class="confirm-actions">
                                        <button type="button" class="btn btn-secondary btn-sm" onclick="cancelNameMatch()">取消</button>
                                        <button type="button" class="btn btn-primary btn-sm" onclick="confirmNameMatch()">确认选择</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 筛选器 -->
                        <div class="filter-panel">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="statusFilter">状态筛选</label>
                                    <select id="statusFilter" onchange="filterInstances()">
                                        <option value="">全部状态</option>
                                        <option value="Running">运行中</option>
                                        <option value="Stopped">已停止</option>
                                        <option value="Starting">启动中</option>
                                        <option value="Stopping">停止中</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="searchInput">搜索实例</label>
                                    <input type="text" id="searchInput" placeholder="输入实例名称或ID"
                                        oninput="filterInstances()">
                                </div>
                            </div>
                        </div>

                        <!-- 实例名称模式选择 -->
                        <div class="name-pattern-panel">
                            <div class="form-group">
                                <label for="instanceNamePattern">实例名称模式选择</label>
                                <input type="text" id="instanceNamePattern" placeholder="web-*-prod"
                                       oninput="handleInstanceNamePatternInput()" onblur="handleInstanceNamePatternBlur()">

                                <!-- 通配符说明 -->
                                <!-- <div class="wildcard-help">
                                    <small style="color: #666;">
                                        <strong>通配符支持：</strong>
                                        <code>*</code> 匹配任意字符序列，
                                        <code>?</code> 匹配单个字符
                                    </small>
                                    <div class="wildcard-examples">
                                        <button type="button" class="example-btn" onclick="setInstanceNamePattern('web-*')">web-*</button>
                                        <button type="button" class="example-btn" onclick="setInstanceNamePattern('*-prod')">*-prod</button>
                                        <button type="button" class="example-btn" onclick="setInstanceNamePattern('test-?')">test-?</button>
                                        <button type="button" class="example-btn" onclick="setInstanceNamePattern('*-prod-*')">*-prod-*</button>
                                    </div>
                                </div> -->

                                <!-- 实时匹配预览 -->
                                <div id="instanceNameMatchPreview" class="name-match-preview" style="display: none;">
                                    <div class="preview-header">
                                        <span class="preview-title">匹配预览</span>
                                        <span id="instanceMatchCount" class="match-count">0 个匹配</span>
                                    </div>
                                    <div id="instanceMatchedList" class="matched-instances">
                                        <!-- 匹配的实例将在这里显示 -->
                                    </div>
                                </div>

                                <!-- 匹配确认区域 -->
                                <div id="instanceNameMatchConfirm" class="name-match-confirm" style="display: none;">
                                    <div class="confirm-header">
                                        <strong>⚡ 批量选择确认</strong>
                                    </div>
                                    <div id="instanceConfirmContent" class="confirm-content">
                                        <!-- 确认信息将在这里显示 -->
                                    </div>
                                    <div class="confirm-actions">
                                        <button type="button" class="btn btn-secondary btn-sm" onclick="cancelInstanceNameMatch()">取消</button>
                                        <button type="button" class="btn btn-primary btn-sm" onclick="confirmInstanceNameMatch()">确认选择</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 实例列表 -->
                    <div id="instancesContainer" style="display: none;">
                        <div class="instances-header">
                            <div class="batch-actions">
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                <label for="selectAll">全选</label>
                                <span id="selectedCount">已选择 0 个实例</span>
                            </div>
                        </div>

                        <div class="instances-list-container" id="instancesListContainer">
                            <div class="drag-select-status">拖拽选择中...</div>
                            <div id="instancesList" class="instances-list compact-view">
                                <!-- 实例列表将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧区域：实例操作 -->
            <div class="right-panel">
                <div class="card">
                    <h2 class="section-title">⚙️ 实例操作</h2>

                    <!-- 释放实例操作 -->
                    <div class="operation-section">
                        <h3 class="operation-title collapsible" onclick="toggleOperationSection(this)">
                            <span class="collapse-icon">▶</span>
                            🗑️ 释放实例
                        </h3>
                        <div class="operation-content" style="display: none;">
                        <!-- <div class="warning-box">
                            <strong>⚠️ 警告：</strong>释放实例是不可逆操作，实例释放后数据将无法恢复。请确保已备份重要数据。
                        </div> -->

                        <div class="release-options">
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="forceRelease" name="forceRelease" checked>
                                    强制释放（忽略删除保护）
                                </label>
                                <small class="form-help">启用后将忽略实例的删除保护设置</small>
                            </div>

                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="releaseEip" name="releaseEip" checked>
                                    同时释放绑定的弹性IP
                                </label>
                                <small class="form-help">建议同时释放以避免产生额外费用</small>
                            </div>
                        </div>

                        <div class="release-actions">
                            <button type="button" class="btn btn-danger" onclick="releaseSelectedInstances()" id="releaseBtn" disabled>
                                🗑️ 释放选中的实例
                            </button>
                        </div>

                        <div id="selectedSummary" class="selected-summary" style="display: none;">
                            <!-- 选中实例的摘要信息 -->
                        </div>
                        </div>
                    </div>

            
                    <!-- Shell命令执行 -->
                    <div class="operation-section">
                        <h3 class="operation-title collapsible" onclick="toggleOperationSection(this)">
                            <span class="collapse-icon">▶</span>
                            💻 Shell命令执行
                        </h3>
                        <div class="operation-content" style="display: none;">

                        <div class="command-panel">
                            <!-- 脚本文件上传区域 -->
                            <div class="script-upload-section">
                                <h4>📁 脚本文件上传</h4>

                                <!-- 拖拽上传区域 -->
                                <div class="upload-drop-zone" id="uploadDropZone">
                                    <div class="upload-drop-content">
                                        <div class="upload-icon">📄</div>
                                        <div class="upload-text">
                                            <p><strong>拖拽脚本文件到此处</strong></p>
                                            <p>或者 <button type="button" class="btn-link" onclick="document.getElementById('scriptFileInput').click()">点击选择文件</button></p>
                                            <small>支持格式：.sh, .py, .ps1, .bat, .js, .pl (最大10MB)</small>
                                        </div>
                                    </div>
                                    <input type="file" id="scriptFileInput" accept=".sh,.py,.ps1,.bat,.js,.pl,.txt" style="display: none;" onchange="handleFileSelect(event)">
                                </div>

                                <!-- 上传进度 -->
                                <div class="upload-progress" id="uploadProgress" style="display: none;">
                                    <div class="progress-bar">
                                        <div class="progress-fill" id="progressFill"></div>
                                    </div>
                                    <div class="progress-text" id="progressText">上传中... 0%</div>
                                </div>

                                <!-- 文件信息显示 -->
                                <div class="file-info" id="fileInfo" style="display: none;">
                                    <div class="file-details">
                                        <div class="file-icon" id="fileIcon">📄</div>
                                        <div class="file-meta">
                                            <div class="file-name" id="fileName"></div>
                                            <div class="file-size" id="fileSize"></div>
                                            <div class="file-type" id="fileType"></div>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-danger" onclick="clearUploadedFile()">❌</button>
                                    </div>
                                </div>

                                <!-- 脚本预览 -->
                                <div class="script-preview" id="scriptPreview" style="display: none;">
                                    <h5>脚本预览 (前100行)</h5>
                                    <pre id="scriptContent"></pre>
                                    <div class="preview-actions">
                                        <button type="button" class="btn btn-sm btn-secondary" onclick="toggleFullPreview()">查看完整内容</button>
                                    </div>
                                </div>

                                <!-- 执行参数 -->
                                <div class="script-params" id="scriptParams" style="display: none;">
                                    <div class="form-group">
                                        <label for="scriptArgs">执行参数 (可选)</label>
                                        <input type="text" id="scriptArgs" placeholder="例如: --verbose --output /tmp/result.txt">
                                        <small>为脚本添加命令行参数</small>
                                    </div>
                                </div>
                            </div>

                            <!-- 分隔线 -->
                            <div class="section-divider">
                                <span>或者</span>
                            </div>

                            <!-- 命令输入 -->
                            <div class="form-group">
                                <label for="commandInput">Shell命令 *</label>
                                <textarea id="commandInput" rows="3" placeholder="输入要执行的Shell命令..." required></textarea>
                            </div>

                            <!-- 执行按钮 -->
                            <div class="command-actions">
                                <button type="button" class="btn btn-success" id="uploadAndExecuteBtn" onclick="uploadAndExecuteScript()" style="display: none;">🚀 上传并执行脚本</button>
                                <button type="button" class="btn btn-warning" onclick="executeCommand()">▶️ 执行命令</button>
                                <button type="button" class="btn btn-secondary" onclick="clearCommand()">🗑️ 清空</button>
                            </div>
                        </div>
                        </div>
                         <!-- 命令执行结果 -->
                    <div id="commandResults" class="command-results" style="display: none;">
                        <h3>执行结果</h3>
                        <div id="resultsContainer">
                            <!-- 执行结果将在这里显示 -->
                        </div>
                    </div>

                    <!-- 命令历史 -->
                    <!-- <div id="commandHistory" class="command-history" style="display: none;">
                        <h3>命令历史</h3>
                        <div id="historyContainer">
                        </div>
                    </div> -->
                    </div>

                    <!-- HTTP文件下载 -->
                    <div class="operation-section">
                        <h3 class="operation-title collapsible" onclick="toggleOperationSection(this)">
                            <span class="collapse-icon">▶</span>
                            🌐 HTTP文件下载
                        </h3>
                        <div class="operation-content" style="display: none;">

                        <div class="download-panel">
                            <div class="http-download-info">
                                <h5>🎯 通配符下载示例</h5>
                                <ul>
                                    <li><code>/var/log/*.log</code> - 下载所有.log文件</li>
                                    <li><code>/etc/*.conf</code> - 下载所有.conf配置文件</li>
                                    <li><code>/home/<USER>/data*.txt</code> - 下载以data开头的.txt文件</li>
                                    <li><code>/tmp/backup_?.sql</code> - 下载单字符匹配的SQL文件</li>
                                </ul>
                                <!-- <div class="alert alert-info" style="margin-top: 10px; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 5px;">
                                    <strong>💡 通配符下载说明：</strong><br>
                                    • 匹配的文件将直接复制到HTTP服务器目录<br>
                                    • 系统会自动循环下载所有匹配的文件<br>
                                    • 每个文件单独下载，保持原始文件名<br>
                                    • <strong>⚠️ HTTP服务器将保持运行，请手动停止</strong>
                                </div> -->
                            </div>

                            <!-- HTTP服务器配置（隐藏） -->
                            <div class="form-group" style="display: none;">
                                <input type="number" id="httpServerPort" value="8000" min="1000" max="65535" />
                                <input type="text" id="httpServerDirectory" value="/tmp" placeholder="/tmp" />
                            </div>

                            <div id="httpServerStatus" class="status-info" style="display: none;">
                                <h4>HTTP服务器状态</h4>
                                <div id="httpServerInfo"></div>
                            </div>

                            <!-- 网络状态检查 -->
                            <div class="form-group">
                                <button onclick="checkNetworkStatus()" class="btn btn-info">🔍 检查选中实例网络状态</button>
                                <div id="networkStatusResult" class="status-info" style="display: none;"></div>
                            </div>

                            <div class="form-group">
                                <label for="downloadFilePath">要下载的文件路径:</label>
                                <input type="text" id="downloadFilePath" placeholder="例如: /etc/hostname 或 /var/log/*.log" required />
                                <!-- <div class="mt-2">
                                    <small class="text-muted">常用文件：</small>
                                    <button type="button" class="btn btn-sm btn-outline-secondary ml-1" onclick="setFilePath('/etc/hosts')">hosts</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary ml-1" onclick="setFilePath('/etc/hostname')">hostname</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary ml-1" onclick="setFilePath('/etc/passwd')">passwd</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary ml-1" onclick="setFilePath('/var/log/messages')">messages</button>
                                </div> -->
                                <small class="text-muted">支持单个文件路径或通配符模式（*、?、[]）</small>
                            </div>

                            <!-- <div class="form-group">
                                <button onclick="startHTTPServer()" class="btn btn-warning">🌐 启动HTTP服务器</button>
                                <button onclick="stopHTTPServer()" class="btn btn-secondary">⏹️ 停止HTTP服务器</button>
                            </div> -->

                            <!-- 统一下载功能 -->
                            <div class="form-group">
                                <button onclick="startUnifiedDownload()" class="btn btn-primary">🚀 批量下载</button>
                                <button onclick="checkDownloadService()" class="btn btn-info">🔍 检查服务状态</button>
                            </div>

                            <!-- <div class="form-group">
                                <small class="text-muted">
                                    💡 <strong>使用步骤</strong>：<br>
                                    1️⃣ 先点击"🌐 启动HTTP服务器"在ECS实例上启动文件服务器<br>
                                    2️⃣ 等待服务器启动完成（约5-10秒）<br>
                                    3️⃣ 再点击"🚀 批量下载"进行文件下载<br>
                                    4️⃣ 下载完成后可点击"⏹️ 停止HTTP服务器"<br>
                                    <br><strong>批量下载</strong>：通过服务器端绕过浏览器CORS限制，支持并发下载和自动重试。
                                    文件将保存到服务器的 <code>downloads/</code> 目录。
                                </small>
                            </div> -->

                            <!-- 批量下载进度 -->
                            <div id="batchHttpProgress" class="progress-container" style="display: none;">
                                <div class="progress-info">
                                    <div id="batchHttpProgressTitle" class="progress-filename">批量HTTP下载进度</div>
                                    <div id="batchHttpProgressStats" class="progress-stats"></div>
                                </div>
                                <div class="progress-bar-container">
                                    <div id="batchHttpProgressBar" class="progress-bar"></div>
                                </div>
                                <div id="batchHttpProgressDetails" class="progress-details"></div>
                            </div>
                        </div>
                        </div>
                    </div>


        <!-- 密码重置操作 -->
        <div class="operation-section">
            <h3 class="operation-title collapsible" onclick="toggleOperationSection(this)">
                <span class="collapse-icon">▶</span>
                🔐 批量重置密码
            </h3>
            <div class="operation-content" style="display: none;">

            <div class="password-panel">
                <div class="form-group">
                    <label for="passwordUsername">用户名</label>
                    <input type="text" id="passwordUsername" placeholder="默认为 root" value="root">
                    <small>要重置密码的用户名，默认为root</small>
                </div>

                <div class="form-group">
                    <label for="passwordInput">新密码 *</label>
                    <div class="password-input-container">
                        <input type="password" id="passwordInput" placeholder="输入新密码" required>
                        <button type="button" class="password-toggle" onclick="togglePasswordVisibility('passwordInput')">👁️</button>
                    </div>
                    <small>密码要求：8-30位，包含大小写字母、数字、特殊字符中的至少3种</small>
                </div>

                <div class="form-group">
                    <label for="confirmPassword">确认密码 *</label>
                    <div class="password-input-container">
                        <input type="password" id="confirmPassword" placeholder="再次输入密码" required>
                        <button type="button" class="password-toggle" onclick="togglePasswordVisibility('confirmPassword')">👁️</button>
                    </div>
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="createNewUser" onchange="toggleCreateUser()">
                        创建新用户（如果用户不存在）
                    </label>
                    <small>启用后，如果用户不存在将自动创建并添加sudo权限</small>
                </div>

                <div class="password-actions">
                    <button type="button" class="btn btn-primary" onclick="setPasswordForSelected()" id="setPasswordBtn" disabled>
                        🔐 重置密码
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="clearPasswordForm()">🗑️ 清空</button>
                </div>
            </div>
            </div>
        </div>

                   
                </div>
            </div>
        </div>

        <div class="loading" id="loading">
            <div class="spinner-container">
                <div class="spinner"></div>
                <div class="loading-text">正在处理请求，请稍候...</div>
            </div>
        </div>

        <!-- 创建实例模态框 -->
        <div id="createInstanceModal" class="create-instance-modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">➕ 创建ECS实例</h3>
                    <span class="close" onclick="closeCreateInstanceModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="createInstanceForm">
                        <!-- 基本配置 -->
                        <div class="form-section">
                            <h4>📍 基本配置</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="modalRegionId">地域ID *</label>
                                    <select id="modalRegionId" name="regionId" required>
                                        <option value="cn-hongkong">香港</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="modalInstanceType">实例规格 *</label>
                                    <select id="modalInstanceType" name="instanceType" required>
                                        <option value="ecs.u1-c1m1.xlarge">ecs.u1-c1m1.xlarge (4核4GB)</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="modalImageId">镜像ID *</label>
                                <input type="text" id="modalImageId" name="imageId" value="ubuntu_20_04_x64_20G_alibase_20250317.vhd" required>
                            </div>
                        </div>

                        <!-- 网络配置 -->
                        <div class="form-section">
                            <h4>🌐 网络配置</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="modalSecurityGroupId">安全组ID *</label>
                                    <input type="text" id="modalSecurityGroupId" name="securityGroupId" placeholder="sg-bp1fg655nh68xyz9****" value="sg-j6c4a5uhkybf1nxum01t" required>
                                </div>
                                <div class="form-group">
                                    <label for="modalVSwitchId">虚拟交换机ID *</label>
                                    <input type="text" id="modalVSwitchId" name="vSwitchId" placeholder="vsw-bp1s5fnvk4gn2tws0****" value="vsw-j6c8xzpo2pwx75zphjbp2" required>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="modalInternetMaxBandwidthOut">公网带宽 (Mbit/s)</label>
                                    <input type="number" id="modalInternetMaxBandwidthOut" name="internetMaxBandwidthOut" value="200" min="0" max="200">
                                </div>
                                <div class="form-group">
                                    <label for="modalInternetChargeType">网络计费类型</label>
                                    <select id="modalInternetChargeType" name="internetChargeType">
                                        <option value="PayByTraffic">按流量计费</option>
                                        <option value="PayByBandwidth">按带宽计费</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 弹性IP配置 -->
                        <div class="form-section">
                            <h4>🌐 弹性IP配置</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="modalEnableEIP">
                                        <input type="checkbox" id="modalEnableEIP" name="enableEIP" onchange="toggleModalEIPOptions()" checked>
                                        启用弹性IP (EIP)
                                    </label>
                                    <small class="form-help">启用后将自动创建弹性IP并绑定到实例，不分配公网IP</small>
                                </div>
                            </div>

                            <div id="modalEipOptions" >
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="modalEipBandwidth">EIP带宽 (Mbit/s)</label>
                                        <input type="number" id="modalEipBandwidth" name="eipBandwidth" value="200" min="1" max="200">
                                        <small class="form-help">留空则使用公网带宽设置</small>
                                    </div>
                                    <div class="form-group">
                                        <label for="modalEipChargeType">EIP计费类型</label>
                                        <select id="modalEipChargeType" name="eipChargeType">
                                            <option value="PayByTraffic">按流量计费</option>
                                            <option value="PayByBandwidth">按带宽计费</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="modalEipName">EIP名称</label>
                                        <input type="text" id="modalEipName" name="eipName" placeholder="留空则自动生成">
                                    </div>
                                    <div class="form-group">
                                        <label for="modalEipDescription">EIP描述</label>
                                        <input type="text" id="modalEipDescription" name="eipDescription" placeholder="Created by aliyun-manager">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 存储配置 -->
                        <div class="form-section">
                            <h4>💾 存储配置</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="modalSystemDiskSize">系统盘大小 (GB)</label>
                                    <input type="number" id="modalSystemDiskSize" name="systemDiskSize" value="40" min="20" max="500">
                                </div>
                                <div class="form-group">
                                    <label for="modalSystemDiskCategory">系统盘类型</label>
                                    <select id="modalSystemDiskCategory" name="systemDiskCategory">
                                        <option value="cloud_essd">ESSD云盘</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 计费配置 -->
                        <div class="form-section">
                            <h4>💰 计费配置</h4>
                            <div class="form-group">
                                <label for="modalInstanceChargeType">实例计费方式</label>
                                <select id="modalInstanceChargeType" name="instanceChargeType">
                                    <option value="PostPaid">按量付费</option>
                                    <option value="PrePaid">包年包月</option>
                                </select>
                            </div>
                        </div>

                        <!-- 实例信息 -->
                        <div class="form-section">
                            <h4>ℹ️ 实例信息</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="modalInstanceNamePrefix">实例名称前缀</label>
                                    <input type="text" id="modalInstanceNamePrefix" name="instanceNamePrefix" value="vm" placeholder="批量创建时的名称前缀">
                                    <small style="color: #666;">批量创建时会自动添加序号，如：vm-001, vm-002</small>
                                </div>
                                <div class="form-group">
                                    <label for="modalDescription">实例描述</label>
                                    <input type="text" id="modalDescription" name="description" value="Created by aliyun-manager">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="modalPassword">登录密码（可选）</label>
                                    <div style="position: relative;">
                                        <input type="password" id="modalPassword" name="password" placeholder="8-30位，包含大小写字母和数字">
                                        <button type="button" onclick="togglePasswordVisibility('modalPassword')" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); background: none; border: none; cursor: pointer; font-size: 14px;">👁️</button>
                                    </div>
                                    <small style="color: #666;">留空则使用密钥对登录。密码必须8-30位，包含大小写字母和数字</small>
                                </div>
                            </div>
                        </div>

                        <!-- 批量配置 -->
                        <div class="form-section">
                            <h4>🔢 批量配置</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="modalAmount">创建实例数量</label>
                                    <input type="number" id="modalAmount" name="amount" value="1" min="1" max="100">
                                    <small style="color: #666;">最多可创建100台实例</small>
                                </div>
                                <div class="form-group">
                                    <label for="modalMinAmount">最少创建数量（可选）</label>
                                    <input type="number" id="modalMinAmount" name="minAmount" value="0" min="0" max="100">
                                    <small style="color: #666;">库存不足时可接受的最少数量</small>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="modalDryRun" name="dryRun">
                                    <label for="modalDryRun">预检模式 (只验证参数，不实际创建实例)</label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-warning" onclick="validateModalConfig()">验证配置</button>
                    <button type="button" class="btn btn-secondary" onclick="closeCreateInstanceModal()">取消</button>
                    <button type="button" class="btn btn-success" onclick="createInstanceFromModal()">
                        <span class="loading-spinner" style="display: none;"></span>
                        创建实例
                    </button>
                </div>
            </div>
        </div>

{{template "footer" .}}