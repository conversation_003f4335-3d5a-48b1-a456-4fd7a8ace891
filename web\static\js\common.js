// 通用JavaScript函数

// 现代化Toast通知系统
function showAlert(message, type = 'success', duration = 5000) {
    // 创建toast容器（如果不存在）
    let container = document.querySelector('.toast-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'toast-container';
        document.body.appendChild(container);
    }

    // 创建toast元素
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;

    // 获取图标和标题
    const icons = {
        success: '✅',
        error: '❌',
        warning: '⚠️',
        info: 'ℹ️'
    };

    const titles = {
        success: '成功',
        error: '错误',
        warning: '警告',
        info: '提示'
    };

    toast.innerHTML = `
        <div class="toast-header">
            <div class="toast-title">${icons[type]} ${titles[type]}</div>
            <button class="toast-close" onclick="closeToast(this)">&times;</button>
        </div>
        <div class="toast-body">${message}</div>
        <div class="toast-progress"></div>
    `;

    container.appendChild(toast);

    // 显示动画
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);

    // 自动移除
    setTimeout(() => {
        closeToast(toast.querySelector('.toast-close'));
    }, duration);
}

// 关闭Toast
function closeToast(closeBtn) {
    const toast = closeBtn.closest('.toast');
    toast.classList.add('hide');
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 300);
}

// 显示/隐藏加载动画
function showLoading(show, text = '正在处理请求，请稍候...') {
    const loading = document.getElementById('loading');
    if (loading) {
        const loadingText = loading.querySelector('.loading-text');
        if (loadingText) {
            loadingText.textContent = text;
        }

        if (show) {
            loading.classList.add('show');
        } else {
            loading.classList.remove('show');
        }
    }
}

// 发送API请求的通用函数
async function apiRequest(url, options = {}) {
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json'
        }
    };

    const finalOptions = { ...defaultOptions, ...options };

    try {
        const response = await fetch(url, finalOptions);

        // 检查响应状态
        if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
        }

        // 获取响应文本
        const responseText = await response.text();

        // 尝试解析JSON
        try {
            const result = JSON.parse(responseText);
            return result;
        } catch (jsonError) {
            // 如果不是有效的JSON，返回错误信息
            console.error('JSON解析失败:', jsonError);
            console.error('响应内容:', responseText);
            throw new Error(`服务器返回了无效的JSON格式: ${responseText.substring(0, 100)}...`);
        }
    } catch (error) {
        console.error('API请求失败:', error);
        throw error;
    }
}

// 获取表单数据的通用函数
function getFormData(formId) {
    const form = document.getElementById(formId);
    const formData = new FormData(form);
    const data = {};
    
    for (let [key, value] of formData.entries()) {
        // 处理数字类型
        if (key === 'internetMaxBandwidthOut' || key === 'systemDiskSize' ||
            key === 'amount' || key === 'minAmount' || key === 'eipBandwidth' || key === 'eipCount') {
            data[key] = parseInt(value) || 0;
        }
        // 处理布尔类型
        else if (key === 'dryRun' || key === 'deleteProtected' ||
                 key === 'force' || key === 'saveToFile' || key === 'enableEIP') {
            data[key] = true;
        }
        // 处理字符串类型
        else {
            data[key] = value;
        }
    }
    
    // 处理未选中的复选框
    const checkboxes = ['dryRun', 'deleteProtected', 'force', 'saveToFile', 'enableEIP'];
    checkboxes.forEach(checkbox => {
        if (!formData.has(checkbox)) {
            data[checkbox] = false;
        }
    });
    
    return data;
}

// 验证必填字段
function validateRequired(data, requiredFields) {
    for (let field of requiredFields) {
        if (!data[field] || data[field].toString().trim() === '') {
            return `${field} 是必填字段`;
        }
    }
    return null;
}

// 格式化时间
function formatTime(timeString) {
    if (!timeString) return '未知';
    
    try {
        const date = new Date(timeString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        return timeString;
    }
}

// 复制文本到剪贴板
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showAlert('✅ 已复制到剪贴板', 'success');
    } catch (error) {
        console.error('复制失败:', error);
        showAlert('❌ 复制失败', 'error');
    }
}

// 现代化确认对话框
function confirmAction(options) {
    return new Promise((resolve) => {
        const {
            title = '确认操作',
            message = '您确定要执行此操作吗？',
            confirmText = '确认',
            cancelText = '取消',
            type = 'primary', // primary, danger, warning
            details = null
        } = options;

        // 创建模态框
        const overlay = document.createElement('div');
        overlay.className = 'modal-overlay';

        let detailsHtml = '';
        if (details) {
            detailsHtml = `<div style="background: #f8f9fa; padding: 12px; border-radius: 6px; margin-top: 12px; font-size: 14px; color: #666;">${details}</div>`;
        }

        overlay.innerHTML = `
            <div class="modal">
                <div class="modal-header">
                    <h3 class="modal-title">${title}</h3>
                </div>
                <div class="modal-body">
                    <p style="margin: 0; line-height: 1.5;">${message}</p>
                    ${detailsHtml}
                </div>
                <div class="modal-footer">
                    <button class="modal-btn modal-btn-secondary" onclick="closeModal(false)">
                        ${cancelText}
                    </button>
                    <button class="modal-btn modal-btn-${type}" onclick="closeModal(true)">
                        ${confirmText}
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(overlay);

        // 显示模态框
        setTimeout(() => {
            overlay.classList.add('show');
        }, 10);

        // 关闭模态框函数
        window.closeModal = function(confirmed) {
            overlay.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(overlay);
                delete window.closeModal;
                resolve(confirmed);
            }, 300);
        };

        // 点击背景关闭
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                window.closeModal(false);
            }
        });

        // ESC键关闭
        const handleEsc = (e) => {
            if (e.key === 'Escape') {
                window.closeModal(false);
                document.removeEventListener('keydown', handleEsc);
            }
        };
        document.addEventListener('keydown', handleEsc);
    });
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// 检查网络连接
function checkNetworkConnection() {
    return navigator.onLine;
}

// 本地存储工具
const storage = {
    set: function(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch (error) {
            console.error('存储失败:', error);
        }
    },
    
    get: function(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('读取存储失败:', error);
            return defaultValue;
        }
    },
    
    remove: function(key) {
        try {
            localStorage.removeItem(key);
        } catch (error) {
            console.error('删除存储失败:', error);
        }
    },
    
    clear: function() {
        try {
            localStorage.clear();
        } catch (error) {
            console.error('清空存储失败:', error);
        }
    }
};

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查网络连接
    if (!checkNetworkConnection()) {
        showAlert('⚠️ 网络连接异常，部分功能可能无法正常使用', 'warning');
    }
    
    // 监听网络状态变化
    window.addEventListener('online', function() {
        showAlert('✅ 网络连接已恢复', 'success');
    });
    
    window.addEventListener('offline', function() {
        showAlert('⚠️ 网络连接已断开', 'warning');
    });
});

// 错误处理
window.addEventListener('error', function(event) {
    console.error('页面错误:', event.error);
    showAlert('❌ 页面发生错误，请刷新重试', 'error');
});

// 未处理的Promise错误
window.addEventListener('unhandledrejection', function(event) {
    console.error('未处理的Promise错误:', event.reason);
    showAlert('❌ 请求处理失败，请重试', 'error');
});

// 切换EIP选项显示/隐藏
function toggleEIPOptions() {
    const enableEIP = document.getElementById('enableEIP');
    const eipOptions = document.getElementById('eipOptions');

    if (enableEIP && eipOptions) {
        if (enableEIP.checked) {
            eipOptions.style.display = 'block';
            // 如果启用EIP，提示用户公网带宽将被忽略
            const internetBandwidth = document.getElementById('internetMaxBandwidthOut');
            if (internetBandwidth) {
                internetBandwidth.style.backgroundColor = '#f0f0f0';
                internetBandwidth.title = '启用EIP时，此设置将被忽略';
            }
        } else {
            eipOptions.style.display = 'none';
            // 恢复公网带宽设置
            const internetBandwidth = document.getElementById('internetMaxBandwidthOut');
            if (internetBandwidth) {
                internetBandwidth.style.backgroundColor = '';
                internetBandwidth.title = '';
            }
        }
    }
}
