{{define "header"}}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}} - 阿里云管理器</title>
    <link rel="stylesheet" href="/static/css/style.css">
    {{if .ExtraCSS}}
        {{range .ExtraCSS}}
        <link rel="stylesheet" href="{{.}}">
        {{end}}
    {{end}}
</head>
<body>
    <div class="container">
        <header>
            <h1>{{.HeaderIcon}} {{.HeaderTitle}}</h1>
            <nav>
                <a href="/" {{if eq .ActivePage "home"}}class="active"{{end}}>创建实例</a>
                <a href="/instances" {{if eq .ActivePage "instances"}}class="active"{{end}}>实例管理</a>
                <a href="/release" {{if eq .ActivePage "release"}}class="active"{{end}}>释放实例</a>
                <a href="/eip" {{if eq .ActivePage "eip"}}class="active"{{end}}>弹性IP管理</a>
                <a href="/settings" {{if eq .ActivePage "settings"}}class="active"{{end}}>设置</a>
            </nav>
        </header>

        <main>
{{end}}

{{define "footer"}}
        </main>
    </div>

    <script src="/static/js/common.js"></script>
    {{if .ExtraJS}}
        {{range .ExtraJS}}
        <script src="{{.}}"></script>
        {{end}}
    {{end}}
</body>
</html>
{{end}}
