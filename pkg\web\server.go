package web

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"html/template"
	"io"
	"io/fs"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"aliyun-manager/pkg/client"
	"aliyun-manager/pkg/config"
	"aliyun-manager/pkg/ecs"

	"github.com/alibabacloud-go/tea/tea"
	vpc "github.com/alibabacloud-go/vpc-20160428/v6/client"
)

// Settings 设置结构
type Settings struct {
	AccessKeyId     string `json:"accessKeyId"`
	AccessKeySecret string `json:"accessKeySecret"`
	SaveToFile      bool   `json:"saveToFile"`
}

// Server Web服务器
type Server struct {
	port        int
	settings    *Settings
	templatesFS fs.FS
	staticFS    fs.FS
	useEmbedded bool
}

// NewServer 创建新的Web服务器
func NewServer(port int) *Server {
	server := &Server{
		port:     port,
		settings: &Settings{},
	}
	server.initFileSystem()
	server.loadSettingsFromFile()
	return server
}

// initFileSystem 初始化文件系统
func (s *Server) initFileSystem() {
	// 检查是否存在web目录（开发模式）
	if _, err := os.Stat("web"); err == nil {
		s.useEmbedded = false
		s.templatesFS = os.DirFS("web/templates")
		s.staticFS = os.DirFS("web/static")
		log.Println("🔧 开发模式：使用文件系统中的资源")
	} else {
		s.useEmbedded = true
		// 这里需要从main包获取嵌入的文件系统
		// 我们将在后面实现这个功能
		log.Println("📦 生产模式：使用嵌入的资源")
	}
}

// SetEmbeddedFS 设置嵌入的文件系统（由main包调用）
func (s *Server) SetEmbeddedFS(templatesFS, staticFS fs.FS) {
	if s.useEmbedded {
		s.templatesFS = templatesFS
		s.staticFS = staticFS
	}
}

// loadTemplate 加载模板文件
func (s *Server) loadTemplate(templateName string) (*template.Template, error) {
	if s.useEmbedded {
		// 使用嵌入的模板
		templatePath := "web/templates/" + templateName
		content, err := fs.ReadFile(s.templatesFS, templatePath)
		if err != nil {
			return nil, fmt.Errorf("读取嵌入模板失败: %v", err)
		}
		return template.New(templateName).Parse(string(content))
	} else {
		// 开发模式：使用文件系统
		return template.ParseFiles("web/templates/" + templateName)
	}
}

// loadTemplates 加载多个模板文件
func (s *Server) loadTemplates(templateNames ...string) (*template.Template, error) {
	var tmpl *template.Template

	for i, templateName := range templateNames {
		if s.useEmbedded {
			// 使用嵌入的模板
			templatePath := "web/templates/" + templateName
			content, err := fs.ReadFile(s.templatesFS, templatePath)
			if err != nil {
				return nil, fmt.Errorf("读取嵌入模板文件失败: %v", err)
			}

			if i == 0 {
				tmpl = template.New(templateName)
			} else {
				tmpl = tmpl.New(templateName)
			}

			_, err = tmpl.Parse(string(content))
			if err != nil {
				return nil, fmt.Errorf("解析模板失败: %v", err)
			}
		} else {
			// 使用文件系统中的模板
			templatePath := filepath.Join("web", "templates", templateName)
			content, err := os.ReadFile(templatePath)
			if err != nil {
				return nil, fmt.Errorf("读取模板文件失败: %v", err)
			}

			if i == 0 {
				tmpl = template.New(templateName)
			} else {
				tmpl = tmpl.New(templateName)
			}

			_, err = tmpl.Parse(string(content))
			if err != nil {
				return nil, fmt.Errorf("解析模板失败: %v", err)
			}
		}
	}

	return tmpl, nil
}

// Start 启动Web服务器
func (s *Server) Start() error {
	// 设置静态文件服务
	if s.useEmbedded {
		// 使用嵌入的静态文件
		staticSubFS, err := fs.Sub(s.staticFS, "web/static")
		if err != nil {
			log.Printf("⚠️ 无法创建静态文件子系统: %v", err)
			// 回退到直接使用staticFS
			staticSubFS = s.staticFS
		}
		http.Handle("/static/", http.StripPrefix("/static/", http.FileServer(http.FS(staticSubFS))))
	} else {
		// 开发模式：使用文件系统
		http.Handle("/static/", http.StripPrefix("/static/", http.FileServer(http.Dir("web/static/"))))
	}

	// 设置路由
	http.HandleFunc("/", s.handleHome)
	http.HandleFunc("/release", s.handleReleasePage)
	http.HandleFunc("/instances", s.handleInstancesPage)
	http.HandleFunc("/eip", s.handleEIPPage)
	http.HandleFunc("/settings", s.handleSettingsPage)
	http.HandleFunc("/api/create", s.handleCreateInstance)
	http.HandleFunc("/api/release", s.handleReleaseInstance)
	http.HandleFunc("/api/instances", s.handleQueryInstances)
	http.HandleFunc("/api/instances/list", s.handleQueryInstances)
	http.HandleFunc("/api/validate", s.handleValidateConfig)
	http.HandleFunc("/api/config/save", s.handleSaveConfig)
	http.HandleFunc("/api/config/load", s.handleLoadConfig)
	http.HandleFunc("/api/config/list", s.handleListConfigs)
	http.HandleFunc("/api/config/delete", s.handleDeleteConfig)
	http.HandleFunc("/api/instances/run-command", s.handleRunCommand)
	http.HandleFunc("/api/instances/get-command-result", s.handleGetCommandResult)
	http.HandleFunc("/api/instances/reset-password", s.handleResetPassword)

	http.HandleFunc("/api/instances/start-http-server", s.handleStartHTTPServer)
	http.HandleFunc("/api/instances/stop-http-server", s.handleStopHTTPServer)
	http.HandleFunc("/api/instances/download-via-http", s.handleDownloadViaHTTP)
	http.HandleFunc("/api/instances/download-file", s.handleDownloadFile)
	http.HandleFunc("/api/instances/download-file-chunked", s.handleDownloadFileChunked)
	http.HandleFunc("/api/instances/process-download", s.handleProcessDownload)
	http.HandleFunc("/api/instances/batch-download-file", s.handleBatchDownloadFile)
	http.HandleFunc("/api/instances/unified-download", s.handleUnifiedDownload)
	http.HandleFunc("/api/eip/create", s.handleCreateEIP)
	http.HandleFunc("/api/eip/list", s.handleListEIP)
	http.HandleFunc("/api/eip/release", s.handleReleaseEIP)
	http.HandleFunc("/api/eip/bind", s.handleBindEIP)
	http.HandleFunc("/api/eip/unbind", s.handleUnbindEIP)
	http.HandleFunc("/api/security-groups", s.handleListSecurityGroups)
	http.HandleFunc("/api/vswitches", s.handleListVSwitches)
	http.HandleFunc("/api/settings/save", s.handleSaveSettings)
	http.HandleFunc("/api/settings/load", s.handleLoadSettings)

	log.Printf("🌐 Web界面已启动: http://localhost:%d", s.port)
	log.Printf("请在浏览器中打开上述地址")

	return http.ListenAndServe(fmt.Sprintf(":%d", s.port), nil)
}

// handleHome 处理首页
func (s *Server) handleHome(w http.ResponseWriter, r *http.Request) {
	tmpl, err := s.loadTemplates("header.html", "index.html")
	if err != nil {
		http.Error(w, "模板加载失败: "+err.Error(), http.StatusInternalServerError)
		return
	}

	data := map[string]interface{}{
		"Title":       "创建实例",
		"HeaderIcon":  "🚀",
		"HeaderTitle": "ECS实例创建",
		"ActivePage":  "home",
		"ExtraCSS":    []string{"/static/css/create.css"},
		"ExtraJS":     []string{"/static/js/create.js"},
	}

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	tmpl.ExecuteTemplate(w, "index.html", data)
}

// handleReleasePage 处理释放页面
func (s *Server) handleReleasePage(w http.ResponseWriter, r *http.Request) {
	tmpl, err := s.loadTemplates("header.html", "footer.html", "release.html")
	if err != nil {
		http.Error(w, "模板加载失败: "+err.Error(), http.StatusInternalServerError)
		return
	}

	data := map[string]interface{}{
		"Title":       "释放实例",
		"HeaderIcon":  "🗑️",
		"HeaderTitle": "ECS实例释放",
		"ActivePage":  "release",
		"ExtraCSS":    []string{"/static/css/instances.css", "/static/css/release.css", "/static/css/drag-select.css"},
		"ExtraJS":     []string{"/static/js/drag-select.js", "/static/js/release.js"},
	}

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	tmpl.ExecuteTemplate(w, "release.html", data)
}

// handleInstancesPage 处理实例管理页面
func (s *Server) handleInstancesPage(w http.ResponseWriter, r *http.Request) {
	tmpl, err := s.loadTemplates("header.html", "footer.html", "instances.html")
	if err != nil {
		http.Error(w, "模板加载失败: "+err.Error(), http.StatusInternalServerError)
		return
	}

	data := map[string]interface{}{
		"Title":       "实例管理",
		"HeaderIcon":  "🖥️",
		"HeaderTitle": "ECS实例管理",
		"ActivePage":  "instances",
		"ExtraCSS":    []string{"/static/css/instances.css"},
		"ExtraJS":     []string{"/static/js/instances.js"},
	}

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	tmpl.ExecuteTemplate(w, "instances.html", data)
}

// handleSettingsPage 处理设置页面
func (s *Server) handleSettingsPage(w http.ResponseWriter, r *http.Request) {
	tmpl, err := s.loadTemplates("header.html", "settings.html")
	if err != nil {
		http.Error(w, "模板加载失败: "+err.Error(), http.StatusInternalServerError)
		return
	}

	data := map[string]interface{}{
		"Title":       "系统设置",
		"HeaderIcon":  "⚙️",
		"HeaderTitle": "系统设置",
		"ActivePage":  "settings",
		"ExtraCSS":    []string{"/static/css/settings.css"},
		"ExtraJS":     []string{"/static/js/settings.js"},
	}

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	tmpl.ExecuteTemplate(w, "settings.html", data)
}

// APIResponse API响应结构
type APIResponse struct {
	Success  bool        `json:"success"`
	Message  string      `json:"message,omitempty"`
	Error    string      `json:"error,omitempty"`
	Filename string      `json:"filename,omitempty"`
	Data     interface{} `json:"data,omitempty"`
}

// handleCreateInstance 处理创建实例请求
func (s *Server) handleCreateInstance(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var cfg config.ECSInstanceConfig
	if err := json.NewDecoder(r.Body).Decode(&cfg); err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "解析请求数据失败: " + err.Error()})
		return
	}

	// 验证配置
	if err := cfg.Validate(); err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "配置验证失败: " + err.Error()})
		return
	}

	// 检查环境变量
	if os.Getenv("ACCESS_KEY_ID") == "" || os.Getenv("ACCESS_KEY_SECRET") == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "请设置环境变量 ACCESS_KEY_ID 和 ACCESS_KEY_SECRET"})
		return
	}

	// 创建ECS客户端
	ecsClient, err := client.NewECSClient(cfg.RegionId)
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "创建ECS客户端失败: " + err.Error()})
		return
	}

	// 创建实例管理器
	instanceManager := ecs.NewInstanceManager(ecsClient.GetClient())

	// 如果启用EIP，创建VPC客户端和EIP管理器
	var eipManager *ecs.EIPManager
	if cfg.EnableEIP {
		vpcClient, err := client.NewVPCClient(cfg.RegionId)
		if err != nil {
			s.sendJSONResponse(w, APIResponse{Success: false, Error: "创建VPC客户端失败: " + err.Error()})
			return
		}
		eipManager = ecs.NewEIPManager(vpcClient.GetClient())
	}

	// 创建ECS实例（支持EIP）
	result, err := instanceManager.CreateInstanceWithEIP(&cfg, eipManager)
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "创建ECS实例失败: " + err.Error()})
		return
	}

	if !result.Success {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "创建ECS实例失败: " + result.Error.Error()})
		return
	}

	// 构建成功消息
	message := "ECS实例创建成功！"
	if cfg.DryRun {
		message = "参数验证通过！您可以取消预检模式来实际创建实例"
	} else {
		if cfg.EnableEIP && len(result.EIPIds) > 0 {
			message = fmt.Sprintf("ECS实例创建成功！已为 %d 台实例创建并绑定弹性IP", len(result.InstanceIds))
		} else {
			message = fmt.Sprintf("ECS实例创建成功！共创建 %d 台实例", len(result.InstanceIds))
		}
	}

	s.sendJSONResponse(w, APIResponse{Success: true, Message: message})
}

// handleReleaseInstance 处理释放实例请求
func (s *Server) handleReleaseInstance(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var cfg config.ECSReleaseConfig
	if err := json.NewDecoder(r.Body).Decode(&cfg); err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "解析请求数据失败: " + err.Error()})
		return
	}

	// 验证配置
	if err := cfg.ValidateRelease(); err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "配置验证失败: " + err.Error()})
		return
	}

	// 检查环境变量
	if os.Getenv("ACCESS_KEY_ID") == "" || os.Getenv("ACCESS_KEY_SECRET") == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "请设置环境变量 ACCESS_KEY_ID 和 ACCESS_KEY_SECRET"})
		return
	}

	// 创建ECS客户端
	ecsClient, err := client.NewECSClient(cfg.RegionId)
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "创建ECS客户端失败: " + err.Error()})
		return
	}

	// 创建实例管理器
	instanceManager := ecs.NewInstanceManager(ecsClient.GetClient())

	// 释放ECS实例
	if err := instanceManager.ReleaseInstances(cfg.RegionId, cfg.InstanceIds, cfg.InstanceName, cfg.DeleteProtected, cfg.Force); err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "释放ECS实例失败: " + err.Error()})
		return
	}

	message := "ECS实例释放成功！"
	if cfg.DryRun {
		message = "实例查询完成！您可以取消预检模式来实际释放实例"
	}

	s.sendJSONResponse(w, APIResponse{Success: true, Message: message})
}

// handleQueryInstances 处理查询实例请求
func (s *Server) handleQueryInstances(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	regionId := r.URL.Query().Get("regionId")
	if regionId == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "缺少regionId参数"})
		return
	}

	// 检查环境变量
	if os.Getenv("ACCESS_KEY_ID") == "" || os.Getenv("ACCESS_KEY_SECRET") == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "请设置环境变量 ACCESS_KEY_ID 和 ACCESS_KEY_SECRET"})
		return
	}

	// 创建ECS客户端
	ecsClient, err := client.NewECSClient(regionId)
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "创建ECS客户端失败: " + err.Error()})
		return
	}

	// 创建实例管理器
	instanceManager := ecs.NewInstanceManager(ecsClient.GetClient())

	// 查询实例
	instances, err := instanceManager.DescribeInstancesForWeb(regionId)
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "查询实例失败: " + err.Error()})
		return
	}

	// 返回实例列表
	s.sendJSONResponse(w, APIResponse{
		Success: true,
		Data:    instances,
	})
}

// handleValidateConfig 处理配置验证请求
func (s *Server) handleValidateConfig(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var cfg config.ECSInstanceConfig
	if err := json.NewDecoder(r.Body).Decode(&cfg); err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "解析请求数据失败: " + err.Error()})
		return
	}

	// 验证配置
	if err := cfg.Validate(); err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: err.Error()})
		return
	}

	s.sendJSONResponse(w, APIResponse{Success: true, Message: "配置验证通过"})
}

// handleSaveConfig 处理保存配置请求
func (s *Server) handleSaveConfig(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var cfg config.ECSInstanceConfig
	if err := json.NewDecoder(r.Body).Decode(&cfg); err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "解析请求数据失败: " + err.Error()})
		return
	}

	// 生成文件名
	filename := fmt.Sprintf("web-config-%d.json", os.Getpid())

	// 保存配置文件
	if err := s.saveConfigToFile(&cfg, filename); err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "保存配置文件失败: " + err.Error()})
		return
	}

	s.sendJSONResponse(w, APIResponse{Success: true, Message: "配置已保存", Filename: filename})
}

// handleLoadConfig 处理加载配置请求
func (s *Server) handleLoadConfig(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 解析请求体获取文件名
	var request struct {
		Filename string `json:"filename"`
	}
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "解析请求数据失败: " + err.Error()})
		return
	}

	if request.Filename == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "文件名不能为空"})
		return
	}

	// 加载配置文件
	cfg, err := s.loadConfigFromFile(request.Filename)
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "加载配置文件失败: " + err.Error()})
		return
	}

	// 返回配置数据
	s.sendJSONResponse(w, APIResponse{
		Success: true,
		Message: "配置加载成功",
		Data:    cfg,
	})
}

// handleListConfigs 处理列出配置文件请求
func (s *Server) handleListConfigs(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 获取配置文件列表
	configs, err := s.listConfigFiles()
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "获取配置文件列表失败: " + err.Error()})
		return
	}

	// 返回配置文件列表
	s.sendJSONResponse(w, APIResponse{
		Success: true,
		Message: fmt.Sprintf("找到 %d 个配置文件", len(configs)),
		Data:    configs,
	})
}

// handleDeleteConfig 处理删除配置文件请求
func (s *Server) handleDeleteConfig(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 解析请求体获取文件名
	var request struct {
		Filename string `json:"filename"`
	}
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "解析请求数据失败: " + err.Error()})
		return
	}

	if request.Filename == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "文件名不能为空"})
		return
	}

	// 安全检查：只允许删除web-config-开头的JSON文件
	if !strings.HasPrefix(request.Filename, "web-config-") || !strings.HasSuffix(request.Filename, ".json") {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "只能删除web-config-开头的配置文件"})
		return
	}

	// 检查文件是否存在
	if _, err := os.Stat(request.Filename); os.IsNotExist(err) {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "配置文件不存在"})
		return
	}

	// 删除文件
	if err := os.Remove(request.Filename); err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "删除配置文件失败: " + err.Error()})
		return
	}

	log.Printf("配置文件已删除: %s", request.Filename)
	s.sendJSONResponse(w, APIResponse{
		Success: true,
		Message: "配置文件删除成功",
	})
}

// handleStatic 处理静态文件请求
func (s *Server) handleStatic(w http.ResponseWriter, r *http.Request) {
	// 这里可以处理CSS、JS等静态文件
	http.NotFound(w, r)
}

// handleSaveSettings 处理保存设置请求
func (s *Server) handleSaveSettings(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var settings Settings
	if err := json.NewDecoder(r.Body).Decode(&settings); err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "解析请求数据失败: " + err.Error()})
		return
	}

	// 更新服务器设置
	s.settings = &settings

	// 如果选择保存到文件，则保存
	if settings.SaveToFile {
		if err := s.saveSettingsToFile(&settings); err != nil {
			s.sendJSONResponse(w, APIResponse{Success: false, Error: "保存设置到文件失败: " + err.Error()})
			return
		}
	}

	// 设置环境变量
	if settings.AccessKeyId != "" && settings.AccessKeySecret != "" {
		os.Setenv("ACCESS_KEY_ID", settings.AccessKeyId)
		os.Setenv("ACCESS_KEY_SECRET", settings.AccessKeySecret)
	}

	s.sendJSONResponse(w, APIResponse{Success: true, Message: "设置保存成功"})
}

// handleLoadSettings 处理加载设置请求
func (s *Server) handleLoadSettings(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 检查连接状态
	connected := false
	if s.settings.AccessKeyId != "" && s.settings.AccessKeySecret != "" {
		// 简单检查环境变量是否设置
		connected = os.Getenv("ACCESS_KEY_ID") != "" && os.Getenv("ACCESS_KEY_SECRET") != ""
	}

	response := map[string]interface{}{
		"success": true,
		"settings": map[string]interface{}{
			"accessKeyId": s.settings.AccessKeyId,
			"accessKeySecret": func() string {
				if s.settings.AccessKeySecret != "" {
					return "已设置"
				}
				return ""
			}(),
			"saveToFile": s.settings.SaveToFile,
			"connected":  connected,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// loadSettingsFromFile 从文件加载设置
func (s *Server) loadSettingsFromFile() {
	filename := "aliyun-settings.json"
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		return
	}

	data, err := os.ReadFile(filename)
	if err != nil {
		log.Printf("读取设置文件失败: %v", err)
		return
	}

	var settings Settings
	if err := json.Unmarshal(data, &settings); err != nil {
		log.Printf("解析设置文件失败: %v", err)
		return
	}

	s.settings = &settings

	// 设置环境变量
	if settings.AccessKeyId != "" && settings.AccessKeySecret != "" {
		os.Setenv("ACCESS_KEY_ID", settings.AccessKeyId)
		os.Setenv("ACCESS_KEY_SECRET", settings.AccessKeySecret)
		log.Printf("已从文件加载阿里云访问密钥")
	}
}

// saveSettingsToFile 保存设置到文件
func (s *Server) saveSettingsToFile(settings *Settings) error {
	filename := "aliyun-settings.json"
	data, err := json.MarshalIndent(settings, "", "  ")
	if err != nil {
		return err
	}

	if err := os.WriteFile(filename, data, 0600); err != nil {
		return err
	}

	log.Printf("设置已保存到文件: %s", filename)
	return nil
}

// saveConfigToFile 保存配置到文件
func (s *Server) saveConfigToFile(cfg *config.ECSInstanceConfig, filename string) error {
	// 验证配置
	if cfg == nil {
		return fmt.Errorf("配置不能为空")
	}

	// 创建配置数据结构，包含元数据
	configData := map[string]interface{}{
		"metadata": map[string]interface{}{
			"version":     "1.0",
			"created_at":  fmt.Sprintf("%d", os.Getpid()), // 使用进程ID作为简单的时间戳
			"created_by":  "aliyun-manager-web",
			"description": "ECS实例创建配置文件",
		},
		"ecs": cfg,
	}

	// 将配置序列化为JSON格式，使用缩进格式化
	data, err := json.MarshalIndent(configData, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	// 写入文件，设置适当的权限（只有所有者可读写）
	if err := os.WriteFile(filename, data, 0600); err != nil {
		return fmt.Errorf("写入配置文件失败: %v", err)
	}

	log.Printf("配置已保存到文件: %s", filename)
	return nil
}

// loadConfigFromFile 从文件加载配置
func (s *Server) loadConfigFromFile(filename string) (*config.ECSInstanceConfig, error) {
	// 检查文件是否存在
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		return nil, fmt.Errorf("配置文件不存在: %s", filename)
	}

	// 读取文件内容
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 解析JSON数据
	var configData map[string]interface{}
	if err := json.Unmarshal(data, &configData); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 提取ECS配置部分
	ecsData, exists := configData["ecs"]
	if !exists {
		return nil, fmt.Errorf("配置文件中缺少ecs配置节")
	}

	// 将ECS配置转换为JSON字节数组，然后反序列化为ECSInstanceConfig
	ecsBytes, err := json.Marshal(ecsData)
	if err != nil {
		return nil, fmt.Errorf("序列化ECS配置失败: %v", err)
	}

	var cfg config.ECSInstanceConfig
	if err := json.Unmarshal(ecsBytes, &cfg); err != nil {
		return nil, fmt.Errorf("解析ECS配置失败: %v", err)
	}

	// 验证配置
	if err := cfg.Validate(); err != nil {
		return nil, fmt.Errorf("配置验证失败: %v", err)
	}

	log.Printf("配置已从文件加载: %s", filename)
	return &cfg, nil
}

// ConfigFileInfo 配置文件信息
type ConfigFileInfo struct {
	Filename    string `json:"filename"`
	Size        int64  `json:"size"`
	ModTime     string `json:"modTime"`
	Description string `json:"description,omitempty"`
}

// listConfigFiles 列出当前目录下的配置文件
func (s *Server) listConfigFiles() ([]ConfigFileInfo, error) {
	var configs []ConfigFileInfo

	// 读取当前目录
	files, err := os.ReadDir(".")
	if err != nil {
		return nil, fmt.Errorf("读取目录失败: %v", err)
	}

	// 筛选配置文件（以web-config-开头的JSON文件）
	for _, file := range files {
		if file.IsDir() {
			continue
		}

		filename := file.Name()
		// 检查是否是配置文件
		if !strings.HasPrefix(filename, "web-config-") || !strings.HasSuffix(filename, ".json") {
			continue
		}

		// 获取文件信息
		info, err := file.Info()
		if err != nil {
			log.Printf("获取文件信息失败 %s: %v", filename, err)
			continue
		}

		// 尝试读取配置文件的描述信息
		description := ""
		if data, err := os.ReadFile(filename); err == nil {
			var configData map[string]interface{}
			if err := json.Unmarshal(data, &configData); err == nil {
				if metadata, ok := configData["metadata"].(map[string]interface{}); ok {
					if desc, ok := metadata["description"].(string); ok {
						description = desc
					}
				}
			}
		}

		configs = append(configs, ConfigFileInfo{
			Filename:    filename,
			Size:        info.Size(),
			ModTime:     info.ModTime().Format("2006-01-02 15:04:05"),
			Description: description,
		})
	}

	return configs, nil
}

// performUnifiedDownload 执行统一批量下载
func (s *Server) performUnifiedDownload(files []DownloadFileItem, config *DownloadConfig) []DownloadResult {
	results := make([]DownloadResult, len(files))

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: time.Duration(config.TimeoutSeconds) * time.Second,
	}

	// 创建工作池
	semaphore := make(chan struct{}, config.MaxConcurrency)
	var wg sync.WaitGroup

	for i, file := range files {
		wg.Add(1)
		go func(index int, fileItem DownloadFileItem) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			results[index] = s.downloadSingleFileUnified(client, fileItem, config)
		}(i, file)
	}

	wg.Wait()
	return results
}

// downloadSingleFileUnified 下载单个文件（统一版本）
func (s *Server) downloadSingleFileUnified(client *http.Client, file DownloadFileItem, config *DownloadConfig) DownloadResult {
	startTime := time.Now()
	result := DownloadResult{
		InstanceID: file.InstanceID,
		FileName:   file.FileName,
		Success:    false,
	}

	// 设置默认端口
	port := file.Port
	if port == 0 {
		port = 8000
	}

	// HTTP服务器中的文件名（原始文件名）
	httpFileName := filepath.Base(file.FilePath)

	// 本地保存的文件名（带实例名前缀）
	localFileName := file.FileName
	if localFileName == "" {
		localFileName = filepath.Base(file.FilePath)
	}
	result.FileName = localFileName

	// 构建完整URL（使用HTTP服务器中的原始文件名）
	url := fmt.Sprintf("http://%s:%d/%s", file.InstanceIP, port, httpFileName)
	log.Printf("🌐 下载URL: %s -> 本地文件名: %s", url, localFileName)
	// 重试机制
	var lastErr error
	for attempt := 0; attempt <= config.RetryAttempts; attempt++ {
		if attempt > 0 {
			// 重试延迟
			time.Sleep(time.Duration(attempt) * time.Second)
			log.Printf("🔄 重试下载 %s (第%d次)", localFileName, attempt)
		}

		localPath, err := s.performSingleDownloadUnified(client, url, localFileName)
		if err == nil {
			// 下载成功
			if fileInfo, err := os.Stat(localPath); err == nil {
				result.FileSize = fileInfo.Size()
			}
			result.Success = true
			result.LocalPath = localPath
			result.Duration = time.Since(startTime)
			log.Printf("✅ 下载成功: %s (%d bytes)", localFileName, result.FileSize)
			return result
		}

		lastErr = err
		log.Printf("❌ 下载失败: %s - %v", localFileName, err)
	}

	result.Error = fmt.Sprintf("下载失败 (重试%d次): %v", config.RetryAttempts, lastErr)
	result.Duration = time.Since(startTime)
	return result
}

// performSingleDownloadUnified 执行单个文件的下载（统一版本）
func (s *Server) performSingleDownloadUnified(client *http.Client, url, fileName string) (string, error) {
	// 发送HTTP请求
	resp, err := client.Get(url)
	if err != nil {
		return "", fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("HTTP错误: %d %s", resp.StatusCode, http.StatusText(resp.StatusCode))
	}

	// 确保下载目录存在
	downloadDir := "downloads"
	if err := os.MkdirAll(downloadDir, 0755); err != nil {
		return "", fmt.Errorf("创建下载目录失败: %v", err)
	}

	// 创建本地文件
	localPath := filepath.Join(downloadDir, fileName)
	file, err := os.Create(localPath)
	if err != nil {
		return "", fmt.Errorf("创建本地文件失败: %v", err)
	}
	defer file.Close()

	// 复制文件内容
	_, err = io.Copy(file, resp.Body)
	if err != nil {
		return "", fmt.Errorf("写入文件失败: %v", err)
	}

	return localPath, nil
}

// handleRunCommand 处理执行命令请求
func (s *Server) handleRunCommand(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 解析请求体
	var request struct {
		RegionId       string   `json:"regionId"`
		InstanceIds    []string `json:"instanceIds"`
		CommandContent string   `json:"commandContent"`
		CommandType    string   `json:"commandType"`
	}
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "解析请求数据失败: " + err.Error()})
		return
	}

	// 验证参数
	if request.RegionId == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "地域ID不能为空"})
		return
	}
	if len(request.InstanceIds) == 0 {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "实例ID列表不能为空"})
		return
	}
	if request.CommandContent == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "命令内容不能为空"})
		return
	}

	// 检查环境变量
	if os.Getenv("ACCESS_KEY_ID") == "" || os.Getenv("ACCESS_KEY_SECRET") == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "请设置环境变量 ACCESS_KEY_ID 和 ACCESS_KEY_SECRET"})
		return
	}

	// 创建ECS客户端
	ecsClient, err := client.NewECSClient(request.RegionId)
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "创建ECS客户端失败: " + err.Error()})
		return
	}

	// 创建实例管理器
	instanceManager := ecs.NewInstanceManager(ecsClient.GetClient())

	// 执行命令
	response, err := instanceManager.RunCommand(request.RegionId, request.InstanceIds, request.CommandContent, request.CommandType)
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "执行命令失败: " + err.Error()})
		return
	}

	// 返回执行结果
	s.sendJSONResponse(w, APIResponse{
		Success: true,
		Message: "命令执行成功",
		Data: map[string]interface{}{
			"invokeId": *response.Body.InvokeId,
		},
	})
}

// handleResetPassword 处理批量重置ECS实例密码请求
func (s *Server) handleResetPassword(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 解析请求体
	var request struct {
		RegionId    string   `json:"regionId"`
		InstanceIds []string `json:"instanceIds"`
		Password    string   `json:"password"`
		Username    string   `json:"username,omitempty"`   // 可选，默认为root
		CreateUser  bool     `json:"createUser,omitempty"` // 是否创建新用户
	}
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "解析请求数据失败: " + err.Error()})
		return
	}

	// 验证参数
	if request.RegionId == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "地域ID不能为空"})
		return
	}
	if len(request.InstanceIds) == 0 {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "实例ID列表不能为空"})
		return
	}
	if request.Password == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "密码不能为空"})
		return
	}

	// 验证密码复杂度
	if err := validatePassword(request.Password); err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "密码不符合要求: " + err.Error()})
		return
	}

	// 设置默认用户名
	if request.Username == "" {
		request.Username = "root"
	}

	// 检查环境变量
	if os.Getenv("ACCESS_KEY_ID") == "" || os.Getenv("ACCESS_KEY_SECRET") == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "请设置环境变量 ACCESS_KEY_ID 和 ACCESS_KEY_SECRET"})
		return
	}

	// 创建ECS客户端
	ecsClient, err := client.NewECSClient(request.RegionId)
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "创建ECS客户端失败: " + err.Error()})
		return
	}

	// 创建实例管理器
	instanceManager := ecs.NewInstanceManager(ecsClient.GetClient())

	// 生成重置密码的命令
	var commandContent string
	if request.CreateUser && request.Username != "root" {
		// 创建新用户并重置密码
		commandContent = fmt.Sprintf(`
# 创建新用户并重置密码
useradd -m %s 2>/dev/null || echo "用户已存在"
echo '%s:%s' | chpasswd
usermod -aG sudo %s 2>/dev/null || usermod -aG wheel %s 2>/dev/null || echo "添加sudo权限完成"
echo "用户 %s 密码重置完成"
`, request.Username, request.Username, request.Password, request.Username, request.Username, request.Username)
	} else {
		// 直接重置用户密码
		commandContent = fmt.Sprintf(`
# 重置用户密码
echo '%s:%s' | chpasswd
echo "用户 %s 密码重置完成"
`, request.Username, request.Password, request.Username)
	}

	// 执行命令
	response, err := instanceManager.RunCommand(request.RegionId, request.InstanceIds, commandContent, "RunShellScript")
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "执行密码重置命令失败: " + err.Error()})
		return
	}

	// 返回执行结果
	s.sendJSONResponse(w, APIResponse{
		Success: true,
		Message: fmt.Sprintf("密码重置命令已发送到 %d 个实例", len(request.InstanceIds)),
		Data: map[string]interface{}{
			"invokeId":      *response.Body.InvokeId,
			"username":      request.Username,
			"instanceCount": len(request.InstanceIds),
		},
	})
}

// validatePassword 验证密码复杂度
func validatePassword(password string) error {
	if len(password) < 8 || len(password) > 30 {
		return fmt.Errorf("密码长度必须在8-30位之间")
	}

	var hasUpper, hasLower, hasDigit, hasSpecial bool
	for _, char := range password {
		switch {
		case char >= 'A' && char <= 'Z':
			hasUpper = true
		case char >= 'a' && char <= 'z':
			hasLower = true
		case char >= '0' && char <= '9':
			hasDigit = true
		case strings.ContainsRune("!@#$%^&*()_+-=[]{}|;:,.<>?", char):
			hasSpecial = true
		}
	}

	count := 0
	if hasUpper {
		count++
	}
	if hasLower {
		count++
	}
	if hasDigit {
		count++
	}
	if hasSpecial {
		count++
	}

	if count < 3 {
		return fmt.Errorf("密码必须包含大写字母、小写字母、数字、特殊字符中的至少3种")
	}

	return nil
}

// handleGetCommandResult 处理获取命令执行结果请求
func (s *Server) handleGetCommandResult(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	regionId := r.URL.Query().Get("regionId")
	invokeId := r.URL.Query().Get("invokeId")

	if regionId == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "地域ID不能为空"})
		return
	}
	if invokeId == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "执行ID不能为空"})
		return
	}

	// 检查环境变量
	if os.Getenv("ACCESS_KEY_ID") == "" || os.Getenv("ACCESS_KEY_SECRET") == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "请设置环境变量 ACCESS_KEY_ID 和 ACCESS_KEY_SECRET"})
		return
	}

	// 创建ECS客户端
	ecsClient, err := client.NewECSClient(regionId)
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "创建ECS客户端失败: " + err.Error()})
		return
	}

	// 创建实例管理器
	instanceManager := ecs.NewInstanceManager(ecsClient.GetClient())

	// 查询命令执行结果
	response, err := instanceManager.DescribeInvocations(regionId, invokeId)
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "查询命令执行结果失败: " + err.Error()})
		return
	}

	// 处理返回结果
	var results []map[string]interface{}
	if response.Body.Invocations != nil && len(response.Body.Invocations.Invocation) > 0 {
		for _, invocation := range response.Body.Invocations.Invocation {
			if invocation.InvokeInstances != nil {
				for _, instance := range invocation.InvokeInstances.InvokeInstance {
					result := map[string]interface{}{
						"instanceId":       tea.StringValue(instance.InstanceId),
						"invocationStatus": tea.StringValue(instance.InvocationStatus),
						"output":           tea.StringValue(instance.Output),
						"errorCode":        tea.StringValue(instance.ErrorCode),
						"errorInfo":        tea.StringValue(instance.ErrorInfo),
						"exitCode":         tea.Int64Value(instance.ExitCode),
						"finishTime":       tea.StringValue(instance.FinishTime),
					}
					results = append(results, result)
				}
			}
		}
	}

	// 返回查询结果
	s.sendJSONResponse(w, APIResponse{
		Success: true,
		Message: "查询成功",
		Data: map[string]interface{}{
			"results": results,
		},
	})
}

// handleEIPPage 处理弹性IP管理页面
func (s *Server) handleEIPPage(w http.ResponseWriter, r *http.Request) {
	log.Printf("EIP页面请求: %s", r.URL.Path)

	tmpl, err := s.loadTemplates("header.html", "eip.html")
	if err != nil {
		log.Printf("模板加载失败: %v", err)
		http.Error(w, "模板加载失败: "+err.Error(), http.StatusInternalServerError)
		return
	}

	data := map[string]interface{}{
		"Title":       "弹性IP管理",
		"HeaderIcon":  "🌐",
		"HeaderTitle": "弹性IP管理",
		"ActivePage":  "eip",
		"ExtraCSS":    []string{"/static/css/eip.css"},
		"ExtraJS":     []string{"/static/js/eip.js"},
	}

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	err = tmpl.ExecuteTemplate(w, "eip.html", data)
	if err != nil {
		log.Printf("页面渲染失败: %v", err)
		http.Error(w, "页面渲染失败: "+err.Error(), http.StatusInternalServerError)
		return
	}

	log.Printf("EIP页面渲染成功")
}

// handleCreateEIP 处理创建弹性IP请求
func (s *Server) handleCreateEIP(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req struct {
		RegionId       string `json:"regionId"`
		EipBandwidth   int    `json:"eipBandwidth"`
		EipChargeType  string `json:"eipChargeType"`
		EipName        string `json:"eipName"`
		EipDescription string `json:"eipDescription"`
		EipCount       int    `json:"eipCount"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "解析请求数据失败: " + err.Error()})
		return
	}

	// 检查环境变量
	if os.Getenv("ACCESS_KEY_ID") == "" || os.Getenv("ACCESS_KEY_SECRET") == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "请设置环境变量 ACCESS_KEY_ID 和 ACCESS_KEY_SECRET"})
		return
	}

	// 创建VPC客户端
	vpcClient, err := client.NewVPCClient(req.RegionId)
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "创建VPC客户端失败: " + err.Error()})
		return
	}

	// 创建EIP管理器
	eipManager := ecs.NewEIPManager(vpcClient.GetClient())

	// 创建配置
	cfg := &config.ECSInstanceConfig{
		RegionId:       req.RegionId,
		EnableEIP:      true,
		EIPBandwidth:   &req.EipBandwidth,
		EIPChargeType:  req.EipChargeType,
		EIPName:        req.EipName,
		EIPDescription: req.EipDescription,
	}

	// 批量创建EIP
	createdEIPs := make([]string, 0)
	for i := 0; i < req.EipCount; i++ {
		result, err := eipManager.CreateEIP(cfg)
		if err != nil {
			s.sendJSONResponse(w, APIResponse{Success: false, Error: fmt.Sprintf("创建第%d个EIP失败: %v", i+1, err)})
			return
		}
		if !result.Success {
			s.sendJSONResponse(w, APIResponse{Success: false, Error: fmt.Sprintf("创建第%d个EIP失败: %v", i+1, result.Error)})
			return
		}
		createdEIPs = append(createdEIPs, result.EIPId)
	}

	s.sendJSONResponse(w, APIResponse{
		Success: true,
		Message: fmt.Sprintf("成功创建 %d 个弹性IP", len(createdEIPs)),
		Data:    map[string]interface{}{"eipIds": createdEIPs},
	})
}

// handleListEIP 处理查询弹性IP列表请求
func (s *Server) handleListEIP(w http.ResponseWriter, r *http.Request) {
	regionId := r.URL.Query().Get("regionId")
	if regionId == "" {
		regionId = "cn-hongkong"
	}

	// 检查环境变量
	if os.Getenv("ACCESS_KEY_ID") == "" || os.Getenv("ACCESS_KEY_SECRET") == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "请设置环境变量 ACCESS_KEY_ID 和 ACCESS_KEY_SECRET"})
		return
	}

	// 创建VPC客户端
	vpcClient, err := client.NewVPCClient(regionId)
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "创建VPC客户端失败: " + err.Error()})
		return
	}

	// 查询EIP列表
	request := &vpc.DescribeEipAddressesRequest{
		RegionId: tea.String(regionId),
		PageSize: tea.Int32(100),
	}

	response, err := vpcClient.GetClient().DescribeEipAddresses(request)
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "查询弹性IP列表失败: " + err.Error()})
		return
	}

	// 转换数据格式
	eipList := make([]map[string]interface{}, 0)
	if response.Body.EipAddresses != nil {
		for _, eip := range response.Body.EipAddresses.EipAddress {
			eipInfo := map[string]interface{}{
				"allocationId": tea.StringValue(eip.AllocationId),
				"ipAddress":    tea.StringValue(eip.IpAddress),
				"status":       tea.StringValue(eip.Status),
				"bandwidth":    tea.StringValue(eip.Bandwidth),
				"chargeType":   tea.StringValue(eip.InternetChargeType),
				"instanceId":   tea.StringValue(eip.InstanceId),
				"instanceType": tea.StringValue(eip.InstanceType),
			}
			eipList = append(eipList, eipInfo)
		}
	}

	s.sendJSONResponse(w, APIResponse{
		Success: true,
		Data:    eipList,
	})
}

// handleReleaseEIP 处理释放弹性IP请求
func (s *Server) handleReleaseEIP(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req struct {
		EipIds []string `json:"eipIds"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "解析请求数据失败: " + err.Error()})
		return
	}

	if len(req.EipIds) == 0 {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "请提供要释放的EIP ID"})
		return
	}

	// 检查环境变量
	if os.Getenv("ACCESS_KEY_ID") == "" || os.Getenv("ACCESS_KEY_SECRET") == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "请设置环境变量 ACCESS_KEY_ID 和 ACCESS_KEY_SECRET"})
		return
	}

	// 创建VPC客户端（使用第一个EIP的地域）
	vpcClient, err := client.NewVPCClient("cn-hongkong") // 默认地域
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "创建VPC客户端失败: " + err.Error()})
		return
	}

	// 创建EIP管理器
	eipManager := ecs.NewEIPManager(vpcClient.GetClient())

	// 批量释放EIP
	failedEIPs := make([]string, 0)
	for _, eipId := range req.EipIds {
		err := eipManager.ReleaseEIP(eipId)
		if err != nil {
			failedEIPs = append(failedEIPs, eipId)
		}
	}

	if len(failedEIPs) > 0 {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   fmt.Sprintf("部分EIP释放失败: %v", failedEIPs),
		})
		return
	}

	s.sendJSONResponse(w, APIResponse{
		Success: true,
		Message: fmt.Sprintf("成功释放 %d 个弹性IP", len(req.EipIds)),
	})
}

// handleBindEIP 处理绑定弹性IP请求
func (s *Server) handleBindEIP(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req struct {
		Bindings []struct {
			EipId      string `json:"eipId"`
			InstanceId string `json:"instanceId"`
		} `json:"bindings"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "解析请求数据失败: " + err.Error()})
		return
	}

	if len(req.Bindings) == 0 {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "请提供要绑定的EIP和实例"})
		return
	}

	// 检查环境变量
	if os.Getenv("ACCESS_KEY_ID") == "" || os.Getenv("ACCESS_KEY_SECRET") == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "请设置环境变量 ACCESS_KEY_ID 和 ACCESS_KEY_SECRET"})
		return
	}

	// 创建VPC客户端
	vpcClient, err := client.NewVPCClient("cn-hongkong") // 默认地域
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "创建VPC客户端失败: " + err.Error()})
		return
	}

	// 创建EIP管理器
	eipManager := ecs.NewEIPManager(vpcClient.GetClient())

	// 批量绑定EIP
	failedBindings := make([]string, 0)
	for _, binding := range req.Bindings {
		err := eipManager.AssociateEIP(binding.EipId, binding.InstanceId)
		if err != nil {
			failedBindings = append(failedBindings, fmt.Sprintf("%s->%s", binding.EipId, binding.InstanceId))
		}
	}

	if len(failedBindings) > 0 {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   fmt.Sprintf("部分绑定失败: %v", failedBindings),
		})
		return
	}

	s.sendJSONResponse(w, APIResponse{
		Success: true,
		Message: fmt.Sprintf("成功绑定 %d 个弹性IP", len(req.Bindings)),
	})
}

// handleUnbindEIP 处理解绑弹性IP请求
func (s *Server) handleUnbindEIP(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req struct {
		EipIds []string `json:"eipIds"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "解析请求数据失败: " + err.Error()})
		return
	}

	if len(req.EipIds) == 0 {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "请提供要解绑的EIP ID"})
		return
	}

	// 检查环境变量
	if os.Getenv("ACCESS_KEY_ID") == "" || os.Getenv("ACCESS_KEY_SECRET") == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "请设置环境变量 ACCESS_KEY_ID 和 ACCESS_KEY_SECRET"})
		return
	}

	// 创建VPC客户端
	vpcClient, err := client.NewVPCClient("cn-hongkong") // 默认地域
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "创建VPC客户端失败: " + err.Error()})
		return
	}

	// 创建EIP管理器
	eipManager := ecs.NewEIPManager(vpcClient.GetClient())

	// 批量解绑EIP
	failedEIPs := make([]string, 0)
	for _, eipId := range req.EipIds {
		err := eipManager.DisassociateEIP(eipId)
		if err != nil {
			failedEIPs = append(failedEIPs, eipId)
		}
	}

	if len(failedEIPs) > 0 {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   fmt.Sprintf("部分EIP解绑失败: %v", failedEIPs),
		})
		return
	}

	s.sendJSONResponse(w, APIResponse{
		Success: true,
		Message: fmt.Sprintf("成功解绑 %d 个弹性IP", len(req.EipIds)),
	})
}

// handleListSecurityGroups 处理查询安全组列表请求
func (s *Server) handleListSecurityGroups(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	regionId := r.URL.Query().Get("regionId")
	if regionId == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "缺少regionId参数"})
		return
	}

	// 检查环境变量
	if os.Getenv("ACCESS_KEY_ID") == "" || os.Getenv("ACCESS_KEY_SECRET") == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "请设置环境变量 ACCESS_KEY_ID 和 ACCESS_KEY_SECRET"})
		return
	}

	// 创建ECS客户端
	ecsClient, err := client.NewECSClient(regionId)
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "创建ECS客户端失败: " + err.Error()})
		return
	}

	// 查询安全组列表
	request := &ecs.DescribeSecurityGroupsRequest{
		RegionId: tea.String(regionId),
		PageSize: tea.Int32(100),
	}

	response, err := ecsClient.GetClient().DescribeSecurityGroups(request)
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "查询安全组列表失败: " + err.Error()})
		return
	}

	// 转换数据格式
	securityGroups := make([]map[string]interface{}, 0)
	if response.Body.SecurityGroups != nil {
		for _, sg := range response.Body.SecurityGroups.SecurityGroup {
			sgInfo := map[string]interface{}{
				"securityGroupId":   tea.StringValue(sg.SecurityGroupId),
				"securityGroupName": tea.StringValue(sg.SecurityGroupName),
				"description":       tea.StringValue(sg.Description),
				"vpcId":             tea.StringValue(sg.VpcId),
				"securityGroupType": tea.StringValue(sg.SecurityGroupType),
			}
			securityGroups = append(securityGroups, sgInfo)
		}
	}

	s.sendJSONResponse(w, APIResponse{
		Success: true,
		Data:    securityGroups,
		Message: fmt.Sprintf("成功获取 %d 个安全组", len(securityGroups)),
	})
}

// handleListVSwitches 处理查询虚拟交换机列表请求
func (s *Server) handleListVSwitches(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	regionId := r.URL.Query().Get("regionId")
	if regionId == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "缺少regionId参数"})
		return
	}

	// 检查环境变量
	if os.Getenv("ACCESS_KEY_ID") == "" || os.Getenv("ACCESS_KEY_SECRET") == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "请设置环境变量 ACCESS_KEY_ID 和 ACCESS_KEY_SECRET"})
		return
	}

	// 创建VPC客户端
	vpcClient, err := client.NewVPCClient(regionId)
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "创建VPC客户端失败: " + err.Error()})
		return
	}

	// 查询虚拟交换机列表
	request := &vpc.DescribeVSwitchesRequest{
		RegionId: tea.String(regionId),
		PageSize: tea.Int32(100),
	}

	response, err := vpcClient.GetClient().DescribeVSwitches(request)
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "查询虚拟交换机列表失败: " + err.Error()})
		return
	}

	// 转换数据格式
	vSwitches := make([]map[string]interface{}, 0)
	if response.Body.VSwitches != nil {
		for _, vs := range response.Body.VSwitches.VSwitch {
			vsInfo := map[string]interface{}{
				"vSwitchId":   tea.StringValue(vs.VSwitchId),
				"vSwitchName": tea.StringValue(vs.VSwitchName),
				"description": tea.StringValue(vs.Description),
				"vpcId":       tea.StringValue(vs.VpcId),
				"cidrBlock":   tea.StringValue(vs.CidrBlock),
				"zoneId":      tea.StringValue(vs.ZoneId),
				"status":      tea.StringValue(vs.Status),
			}
			vSwitches = append(vSwitches, vsInfo)
		}
	}

	s.sendJSONResponse(w, APIResponse{
		Success: true,
		Data:    vSwitches,
		Message: fmt.Sprintf("成功获取 %d 个虚拟交换机", len(vSwitches)),
	})
}

// HTTPServerRequest HTTP服务器请求
type HTTPServerRequest struct {
	RegionId   string `json:"regionId"`
	InstanceId string `json:"instanceId"`
	Port       int    `json:"port"`
	Directory  string `json:"directory"`
}

// DownloadViaHTTPRequest 通过HTTP下载请求
type DownloadViaHTTPRequest struct {
	InstanceIP string `json:"instanceIP"`
	Port       int    `json:"port"`
	FilePath   string `json:"filePath"`
	InstanceId string `json:"instanceId"`
}

// DownloadFileRequest 下载文件请求
type DownloadFileRequest struct {
	RegionId    string `json:"regionId"`
	InstanceId  string `json:"instanceId"`
	RemotePath  string `json:"remotePath"`
	LocalPath   string `json:"localPath"`
	CommandType string `json:"commandType"`
}

// DownloadFileChunkedRequest 分块下载文件请求
type DownloadFileChunkedRequest struct {
	RegionId    string `json:"regionId"`
	InstanceId  string `json:"instanceId"`
	RemotePath  string `json:"remotePath"`
	LocalPath   string `json:"localPath"`
	CommandType string `json:"commandType"`
	ChunkSize   int64  `json:"chunkSize"`
	StartOffset int64  `json:"startOffset"`
}

// ProcessDownloadRequest 处理下载结果请求
type ProcessDownloadRequest struct {
	CommandOutput string `json:"commandOutput"`
	LocalPath     string `json:"localPath"`
	RemotePath    string `json:"remotePath"`
}

// BatchDownloadFileRequest 批量下载文件请求
type BatchDownloadFileRequest struct {
	RegionId    string   `json:"regionId"`
	InstanceIds []string `json:"instanceIds"`
	RemotePath  string   `json:"remotePath"`
	LocalDir    string   `json:"localDir"`
	CommandType string   `json:"commandType"`
}

// handleStartHTTPServer 启动ECS实例上的HTTP文件服务器
func (s *Server) handleStartHTTPServer(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "只支持POST请求",
		})
		return
	}

	var request HTTPServerRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "请求参数解析失败: " + err.Error(),
		})
		return
	}

	// 验证必填参数
	if request.RegionId == "" || request.InstanceId == "" {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "缺少必填参数",
		})
		return
	}

	// 设置默认值
	if request.Port == 0 {
		request.Port = 8000
	}
	if request.Directory == "" {
		request.Directory = "/tmp"
	}

	log.Printf("🌐 启动HTTP服务器: 实例 %s，端口 %d，目录 %s", request.InstanceId, request.Port, request.Directory)

	log.Printf("🌐 启动HTTP服务器: 实例 %s，端口 %d，目录 %s",
		request.InstanceId, request.Port, request.Directory)

	// 检查环境变量
	if os.Getenv("ACCESS_KEY_ID") == "" || os.Getenv("ACCESS_KEY_SECRET") == "" {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "请设置环境变量 ACCESS_KEY_ID 和 ACCESS_KEY_SECRET",
		})
		return
	}

	// 创建ECS客户端
	ecsClient, err := client.NewECSClient(request.RegionId)
	if err != nil {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "创建ECS客户端失败: " + err.Error(),
		})
		return
	}

	// 创建实例管理器
	instanceManager := ecs.NewInstanceManager(ecsClient.GetClient())

	// 构建HTTP服务器启动命令
	startCommand := s.buildHTTPServerStartCommand(request.Port, request.Directory)

	// 通过云助手执行启动命令
	response, err := instanceManager.RunCommand(request.RegionId, []string{request.InstanceId}, startCommand, "RunShellScript")
	if err != nil {
		log.Printf("❌ HTTP服务器启动命令执行失败: %v", err)
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "HTTP服务器启动命令执行失败: " + err.Error(),
		})
		return
	}

	invokeId := *response.Body.InvokeId
	log.Printf("✅ HTTP服务器启动命令已发送，执行ID: %s", invokeId)

	s.sendJSONResponse(w, APIResponse{
		Success: true,
		Message: "HTTP服务器启动命令已发送",
		Data: map[string]interface{}{
			"invokeId":   invokeId,
			"instanceId": request.InstanceId,
			"port":       request.Port,
			"directory":  request.Directory,
		},
	})
}

// handleStopHTTPServer 停止ECS实例上的HTTP文件服务器
func (s *Server) handleStopHTTPServer(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "只支持POST请求",
		})
		return
	}

	var request struct {
		RegionId   string `json:"regionId"`
		InstanceId string `json:"instanceId"`
		Port       int    `json:"port"`
	}

	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "请求参数解析失败: " + err.Error(),
		})
		return
	}

	// 验证必填参数
	if request.RegionId == "" || request.InstanceId == "" {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "缺少必填参数",
		})
		return
	}

	if request.Port == 0 {
		request.Port = 8000
	}

	log.Printf("🛑 停止HTTP服务器: 实例 %s，端口 %d", request.InstanceId, request.Port)

	// 检查环境变量
	if os.Getenv("ACCESS_KEY_ID") == "" || os.Getenv("ACCESS_KEY_SECRET") == "" {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "请设置环境变量 ACCESS_KEY_ID 和 ACCESS_KEY_SECRET",
		})
		return
	}

	// 创建ECS客户端
	ecsClient, err := client.NewECSClient(request.RegionId)
	if err != nil {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "创建ECS客户端失败: " + err.Error(),
		})
		return
	}

	// 创建实例管理器
	instanceManager := ecs.NewInstanceManager(ecsClient.GetClient())

	// 构建HTTP服务器停止命令
	stopCommand := s.buildHTTPServerStopCommand(request.Port)

	// 通过云助手执行停止命令
	response, err := instanceManager.RunCommand(request.RegionId, []string{request.InstanceId}, stopCommand, "RunShellScript")
	if err != nil {
		log.Printf("❌ HTTP服务器停止命令执行失败: %v", err)
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "HTTP服务器停止命令执行失败: " + err.Error(),
		})
		return
	}

	invokeId := *response.Body.InvokeId
	log.Printf("✅ HTTP服务器停止命令已发送，执行ID: %s", invokeId)

	s.sendJSONResponse(w, APIResponse{
		Success: true,
		Message: "HTTP服务器停止命令已发送",
		Data: map[string]interface{}{
			"invokeId": invokeId,
		},
	})
}

// DownloadViaHTTPRequest 通过HTTP下载请求
type DownloadViaHTTPRequestExtended struct {
	InstanceIP   string `json:"instanceIP"`
	Port         int    `json:"port"`
	FilePath     string `json:"filePath"`
	InstanceId   string `json:"instanceId"`
	InstanceName string `json:"instanceName"`
	RegionId     string `json:"regionId"`
	Directory    string `json:"directory"`
	CopyOnly     bool   `json:"copyOnly"` // 只复制文件，不下载
}

// handleDownloadViaHTTP 通过HTTP协议下载文件
func (s *Server) handleDownloadViaHTTP(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "只支持POST请求",
		})
		return
	}

	var request DownloadViaHTTPRequestExtended
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "请求参数解析失败: " + err.Error(),
		})
		return
	}

	// 验证必填参数
	if request.InstanceIP == "" || request.FilePath == "" || request.InstanceId == "" || request.RegionId == "" {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "缺少必填参数",
		})
		return
	}

	if request.Port == 0 {
		request.Port = 8000
	}
	if request.Directory == "" {
		request.Directory = "/tmp"
	}

	log.Printf("📥 通过HTTP下载文件: %s:%d%s", request.InstanceIP, request.Port, request.FilePath)

	// 1. 先复制文件到HTTP服务器目录
	var copyCommand string

	// HTTP服务器中使用原始文件名（不带前缀）
	httpFileName := filepath.Base(request.FilePath)

	// 检查是否使用通配符
	if strings.Contains(request.FilePath, "*") || strings.Contains(request.FilePath, "?") || strings.Contains(request.FilePath, "[") {
		// 通配符模式：直接复制多个文件
		copyCommand = s.buildWildcardFileCopyCommand(request.FilePath, request.Directory)
	} else {
		// 单个文件模式：使用原始文件名
		copyCommand = s.buildFileCopyCommand(request.FilePath, request.Directory, httpFileName)
	}

	// 检查环境变量
	if os.Getenv("ACCESS_KEY_ID") == "" || os.Getenv("ACCESS_KEY_SECRET") == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "请设置环境变量 ACCESS_KEY_ID 和 ACCESS_KEY_SECRET"})
		return
	}

	// 创建ECS客户端
	ecsClient, err := client.NewECSClient(request.RegionId)
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "创建ECS客户端失败: " + err.Error()})
		return
	}

	// 创建实例管理器
	instanceManager := ecs.NewInstanceManager(ecsClient.GetClient())

	// 执行文件复制命令
	_, err = instanceManager.RunCommand(request.RegionId, []string{request.InstanceId}, copyCommand, "RunShellScript")
	if err != nil {
		log.Printf("❌ 文件复制命令执行失败: %v", err)
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "文件复制失败: " + err.Error(),
		})
		return
	}

	// 等待文件复制完成
	time.Sleep(2 * time.Second)

	// 2. 检查是否使用通配符
	if strings.Contains(request.FilePath, "*") || strings.Contains(request.FilePath, "?") || strings.Contains(request.FilePath, "[") {
		// 通配符模式：返回特殊响应，让前端处理循环下载
		s.sendJSONResponse(w, APIResponse{
			Success: true,
			Message: "wildcard_download_ready",
			Data: map[string]interface{}{
				"serverURL": fmt.Sprintf("http://%s:%d/", request.InstanceIP, request.Port),
				"pattern":   request.FilePath,
			},
		})
		return
	}

	// 如果只是复制文件，不需要下载，直接返回成功响应
	if request.CopyOnly {
		s.sendJSONResponse(w, APIResponse{
			Success: true,
			Message: "文件复制成功",
			Data: map[string]interface{}{
				"filePath":     request.FilePath,
				"httpFileName": httpFileName,
				"downloadURL":  fmt.Sprintf("http://%s:%d/%s", request.InstanceIP, request.Port, httpFileName),
			},
		})
		log.Printf("✅ 文件复制成功: %s -> %s", request.FilePath, httpFileName)
		return
	}

	// 3. 构建HTTP下载URL（使用原始文件名）
	downloadURL := fmt.Sprintf("http://%s:%d/%s", request.InstanceIP, request.Port, httpFileName)

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 发送HTTP请求
	resp, err := client.Get(downloadURL)
	if err != nil {
		log.Printf("❌ HTTP下载失败: %v", err)
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "HTTP下载失败: " + err.Error(),
		})
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   fmt.Sprintf("HTTP下载失败，状态码: %d", resp.StatusCode),
		})
		return
	}

	// 读取文件内容
	fileContent, err := io.ReadAll(resp.Body)
	if err != nil {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "读取文件内容失败: " + err.Error(),
		})
		return
	}

	// 设置响应头
	downloadFileName := filepath.Base(request.FilePath)
	if request.InstanceName != "" {
		downloadFileName = fmt.Sprintf("%s_%s", request.InstanceName, downloadFileName)
	} else if request.InstanceId != "" {
		downloadFileName = fmt.Sprintf("%s_%s", request.InstanceId, downloadFileName)
	}

	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", downloadFileName))
	w.Header().Set("Content-Type", "application/octet-stream")
	w.Header().Set("Content-Length", fmt.Sprintf("%d", len(fileContent)))

	// 返回文件内容
	w.Write(fileContent)
	log.Printf("✅ 文件下载成功: %s (%d bytes)", downloadFileName, len(fileContent))
}

// buildHTTPServerStartCommand 构建HTTP服务器启动命令
func (s *Server) buildHTTPServerStartCommand(port int, directory string) string {
	return fmt.Sprintf(`#!/bin/bash

# HTTP文件服务器启动脚本
echo "=== 启动HTTP文件服务器 ==="
echo "端口: %d"
echo "目录: %s"

# 复制系统文件到HTTP服务器目录，以便可以访问
echo "� 复制系统文件到HTTP服务器目录..."
mkdir -p "%s/etc" "%s/var/log" 2>/dev/null

# 复制常用系统文件
if [ -f "/etc/hosts" ]; then
    cp "/etc/hosts" "%s/etc/hosts" 2>/dev/null && echo "✅ 复制 /etc/hosts"
fi
if [ -f "/etc/hostname" ]; then
    cp "/etc/hostname" "%s/etc/hostname" 2>/dev/null && echo "✅ 复制 /etc/hostname"
fi
if [ -f "/etc/passwd" ]; then
    cp "/etc/passwd" "%s/etc/passwd" 2>/dev/null && echo "✅ 复制 /etc/passwd"
fi
if [ -f "/etc/resolv.conf" ]; then
    cp "/etc/resolv.conf" "%s/etc/resolv.conf" 2>/dev/null && echo "✅ 复制 /etc/resolv.conf"
fi
if [ -f "/etc/fstab" ]; then
    cp "/etc/fstab" "%s/etc/fstab" 2>/dev/null && echo "✅ 复制 /etc/fstab"
fi

# 复制日志文件（如果存在且可读）
if [ -f "/var/log/messages" ] && [ -r "/var/log/messages" ]; then
    cp "/var/log/messages" "%s/var/log/messages" 2>/dev/null && echo "✅ 复制 /var/log/messages"
fi

echo "✅ 系统文件复制完成"

# 检查端口是否被占用
check_port() {
    local port=$1
    if netstat -tuln 2>/dev/null | grep -q ":$port "; then
        return 1  # 端口被占用
    fi
    if ss -tuln 2>/dev/null | grep -q ":$port "; then
        return 1  # 端口被占用
    fi
    return 0  # 端口可用
}

# 查找可用端口
find_available_port() {
    local start_port=$1
    local port=$start_port

    while [ $port -le $((start_port + 100)) ]; do
        if check_port $port; then
            echo $port
            return 0
        fi
        port=$((port + 1))
    done

    echo "0"  # 未找到可用端口
    return 1
}

# 检查Python3是否可用
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    echo "❌ 未找到Python，正在尝试安装..."
    if command -v yum &> /dev/null; then
        yum install -y python3
        PYTHON_CMD="python3"
    elif command -v apt-get &> /dev/null; then
        apt-get update && apt-get install -y python3
        PYTHON_CMD="python3"
    else
        echo "❌ 无法自动安装Python"
        exit 1
    fi
fi

echo "✅ 使用Python命令: $PYTHON_CMD"

# 切换到指定目录
cd "%s" || {
    echo "❌ 无法切换到目录: %s"
    exit 1
}

echo "📁 当前工作目录: $(pwd)"

# 查找可用端口
AVAILABLE_PORT=$(find_available_port %d)
if [ "$AVAILABLE_PORT" = "0" ]; then
    echo "❌ 无法找到可用端口（尝试范围: %d-%d）"
    exit 1
fi

echo "🌐 使用端口: $AVAILABLE_PORT"

# 启动HTTP服务器
echo "🚀 启动HTTP文件服务器..."
echo "访问地址: http://$(hostname -I | awk '{print $1}'):$AVAILABLE_PORT"
echo "=== HTTP_SERVER_STARTED ==="
echo "PORT:$AVAILABLE_PORT"
echo "DIRECTORY:%s"
echo "=== HTTP_SERVER_INFO_END ==="

# 切换到指定目录并启动服务器（后台运行）
cd "%s"
nohup $PYTHON_CMD -m http.server $AVAILABLE_PORT > /tmp/http_server_$AVAILABLE_PORT.log 2>&1 &
HTTP_PID=$!

echo "✅ HTTP服务器已启动"
echo "PID: $HTTP_PID"
echo "日志文件: /tmp/http_server_$AVAILABLE_PORT.log"

# 等待服务器启动
sleep 2

# 检查服务器是否正常运行
if kill -0 $HTTP_PID 2>/dev/null; then
    echo "✅ HTTP服务器运行正常"
    echo "=== 启动完成 ==="
else
    echo "❌ HTTP服务器启动失败"
    exit 1
fi`, port, directory, directory, directory, directory, directory, directory, directory, directory, directory, directory, directory, directory, port, port, port+100, directory, directory)
}

// buildHTTPServerStopCommand 构建HTTP服务器停止命令
func (s *Server) buildHTTPServerStopCommand(port int) string {
	return fmt.Sprintf(`#!/bin/bash

# HTTP文件服务器停止脚本
echo "=== 停止HTTP文件服务器 ==="
echo "端口: %d"

# 查找并停止HTTP服务器进程
echo "🔍 查找HTTP服务器进程..."

# 查找监听指定端口的Python进程
HTTP_PIDS=$(lsof -ti:%d 2>/dev/null | grep -v "^$")

if [ -z "$HTTP_PIDS" ]; then
    # 如果lsof不可用，尝试使用netstat和ps
    HTTP_PIDS=$(netstat -tlnp 2>/dev/null | grep ":%d " | awk '{print $7}' | cut -d'/' -f1 | grep -v "^-$")
fi

if [ -z "$HTTP_PIDS" ]; then
    echo "⚠️ 未找到监听端口 %d 的进程"
    echo "=== 停止完成 ==="
    exit 0
fi

echo "📋 找到进程: $HTTP_PIDS"

# 停止所有相关进程
for pid in $HTTP_PIDS; do
    if [ -n "$pid" ] && [ "$pid" != "-" ]; then
        echo "🛑 停止进程 $pid..."
        kill $pid 2>/dev/null

        # 等待进程停止
        sleep 1

        # 如果进程仍在运行，强制停止
        if kill -0 $pid 2>/dev/null; then
            echo "⚡ 强制停止进程 $pid..."
            kill -9 $pid 2>/dev/null
        fi

        echo "✅ 进程 $pid 已停止"
    fi
done

# 清理日志文件
LOG_FILE="/tmp/http_server_%d.log"
if [ -f "$LOG_FILE" ]; then
    rm -f "$LOG_FILE"
    echo "🗑️ 已清理日志文件: $LOG_FILE"
fi

echo "✅ HTTP服务器已停止"
echo "=== 停止完成 ==="`, port, port, port, port, port)
}

// handleDownloadFile 处理文件下载请求
func (s *Server) handleDownloadFile(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "只支持POST请求",
		})
		return
	}

	var request DownloadFileRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "请求参数解析失败: " + err.Error(),
		})
		return
	}

	// 验证必填参数
	if request.RegionId == "" || request.InstanceId == "" || request.RemotePath == "" {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "缺少必填参数",
		})
		return
	}

	log.Printf("📥 开始下载文件: 实例=%s, 远程路径=%s, 本地路径=%s",
		request.InstanceId, request.RemotePath, request.LocalPath)

	// 构建下载命令
	var command string
	switch request.CommandType {
	case "RunPowerShellScript":
		// Windows PowerShell命令
		command = fmt.Sprintf(`
# 文件下载脚本
$remotePath = "%s"
$localPath = "%s"

try {
    if (Test-Path $remotePath) {
        $fileInfo = Get-Item $remotePath
        Write-Host "文件信息:"
        Write-Host "路径: $($fileInfo.FullName)"
        Write-Host "大小: $($fileInfo.Length) 字节"
        Write-Host "修改时间: $($fileInfo.LastWriteTime)"

        # 读取文件内容并进行Base64编码
        $bytes = [System.IO.File]::ReadAllBytes($remotePath)
        $base64 = [System.Convert]::ToBase64String($bytes)

        Write-Host "DOWNLOAD_START"
        Write-Host $base64
        Write-Host "DOWNLOAD_END"
        Write-Host "✅ 文件读取成功"
    } else {
        Write-Host "❌ 文件不存在: $remotePath"
        exit 1
    }
} catch {
    Write-Host "❌ 文件下载失败: $($_.Exception.Message)"
    exit 1
}
`, request.RemotePath, request.LocalPath)
	default:
		// Linux Shell命令（安全目录方案）
		command = fmt.Sprintf(`#!/bin/bash
# 安全文件下载脚本
remotePath="%s"
localPath="%s"

echo "尝试访问文件: $remotePath"

# 定义安全下载目录
SAFE_DOWNLOAD_DIR="/tmp/safe_downloads"
TEMP_FILE="$SAFE_DOWNLOAD_DIR/$(basename "$remotePath")"

# 创建安全下载目录
mkdir -p "$SAFE_DOWNLOAD_DIR"
chmod 755 "$SAFE_DOWNLOAD_DIR"

# 检查源文件是否存在
if [ -e "$remotePath" ]; then
    USE_SUDO=false
    echo "💡 文件可通过普通权限访问"
elif sudo test -e "$remotePath" 2>/dev/null; then
    USE_SUDO=true
    echo "💡 文件需要特殊权限，将使用sudo"
else
    echo "❌ 文件不存在或无法访问: $remotePath"
    echo "💡 可能的原因："
    echo "   1. 文件路径错误"
    echo "   2. 文件不存在"
    echo "   3. 权限不足"

    # 尝试列出父目录内容以帮助调试
    PARENT_DIR=$(dirname "$remotePath")
    echo "🔍 父目录内容（用于调试）:"
    ls -la "$PARENT_DIR" 2>/dev/null | head -5 || sudo ls -la "$PARENT_DIR" 2>/dev/null | head -5 || echo "无法列出父目录内容"
    exit 1
fi

# 检查是否为文件
if [ "$USE_SUDO" = "true" ]; then
    if ! sudo test -f "$remotePath"; then
        echo "❌ 路径存在但不是文件: $remotePath"
        exit 1
    fi
else
    if [ ! -f "$remotePath" ]; then
        echo "❌ 路径存在但不是文件: $remotePath"
        exit 1
    fi
fi

# 步骤1: 复制文件到安全目录
echo "📋 步骤1: 复制文件到安全目录..."
if [ "$USE_SUDO" = "true" ]; then
    if sudo cp "$remotePath" "$TEMP_FILE"; then
        sudo chown $(whoami):$(whoami) "$TEMP_FILE" 2>/dev/null || true
        sudo chmod 644 "$TEMP_FILE"
        echo "✅ 使用sudo成功复制到安全目录"
    else
        echo "❌ sudo复制失败"
        exit 1
    fi
else
    if cp "$remotePath" "$TEMP_FILE"; then
        chmod 644 "$TEMP_FILE"
        echo "✅ 成功复制到安全目录"
    else
        echo "❌ 复制到安全目录失败"
        exit 1
    fi
fi

# 步骤2: 读取文件内容
echo "📋 步骤2: 读取文件内容..."
if [ -f "$TEMP_FILE" ]; then
    echo "文件信息:"
    ls -la "$TEMP_FILE"

    # 获取文件大小
    FILE_SIZE=$(stat -c%%s "$TEMP_FILE" 2>/dev/null || echo "未知")
    echo "文件大小: $FILE_SIZE 字节"

    echo "DOWNLOAD_START"
    if base64 "$TEMP_FILE"; then
        echo "DOWNLOAD_END"
        echo "✅ 文件读取成功"
    else
        echo "DOWNLOAD_END"
        echo "❌ 文件读取失败"
        exit 1
    fi

    # 清理临时文件
    rm -f "$TEMP_FILE"
else
    echo "❌ 临时文件不存在"
    exit 1
fi
`, request.RemotePath, request.LocalPath)
	}

	// 检查环境变量
	if os.Getenv("ACCESS_KEY_ID") == "" || os.Getenv("ACCESS_KEY_SECRET") == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "请设置环境变量 ACCESS_KEY_ID 和 ACCESS_KEY_SECRET"})
		return
	}

	// 创建ECS客户端
	ecsClient, err := client.NewECSClient(request.RegionId)
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "创建ECS客户端失败: " + err.Error()})
		return
	}

	// 创建实例管理器
	instanceManager := ecs.NewInstanceManager(ecsClient.GetClient())

	// 执行命令
	response, err := instanceManager.RunCommand(request.RegionId, []string{request.InstanceId}, command, request.CommandType)
	if err != nil {
		log.Printf("❌ 执行下载命令失败: %v", err)
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "执行下载命令失败: " + err.Error(),
		})
		return
	}

	log.Printf("✅ 下载命令已发送，InvokeId: %s", *response.Body.InvokeId)
	s.sendJSONResponse(w, APIResponse{
		Success: true,
		Message: "文件下载命令已发送",
		Data: map[string]interface{}{
			"invokeId":   *response.Body.InvokeId,
			"localPath":  request.LocalPath,
			"remotePath": request.RemotePath,
		},
	})
}

// handleDownloadFileChunked 处理分块文件下载请求
func (s *Server) handleDownloadFileChunked(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "只支持POST请求",
		})
		return
	}

	var request DownloadFileChunkedRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "请求参数解析失败: " + err.Error(),
		})
		return
	}

	// 验证必填参数
	if request.RegionId == "" || request.InstanceId == "" || request.RemotePath == "" {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "缺少必填参数",
		})
		return
	}

	// 设置默认分块大小
	if request.ChunkSize == 0 {
		request.ChunkSize = 1024 * 1024 // 1MB
	}

	log.Printf("📥 开始分块下载文件: 实例=%s, 远程路径=%s, 偏移量=%d, 分块大小=%d",
		request.InstanceId, request.RemotePath, request.StartOffset, request.ChunkSize)

	// 构建分块下载命令
	var command string
	if request.StartOffset == 0 {
		// 第一次请求，获取文件信息
		switch request.CommandType {
		case "RunPowerShellScript":
			command = fmt.Sprintf(`
# 分块文件下载脚本 - 获取文件信息
$remotePath = "%s"
$chunkSize = %d

try {
    if (Test-Path $remotePath) {
        $fileInfo = Get-Item $remotePath
        $fileSize = $fileInfo.Length
        $totalChunks = [Math]::Ceiling($fileSize / $chunkSize)

        Write-Host "FILE_INFO_START"
        Write-Host "FileSize:$fileSize"
        Write-Host "ChunkSize:$chunkSize"
        Write-Host "TotalChunks:$totalChunks"
        Write-Host "FileName:$($fileInfo.Name)"
        Write-Host "FILE_INFO_END"

        # 读取第一个分块
        $bytes = New-Object byte[] $chunkSize
        $stream = [System.IO.File]::OpenRead($remotePath)
        $bytesRead = $stream.Read($bytes, 0, $chunkSize)
        $stream.Close()

        if ($bytesRead -gt 0) {
            $chunkData = $bytes[0..($bytesRead-1)]
            $base64 = [System.Convert]::ToBase64String($chunkData)

            Write-Host "CHUNK_START"
            Write-Host $base64
            Write-Host "CHUNK_END"
        }

        Write-Host "✅ 文件信息获取成功"
    } else {
        Write-Host "❌ 文件不存在: $remotePath"
        exit 1
    }
} catch {
    Write-Host "❌ 分块下载失败: $($_.Exception.Message)"
    exit 1
}
`, request.RemotePath, request.ChunkSize)
		default:
			command = fmt.Sprintf(`#!/bin/bash
# 分块文件下载脚本 - 获取文件信息
remotePath="%s"
chunkSize=%d

if [ -f "$remotePath" ]; then
    fileSize=$(stat -c%%s "$remotePath")
    totalChunks=$(( (fileSize + chunkSize - 1) / chunkSize ))
    fileName=$(basename "$remotePath")

    echo "FILE_INFO_START"
    echo "FileSize:$fileSize"
    echo "ChunkSize:$chunkSize"
    echo "TotalChunks:$totalChunks"
    echo "FileName:$fileName"
    echo "FILE_INFO_END"

    # 读取第一个分块
    echo "CHUNK_START"
    dd if="$remotePath" bs=$chunkSize count=1 2>/dev/null | base64 -w 0
    echo ""
    echo "CHUNK_END"

    echo "✅ 文件信息获取成功"
else
    echo "❌ 文件不存在: $remotePath"
    exit 1
fi
`, request.RemotePath, request.ChunkSize)
		}
	} else {
		// 后续请求，只读取指定分块
		switch request.CommandType {
		case "RunPowerShellScript":
			command = fmt.Sprintf(`
# 分块文件下载脚本 - 读取指定分块
$remotePath = "%s"
$startOffset = %d
$chunkSize = %d

try {
    if (Test-Path $remotePath) {
        $bytes = New-Object byte[] $chunkSize
        $stream = [System.IO.File]::OpenRead($remotePath)
        $stream.Seek($startOffset, [System.IO.SeekOrigin]::Begin) | Out-Null
        $bytesRead = $stream.Read($bytes, 0, $chunkSize)
        $stream.Close()

        if ($bytesRead -gt 0) {
            $chunkData = $bytes[0..($bytesRead-1)]
            $base64 = [System.Convert]::ToBase64String($chunkData)

            Write-Host "CHUNK_START"
            Write-Host $base64
            Write-Host "CHUNK_END"
        }

        Write-Host "✅ 分块读取成功"
    } else {
        Write-Host "❌ 文件不存在: $remotePath"
        exit 1
    }
} catch {
    Write-Host "❌ 分块下载失败: $($_.Exception.Message)"
    exit 1
}
`, request.RemotePath, request.StartOffset, request.ChunkSize)
		default:
			command = fmt.Sprintf(`#!/bin/bash
# 分块文件下载脚本 - 读取指定分块
remotePath="%s"
startOffset=%d
chunkSize=%d

if [ -f "$remotePath" ]; then
    echo "CHUNK_START"
    dd if="$remotePath" bs=1 skip=$startOffset count=$chunkSize 2>/dev/null | base64 -w 0
    echo ""
    echo "CHUNK_END"

    echo "✅ 分块读取成功"
else
    echo "❌ 文件不存在: $remotePath"
    exit 1
fi
`, request.RemotePath, request.StartOffset, request.ChunkSize)
		}
	}

	// 检查环境变量
	if os.Getenv("ACCESS_KEY_ID") == "" || os.Getenv("ACCESS_KEY_SECRET") == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "请设置环境变量 ACCESS_KEY_ID 和 ACCESS_KEY_SECRET"})
		return
	}

	// 创建ECS客户端
	ecsClient, err := client.NewECSClient(request.RegionId)
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "创建ECS客户端失败: " + err.Error()})
		return
	}

	// 创建实例管理器
	instanceManager := ecs.NewInstanceManager(ecsClient.GetClient())

	// 执行命令
	response, err := instanceManager.RunCommand(request.RegionId, []string{request.InstanceId}, command, request.CommandType)
	if err != nil {
		log.Printf("❌ 执行分块下载命令失败: %v", err)
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "执行分块下载命令失败: " + err.Error(),
		})
		return
	}

	log.Printf("✅ 分块下载命令已发送，InvokeId: %s", *response.Body.InvokeId)
	s.sendJSONResponse(w, APIResponse{
		Success: true,
		Message: "分块下载命令已发送",
		Data: map[string]interface{}{
			"invokeId":    *response.Body.InvokeId,
			"localPath":   request.LocalPath,
			"remotePath":  request.RemotePath,
			"startOffset": request.StartOffset,
			"chunkSize":   request.ChunkSize,
		},
	})
}

// handleProcessDownload 处理下载结果
func (s *Server) handleProcessDownload(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "只支持POST请求",
		})
		return
	}

	var request ProcessDownloadRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "请求参数解析失败: " + err.Error(),
		})
		return
	}

	// 验证必填参数
	if request.CommandOutput == "" || request.LocalPath == "" || request.RemotePath == "" {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "缺少必填参数",
		})
		return
	}

	log.Printf("📥 处理下载结果: 本地路径=%s, 远程路径=%s", request.LocalPath, request.RemotePath)

	// 解析命令输出，提取Base64编码的文件内容
	lines := strings.Split(request.CommandOutput, "\n")
	var base64Content strings.Builder
	inDownloadSection := false

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "DOWNLOAD_START" || line == "CHUNK_START" {
			inDownloadSection = true
			continue
		}
		if line == "DOWNLOAD_END" || line == "CHUNK_END" {
			inDownloadSection = false
			continue
		}
		if inDownloadSection && line != "" {
			base64Content.WriteString(line)
		}
	}

	if base64Content.Len() == 0 {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "未找到文件内容",
		})
		return
	}

	// 解码Base64内容
	fileData, err := base64.StdEncoding.DecodeString(base64Content.String())
	if err != nil {
		log.Printf("❌ Base64解码失败: %v", err)
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "文件内容解码失败: " + err.Error(),
		})
		return
	}

	// 确保本地目录存在
	localDir := filepath.Dir(request.LocalPath)
	if err := os.MkdirAll(localDir, 0755); err != nil {
		log.Printf("❌ 创建本地目录失败: %v", err)
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "创建本地目录失败: " + err.Error(),
		})
		return
	}

	// 写入文件
	if err := os.WriteFile(request.LocalPath, fileData, 0644); err != nil {
		log.Printf("❌ 写入文件失败: %v", err)
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "写入文件失败: " + err.Error(),
		})
		return
	}

	// 获取文件信息
	fileInfo, err := os.Stat(request.LocalPath)
	if err != nil {
		log.Printf("⚠️ 获取文件信息失败: %v", err)
	}

	log.Printf("✅ 文件下载成功: %s (%d bytes)", request.LocalPath, len(fileData))

	responseData := map[string]interface{}{
		"localPath": request.LocalPath,
		"fileSize":  len(fileData),
	}

	if fileInfo != nil {
		responseData["modTime"] = fileInfo.ModTime().Format("2006-01-02 15:04:05")
	}

	s.sendJSONResponse(w, APIResponse{
		Success: true,
		Message: "文件下载并保存成功",
		Data:    responseData,
	})
}

// handleBatchDownloadFile 处理批量文件下载请求
func (s *Server) handleBatchDownloadFile(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "只支持POST请求",
		})
		return
	}

	var request BatchDownloadFileRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "请求参数解析失败: " + err.Error(),
		})
		return
	}

	// 验证必填参数
	if request.RegionId == "" || len(request.InstanceIds) == 0 || request.RemotePath == "" || request.LocalDir == "" {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "缺少必填参数",
		})
		return
	}

	log.Printf("📥 开始批量下载文件: 实例数量=%d, 远程路径=%s, 本地目录=%s",
		len(request.InstanceIds), request.RemotePath, request.LocalDir)

	// 构建批量下载命令
	var command string
	switch request.CommandType {
	case "RunPowerShellScript":
		// Windows PowerShell命令
		command = fmt.Sprintf(`
# 批量文件下载脚本
$remotePath = "%s"
$hostname = $env:COMPUTERNAME

try {
    if (Test-Path $remotePath) {
        $fileInfo = Get-Item $remotePath
        Write-Host "主机名: $hostname"
        Write-Host "文件信息:"
        Write-Host "路径: $($fileInfo.FullName)"
        Write-Host "大小: $($fileInfo.Length) 字节"
        Write-Host "修改时间: $($fileInfo.LastWriteTime)"

        # 读取文件内容并进行Base64编码
        $bytes = [System.IO.File]::ReadAllBytes($remotePath)
        $base64 = [System.Convert]::ToBase64String($bytes)

        Write-Host "DOWNLOAD_START"
        Write-Host $base64
        Write-Host "DOWNLOAD_END"
        Write-Host "✅ 文件读取成功"
    } else {
        Write-Host "❌ 文件不存在: $remotePath"
        exit 1
    }
} catch {
    Write-Host "❌ 文件下载失败: $($_.Exception.Message)"
    exit 1
}
`, request.RemotePath)
	default:
		// Linux Shell命令（安全目录方案）
		command = fmt.Sprintf(`#!/bin/bash
# 安全批量文件下载脚本
remotePath="%s"
hostname=$(hostname)

echo "主机名: $hostname"
echo "尝试访问文件: $remotePath"

# 定义安全下载目录
SAFE_DOWNLOAD_DIR="/tmp/safe_downloads"
TEMP_FILE="$SAFE_DOWNLOAD_DIR/$(basename "$remotePath")"

# 创建安全下载目录
mkdir -p "$SAFE_DOWNLOAD_DIR"
chmod 755 "$SAFE_DOWNLOAD_DIR"

# 检查源文件是否存在
if [ -e "$remotePath" ]; then
    USE_SUDO=false
    echo "💡 文件可通过普通权限访问"
elif sudo test -e "$remotePath" 2>/dev/null; then
    USE_SUDO=true
    echo "💡 文件需要特殊权限，将使用sudo"
else
    echo "❌ 文件不存在或无法访问: $remotePath"
    echo "💡 可能的原因："
    echo "   1. 文件路径错误"
    echo "   2. 文件不存在"
    echo "   3. 权限不足"

    # 尝试列出父目录内容以帮助调试
    PARENT_DIR=$(dirname "$remotePath")
    echo "🔍 父目录内容（用于调试）:"
    ls -la "$PARENT_DIR" 2>/dev/null | head -5 || sudo ls -la "$PARENT_DIR" 2>/dev/null | head -5 || echo "无法列出父目录内容"
    exit 1
fi

# 检查是否为文件
if [ "$USE_SUDO" = "true" ]; then
    if ! sudo test -f "$remotePath"; then
        echo "❌ 路径存在但不是文件: $remotePath"
        exit 1
    fi
else
    if [ ! -f "$remotePath" ]; then
        echo "❌ 路径存在但不是文件: $remotePath"
        exit 1
    fi
fi

# 步骤1: 复制文件到安全目录
echo "📋 步骤1: 复制文件到安全目录..."
if [ "$USE_SUDO" = "true" ]; then
    if sudo cp "$remotePath" "$TEMP_FILE"; then
        sudo chown $(whoami):$(whoami) "$TEMP_FILE" 2>/dev/null || true
        sudo chmod 644 "$TEMP_FILE"
        echo "✅ 使用sudo成功复制到安全目录"
    else
        echo "❌ sudo复制失败"
        exit 1
    fi
else
    if cp "$remotePath" "$TEMP_FILE"; then
        chmod 644 "$TEMP_FILE"
        echo "✅ 成功复制到安全目录"
    else
        echo "❌ 复制到安全目录失败"
        exit 1
    fi
fi

# 步骤2: 读取文件内容
echo "📋 步骤2: 读取文件内容..."
if [ -f "$TEMP_FILE" ]; then
    echo "文件信息:"
    ls -la "$TEMP_FILE"

    # 获取文件大小
    FILE_SIZE=$(stat -c%%s "$TEMP_FILE" 2>/dev/null || echo "未知")
    echo "文件大小: $FILE_SIZE 字节"

    echo "DOWNLOAD_START"
    if base64 "$TEMP_FILE"; then
        echo "DOWNLOAD_END"
        echo "✅ 文件读取成功"
    else
        echo "DOWNLOAD_END"
        echo "❌ 文件读取失败"
        exit 1
    fi

    # 清理临时文件
    rm -f "$TEMP_FILE"
else
    echo "❌ 临时文件不存在"
    exit 1
fi
`, request.RemotePath)
	}

	// 检查环境变量
	if os.Getenv("ACCESS_KEY_ID") == "" || os.Getenv("ACCESS_KEY_SECRET") == "" {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "请设置环境变量 ACCESS_KEY_ID 和 ACCESS_KEY_SECRET"})
		return
	}

	// 创建ECS客户端
	ecsClient, err := client.NewECSClient(request.RegionId)
	if err != nil {
		s.sendJSONResponse(w, APIResponse{Success: false, Error: "创建ECS客户端失败: " + err.Error()})
		return
	}

	// 创建实例管理器
	instanceManager := ecs.NewInstanceManager(ecsClient.GetClient())

	// 执行批量命令
	response, err := instanceManager.RunCommand(request.RegionId, request.InstanceIds, command, request.CommandType)
	if err != nil {
		log.Printf("❌ 执行批量下载命令失败: %v", err)
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "执行批量下载命令失败: " + err.Error(),
		})
		return
	}

	// 获取实例主机名映射
	instanceHostnameMap := make(map[string]string)
	instancesResponse, err := instanceManager.DescribeInstances(request.RegionId, strings.Join(request.InstanceIds, ","), "")
	if err == nil && instancesResponse.Body.Instances != nil {
		for _, instance := range instancesResponse.Body.Instances.Instance {
			instanceId := tea.StringValue(instance.InstanceId)
			hostname := tea.StringValue(instance.HostName)
			if hostname == "" {
				hostname = instanceId
			}
			instanceHostnameMap[instanceId] = hostname
		}
	}

	log.Printf("✅ 批量下载命令已发送，InvokeId: %s", *response.Body.InvokeId)
	s.sendJSONResponse(w, APIResponse{
		Success: true,
		Message: "批量下载命令已发送",
		Data: map[string]interface{}{
			"invokeId":            *response.Body.InvokeId,
			"instanceCount":       len(request.InstanceIds),
			"localDir":            request.LocalDir,
			"remotePath":          request.RemotePath,
			"instanceHostnameMap": instanceHostnameMap,
		},
	})
}

// buildFileCopyCommand 构建单个文件复制命令（安全目录方案）
func (s *Server) buildFileCopyCommand(sourcePath, targetDir, fileName string) string {
	// 安全的文件复制方案：先复制到安全目录，再移动到HTTP目录
	return fmt.Sprintf(`#!/bin/bash

# 安全文件复制脚本
echo "=== 安全文件复制到HTTP服务器目录 ==="
echo "源文件: %s"
echo "目标目录: %s"
echo "文件名: %s"

# 定义安全下载目录
SAFE_DOWNLOAD_DIR="/tmp/safe_downloads"
TEMP_FILE="$SAFE_DOWNLOAD_DIR/%s"

# 创建安全下载目录
echo "📁 创建安全下载目录..."
mkdir -p "$SAFE_DOWNLOAD_DIR"
chmod 755 "$SAFE_DOWNLOAD_DIR"

# 确保HTTP目标目录存在
mkdir -p "%s"

# 检查源文件是否存在
echo "🔍 检查源文件..."
if [ ! -e "%s" ]; then
    echo "❌ 源文件不存在: %s"

    # 尝试使用sudo检查（如果普通权限无法访问）
    if sudo test -e "%s" 2>/dev/null; then
        echo "💡 文件存在但需要特殊权限，尝试使用sudo复制..."
        USE_SUDO=true
    else
        echo "💡 可能的原因："
        echo "   1. 文件路径错误"
        echo "   2. 文件不存在"
        echo "   3. 权限不足"

        # 显示父目录内容以帮助调试
        PARENT_DIR=$(dirname "%s")
        echo "🔍 父目录内容（用于调试）:"
        ls -la "$PARENT_DIR" 2>/dev/null | head -5 || echo "无法列出父目录内容"
        exit 1
    fi
else
    USE_SUDO=false
fi

# 检查是否为文件
if [ "$USE_SUDO" = "true" ]; then
    if ! sudo test -f "%s"; then
        echo "❌ 源路径不是文件: %s"
        exit 1
    fi
else
    if [ ! -f "%s" ]; then
        echo "❌ 源路径不是文件: %s"
        exit 1
    fi
fi

# 第一步：复制文件到安全目录
echo "📋 步骤1: 复制文件到安全目录..."
if [ "$USE_SUDO" = "true" ]; then
    # 使用sudo复制受保护的文件
    if sudo cp "%s" "$TEMP_FILE"; then
        # 修改文件权限，使当前用户可以访问
        sudo chown $(whoami):$(whoami) "$TEMP_FILE" 2>/dev/null || true
        sudo chmod 644 "$TEMP_FILE"
        echo "✅ 使用sudo成功复制到安全目录"
    else
        echo "❌ sudo复制失败"
        exit 1
    fi
else
    # 普通复制
    if cp "%s" "$TEMP_FILE"; then
        chmod 644 "$TEMP_FILE"
        echo "✅ 成功复制到安全目录"
    else
        echo "❌ 复制到安全目录失败"
        exit 1
    fi
fi

# 第二步：从安全目录移动到HTTP目录
echo "📋 步骤2: 移动文件到HTTP服务器目录..."
if mv "$TEMP_FILE" "%s/%s"; then
    echo "✅ 文件成功移动到HTTP目录"

    # 确保HTTP服务器可以访问
    chmod 644 "%s/%s"

    echo "目标文件: %s/%s"
    ls -la "%s/%s"

    # 显示文件大小信息
    FILE_SIZE=$(stat -c%%s "%s/%s" 2>/dev/null || echo "未知")
    echo "📊 文件大小: $FILE_SIZE 字节"
else
    echo "❌ 移动文件到HTTP目录失败"
    # 清理临时文件
    rm -f "$TEMP_FILE"
    exit 1
fi

echo "=== 安全复制完成 ==="`,
		sourcePath, targetDir, fileName,
		fileName,
		targetDir,
		sourcePath, sourcePath,
		sourcePath,
		sourcePath,
		sourcePath, sourcePath,
		sourcePath, sourcePath,
		sourcePath,
		sourcePath,
		targetDir, fileName,
		targetDir, fileName,
		targetDir, fileName,
		targetDir, fileName,
		targetDir, fileName)
}

// buildWildcardFileCopyCommand 构建通配符文件复制命令（安全目录方案）
func (s *Server) buildWildcardFileCopyCommand(sourcePath, targetDir string) string {
	return fmt.Sprintf(`#!/bin/bash

# 安全通配符文件复制脚本
echo "=== 安全通配符文件复制到HTTP服务器目录 ==="
echo "源路径模式: %s"
echo "目标目录: %s"

# 定义安全下载目录
SAFE_DOWNLOAD_DIR="/tmp/safe_downloads"

# 创建安全下载目录和HTTP目标目录
echo "📁 创建必要目录..."
mkdir -p "$SAFE_DOWNLOAD_DIR"
chmod 755 "$SAFE_DOWNLOAD_DIR"
mkdir -p "%s"

# 解析源路径
SOURCE_DIR=$(dirname "%s")
SOURCE_PATTERN=$(basename "%s")

echo "🔍 查找匹配的文件..."
echo "搜索目录: $SOURCE_DIR"
echo "搜索模式: $SOURCE_PATTERN"

# 首先尝试普通权限查找
FILES=$(find "$SOURCE_DIR" -maxdepth 1 -name "$SOURCE_PATTERN" -type f 2>/dev/null)

# 如果普通权限找不到文件，尝试使用sudo
if [ -z "$FILES" ]; then
    echo "💡 普通权限未找到文件，尝试使用sudo..."
    FILES=$(sudo find "$SOURCE_DIR" -maxdepth 1 -name "$SOURCE_PATTERN" -type f 2>/dev/null)
    USE_SUDO=true
else
    USE_SUDO=false
fi

if [ -z "$FILES" ]; then
    echo "❌ 未找到匹配的文件: %s"
    echo "💡 尝试的搜索路径: $SOURCE_DIR"
    echo "💡 搜索模式: $SOURCE_PATTERN"

    # 尝试列出目录内容以帮助调试
    echo "🔍 目录内容（用于调试）:"
    if [ "$USE_SUDO" = "true" ]; then
        sudo ls -la "$SOURCE_DIR" 2>/dev/null | head -10 || echo "无法列出目录内容"
    else
        ls -la "$SOURCE_DIR" 2>/dev/null | head -10 || echo "无法列出目录内容"
    fi
    exit 1
fi

echo "📋 找到匹配的文件:"
echo "$FILES"

# 计算文件数量
FILE_COUNT=$(echo "$FILES" | wc -l)
echo "📊 匹配文件数量: $FILE_COUNT"

# 复制所有匹配的文件
COPIED_COUNT=0
FAILED_COUNT=0
TOTAL_SIZE=0

echo "=== WILDCARD_FILES_START ==="
for file in $FILES; do
    filename=$(basename "$file")
    temp_file="$SAFE_DOWNLOAD_DIR/$filename"
    final_file="%s/$filename"

    echo "📁 处理文件: $file"

    # 步骤1: 复制到安全目录
    if [ "$USE_SUDO" = "true" ]; then
        if sudo cp "$file" "$temp_file"; then
            sudo chown $(whoami):$(whoami) "$temp_file" 2>/dev/null || true
            sudo chmod 644 "$temp_file"
            echo "  ✅ 步骤1: 使用sudo复制到安全目录"
        else
            echo "  ❌ 步骤1: sudo复制失败"
            FAILED_COUNT=$((FAILED_COUNT + 1))
            continue
        fi
    else
        if cp "$file" "$temp_file"; then
            chmod 644 "$temp_file"
            echo "  ✅ 步骤1: 复制到安全目录"
        else
            echo "  ❌ 步骤1: 复制失败"
            FAILED_COUNT=$((FAILED_COUNT + 1))
            continue
        fi
    fi

    # 步骤2: 从安全目录移动到HTTP目录
    if mv "$temp_file" "$final_file"; then
        chmod 644 "$final_file"

        # 获取文件大小
        FILE_SIZE=$(stat -c%%s "$final_file" 2>/dev/null || echo "0")
        TOTAL_SIZE=$((TOTAL_SIZE + FILE_SIZE))

        echo "  ✅ 步骤2: 移动到HTTP目录 ($FILE_SIZE 字节)"
        echo "WILDCARD_FILE:$filename"
        COPIED_COUNT=$((COPIED_COUNT + 1))
    else
        echo "  ❌ 步骤2: 移动到HTTP目录失败"
        rm -f "$temp_file"  # 清理临时文件
        FAILED_COUNT=$((FAILED_COUNT + 1))
    fi
done
echo "=== WILDCARD_FILES_END ==="

echo "=== 复制统计 ==="
echo "✅ 成功复制: $COPIED_COUNT 个文件"
echo "❌ 复制失败: $FAILED_COUNT 个文件"
echo "📊 总大小: $TOTAL_SIZE 字节"

if [ $COPIED_COUNT -eq 0 ]; then
    echo "❌ 没有文件被成功复制"
    exit 1
fi

echo "📁 HTTP目录内容:"
ls -la "%s"

# 清理安全目录中的临时文件
echo "🧹 清理临时文件..."
rm -rf "$SAFE_DOWNLOAD_DIR"

echo "=== 安全复制完成 ==="`,
		sourcePath, targetDir,
		targetDir,
		sourcePath, sourcePath,
		sourcePath,
		targetDir,
		targetDir)
}

// sendJSONResponse 发送JSON响应
func (s *Server) sendJSONResponse(w http.ResponseWriter, response APIResponse) {
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// UnifiedDownloadRequest 统一下载请求
type UnifiedDownloadRequest struct {
	Files  []DownloadFileItem `json:"files"`  // 文件列表
	Config *DownloadConfig    `json:"config"` // 下载配置
}

// DownloadFileItem 文件项
type DownloadFileItem struct {
	InstanceID string `json:"instanceId"` // 实例ID
	InstanceIP string `json:"instanceIp"` // 实例IP
	FilePath   string `json:"filePath"`   // 文件路径
	FileName   string `json:"fileName"`   // 保存的文件名
	Port       int    `json:"port"`       // HTTP端口，默认8000
}

// DownloadConfig 下载配置
type DownloadConfig struct {
	MaxConcurrency int `json:"maxConcurrency"` // 最大并发数，默认3
	RetryAttempts  int `json:"retryAttempts"`  // 重试次数，默认2
	TimeoutSeconds int `json:"timeoutSeconds"` // 超时时间（秒），默认30
}

// DownloadResult 下载结果
type DownloadResult struct {
	InstanceID string        `json:"instanceId"`
	FileName   string        `json:"fileName"`
	Success    bool          `json:"success"`
	Error      string        `json:"error,omitempty"`
	FileSize   int64         `json:"fileSize"`
	Duration   time.Duration `json:"duration"`
	LocalPath  string        `json:"localPath"`
}

// handleUnifiedDownload 处理统一下载请求
func (s *Server) handleUnifiedDownload(w http.ResponseWriter, r *http.Request) {
	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	if r.Method != http.MethodPost {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "只支持POST请求",
		})
		return
	}

	var req UnifiedDownloadRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "请求参数解析失败: " + err.Error(),
		})
		return
	}

	// 验证请求参数
	if len(req.Files) == 0 {
		s.sendJSONResponse(w, APIResponse{
			Success: false,
			Error:   "files列表不能为空",
		})
		return
	}

	// 设置默认配置
	config := req.Config
	if config == nil {
		config = &DownloadConfig{}
	}
	if config.MaxConcurrency <= 0 {
		config.MaxConcurrency = 3
	}
	if config.RetryAttempts < 0 {
		config.RetryAttempts = 2
	}
	if config.TimeoutSeconds <= 0 {
		config.TimeoutSeconds = 30
	}

	log.Printf("📥 开始统一批量下载: %d个文件", len(req.Files))

	// 执行批量下载
	results := s.performUnifiedDownload(req.Files, config)

	// 统计结果
	successCount := 0
	failedCount := 0
	totalSize := int64(0)

	for _, result := range results {
		if result.Success {
			successCount++
			totalSize += result.FileSize
		} else {
			failedCount++
		}
	}

	log.Printf("✅ 统一批量下载完成: 成功%d个，失败%d个", successCount, failedCount)

	s.sendJSONResponse(w, APIResponse{
		Success: true,
		Message: fmt.Sprintf("批量下载完成: 成功%d个，失败%d个", successCount, failedCount),
		Data: map[string]interface{}{
			"results": results,
			"stats": map[string]interface{}{
				"total":       len(results),
				"success":     successCount,
				"failed":      failedCount,
				"totalSize":   totalSize,
				"successRate": float64(successCount) / float64(len(results)) * 100,
			},
		},
	})
}
