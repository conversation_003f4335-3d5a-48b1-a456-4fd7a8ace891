#!/bin/bash
set -e

# 获取客户端名称
CLIENT=$(hostnamectl | awk -F: '/Chassis/ {gsub(/^[ \t]+/, "", $2); print $2}')
[ -z "$CLIENT" ] && CLIENT=$(hostname)

echo "[+] 使用客户端名称: $CLIENT"

# 下载官方脚本
echo "[+] 下载 OpenVPN 安装脚本..."
wget -q https://raw.githubusercontent.com/angristan/openvpn-install/master/openvpn-install.sh -O /tmp/openvpn-install.sh
chmod +x /tmp/openvpn-install.sh

# 无人值守安装
echo "[+] 开始无人值守安装..."
AUTO_INSTALL=y \
APPROVE_INSTALL=y \
APPROVE_IP=y \
DNS=1 \
PORT_CHOICE=1 \
PROTOCOL_CHOICE=1 \
COMPRESSION_ENABLED=n \
CUSTOMIZE_ENC=n \
# CLIENT="$CLIENT" \
PASS=1 \
/tmp/openvpn-install.sh

# 修改配置文件，启用 duplicate-cn
CONF_FILE="/etc/openvpn/server.conf"
if [ -f "$CONF_FILE" ]; then
    echo "[+] 启用 duplicate-cn..."
    sed -i '/^duplicate-cn$/d' "$CONF_FILE"
    echo "duplicate-cn" >> "$CONF_FILE"
fi

# 设置开机自启并立即重启服务
echo "[+] 设置 OpenVPN 开机自启并启动..."
systemctl enable openvpn-server@server
systemctl restart openvpn-server@server

echo "[✓] OpenVPN 安装完成，已启用单证书多用户模式。"
echo "[i] 客户端配置文件：/root/${CLIENT}.ovpn"
