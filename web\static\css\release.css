/* 释放页面专用样式 */

/* 释放页面头部 */
.header-release {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.header-release .section-title {
    border-bottom-color: #dc3545;
}

/* 警告框 */
.warning-box {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
}

.warning-box strong {
    color: #721c24;
}

/* 实例列表容器 */
.instances-list-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    padding: 15px;
    background: #f8f9fa;
}

/* 实例卡片 */
.instance-card {
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 10px;
    background: white;
    transition: box-shadow 0.2s;
}

.instance-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.instance-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.instance-header input[type="checkbox"] {
    margin-right: 10px;
}

.instance-name {
    font-weight: bold;
    margin-right: 10px;
}

.instance-status {
    padding: 2px 8px;
    color: white;
    border-radius: 4px;
    font-size: 12px;
    margin-left: 10px;
}

.status-running {
    background: #28a745;
}

.status-stopped {
    background: #6c757d;
}

.status-starting {
    background: #17a2b8;
}

.status-stopping {
    background: #ffc107;
    color: #212529;
}

.status-unknown {
    background: #dc3545;
}

.protection-icon {
    margin-left: 5px;
    font-size: 14px;
}

.instance-details {
    font-size: 14px;
    color: #666;
    line-height: 1.4;
}

.instance-details div {
    margin-bottom: 2px;
}

.instance-details strong {
    color: #333;
}

.protection-warning {
    color: #dc3545;
    font-weight: bold;
}

/* 释放按钮特殊样式 */
.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

/* 加载动画 - 释放页面 */
.header-release + .loading .spinner {
    border-top-color: #dc3545;
}

/* 选择计数器 */
#selectedCount {
    font-weight: 600;
    color: #495057;
}

/* 空状态 */
.empty-state {
    text-align: center;
    color: #666;
    padding: 40px 20px;
    font-style: italic;
}

/* 实例操作按钮组 */
.instance-actions {
    margin-top: 10px;
    display: flex;
    gap: 10px;
}

.instance-actions .btn {
    padding: 6px 12px;
    font-size: 14px;
    margin-right: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .instances-list-container {
        max-height: 300px;
    }
    
    .instance-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .instance-header input[type="checkbox"] {
        margin-bottom: 5px;
    }
    
    .instance-status {
        margin-left: 0;
        margin-top: 5px;
    }
}
