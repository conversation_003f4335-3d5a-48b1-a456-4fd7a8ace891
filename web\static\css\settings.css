/* 设置页面专用样式 */

/* 设置页面头部 */
.header-settings {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

.header-settings .section-title {
    border-bottom-color: #6c757d;
}

/* 信息框 */
.info-box {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
}

.info-box strong {
    color: #0c5460;
}

/* 密码输入框 */
.password-input {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #6c757d;
    font-size: 18px;
    user-select: none;
}

.password-toggle:hover {
    color: #495057;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-connected {
    background: #28a745;
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.3);
}

.status-disconnected {
    background: #dc3545;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.3);
}

#connectionStatus {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

/* 当前设置显示 */
#currentSettings {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

#currentSettings div {
    margin-bottom: 8px;
    padding: 5px 0;
    border-bottom: 1px solid #e9ecef;
}

#currentSettings div:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

#currentSettings strong {
    color: #495057;
    min-width: 120px;
    display: inline-block;
}

/* 设置页面按钮 */
.header-settings + * .btn {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

.header-settings + * .btn:focus,
.header-settings + * .form-group input:focus,
.header-settings + * .form-group select:focus {
    border-color: #6c757d;
}

/* 加载动画 - 设置页面 */
.header-settings + * .loading .spinner {
    border-top-color: #6c757d;
}

/* 表单验证状态 */
.form-group.has-error input {
    border-color: #dc3545;
}

.form-group.has-success input {
    border-color: #28a745;
}

.form-group.has-warning input {
    border-color: #ffc107;
}

/* 设置卡片特殊样式 */
.settings-card {
    border-left: 4px solid #6c757d;
}

/* 安全提示 */
.security-notice {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 12px;
    border-radius: 4px;
    margin-top: 10px;
    font-size: 14px;
}

/* 操作历史 */
.operation-history {
    max-height: 200px;
    overflow-y: auto;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 10px;
}

.operation-item {
    padding: 5px 0;
    border-bottom: 1px solid #e9ecef;
    font-size: 14px;
    color: #666;
}

.operation-item:last-child {
    border-bottom: none;
}

.operation-time {
    color: #999;
    font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .password-toggle {
        right: 8px;
        font-size: 16px;
    }
    
    #currentSettings strong {
        min-width: 100px;
        font-size: 14px;
    }
    
    .info-box {
        padding: 12px;
    }
    
    .info-box ul {
        margin-left: 15px;
    }
}
