# 开发规范和规则

- 阿里云SDK使用规范：环境变量存储AccessKey(ACCESS_KEY_ID/ACCESS_KEY_SECRET)，使用tea包进行类型转换，实现错误恢复机制，DryRun模式用于参数验证，console.Log输出操作日志。
- 阿里云ECS API预检模式限制：在DryRun模式下，Amount和MinAmount参数必须设置为0，否则会返回DryRun.InvalidAmount错误。已在CreateInstance方法中添加条件判断，预检模式时强制设置Amount=0和MinAmount=0。- 阿里云ECS管理器文件下载功能开发规范：使用Base64编码确保文件传输完整性，通过云助手命令实现跨平台文件下载(Linux Shell + Windows PowerShell)，实现轮询机制监控下载状态，提供完整的错误处理和参数验证。前端使用showLoading(true/false)控制加载状态，避免调用不存在的hideLoading函数。
