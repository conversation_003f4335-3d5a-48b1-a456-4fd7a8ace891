# 阿里云ECS管理器配置文件
# 使用此配置文件可以避免每次都输入长长的命令行参数

ecs:
  # 基本配置
  region_id: "cn-hongkong"                                    # 地域ID
  image_id: "ubuntu_20_04_x64_20G_alibase_20250317.vhd"      # 镜像ID
  instance_type: "ecs.u1-c1m1.xlarge"                        # 实例规格
  
  # 网络配置
  security_group_id: "sg-j6c4a5uhkybf1nxum01t"               # 安全组ID（请替换为实际值）
  vswitch_id: "vsw-j6c8xzpo2pwx75zphjbp2"                    # 虚拟交换机ID（请替换为实际值）
  internet_max_bandwidth_out: 100                               # 公网出带宽最大值 (Mbit/s)
  internet_charge_type: "PayByTraffic"                       # 网络计费类型: PayByBandwidth | PayByTraffic
  
  # 存储配置
  system_disk_size: "40"                                     # 系统盘大小 (GB)
  system_disk_category: "cloud_efficiency"                   # 系统盘类型
  
  # 计费配置
  instance_charge_type: "PostPaid"                           # 实例计费方式: PrePaid | PostPaid
  
  # 实例信息（可选）
  instance_name: "vm-"                             # 实例名称
  description: "Created by aliyun-manager"                   # 实例描述
  
  # 运行模式（可选）
  dry_run: false                                              # 预检模式，不实际创建实例

  # 弹性IP配置（可选）
  enable_eip: true                                            # 是否启用弹性IP
  eip_bandwidth: 5                                            # EIP带宽 (Mbps)，留空则使用internet_max_bandwidth_out
  eip_charge_type: "PayByTraffic"                             # EIP计费类型: PayByTraffic | PayByBandwidth
  eip_name: ""                                                # EIP名称，留空则自动生成
  eip_description: "Created by aliyun-manager"                # EIP描述

# 配置说明：
# 1. 请将 security_group_id 和 vswitch_id 替换为您实际的资源ID
# 2. 确保已设置环境变量 ACCESS_KEY_ID 和 ACCESS_KEY_SECRET
# 3. dry_run 设置为 true 时只进行参数验证，不会实际创建实例
# 4. 更多实例规格请参考阿里云官方文档

# 使用方法：
# 1. 使用默认配置文件：go run main.go
# 2. 指定配置文件：go run main.go --config /path/to/config.yaml
# 3. 命令行参数会覆盖配置文件中的设置
