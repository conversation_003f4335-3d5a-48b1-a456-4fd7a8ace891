/* 创建实例页面专用样式 */

/* 表单样式增强 */
.card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #e8f5e8;
}

.section-title {
    color: #2e7d32;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-group label {
    color: #2e7d32;
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: block;
}

.form-group input,
.form-group select,
.form-group textarea {
    border: 2px solid #e8f5e8;
    border-radius: 8px;
    padding: 0.75rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: #4caf50;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
    outline: none;
}

/* 按钮样式 */
.btn-primary {
    background: linear-gradient(135deg, #4caf50, #66bb6a);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #388e3c, #4caf50);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.btn-secondary {
    background: #f5f5f5;
    color: #666;
    border: 2px solid #e0e0e0;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #eeeeee;
    border-color: #bdbdbd;
}

/* 配置管理区域 */
.config-actions {
    background: #f8fffe;
    border: 1px solid #e8f5e8;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.config-actions h3 {
    color: #2e7d32;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    header {
        padding: 1rem;
    }
    
    header h1 {
        font-size: 1.5rem;
    }
    
    header nav {
        gap: 0.5rem;
    }
    
    header nav a {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }
    
    .card {
        padding: 1.5rem;
    }
    
    .section-title {
        font-size: 1.2rem;
    }
}

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* 成功/错误提示样式 */
.alert {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-weight: 500;
}

.alert-success {
    background: #e8f5e9;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
}

.alert-error {
    background: #ffebee;
    color: #c62828;
    border: 1px solid #ffcdd2;
}

.alert-warning {
    background: #fff8e1;
    color: #f57c00;
    border: 1px solid #ffecb3;
}
