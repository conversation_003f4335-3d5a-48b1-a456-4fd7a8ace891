{{template "header" .}}



        <div class="card">
            <h2 class="section-title">📍 基本配置</h2>
            <form id="ecsForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="regionId">地域ID *</label>
                        <select id="regionId" name="regionId" required>
                            <option value="cn-hongkong">香港</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="instanceType">实例规格 *</label>
                        <select id="instanceType" name="instanceType" required>
                            <option value="ecs.u1-c1m1.xlarge">ecs.u1-c1m1.xlarge (4核4GB)</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="imageId">镜像ID *</label>
                    <input type="text" id="imageId" name="imageId" value="ubuntu_20_04_x64_20G_alibase_20250317.vhd" required>
                </div>

                <h2 class="section-title">🌐 网络配置</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="securityGroupId">安全组ID *</label>
                        <input type="text" id="securityGroupId" name="securityGroupId" placeholder="sg-bp1fg655nh68xyz9****" value="sg-j6c4a5uhkybf1nxum01t" required>
                    </div>
                    <div class="form-group">
                        <label for="vSwitchId">虚拟交换机ID *</label>
                        <input type="text" id="vSwitchId" name="vSwitchId" placeholder="vsw-bp1s5fnvk4gn2tws0****" value="vsw-j6c8xzpo2pwx75zphjbp2" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="internetMaxBandwidthOut">公网带宽 (Mbit/s)</label>
                        <input type="number" id="internetMaxBandwidthOut" name="internetMaxBandwidthOut" value="200" min="0" max="200">
                    </div>
                    <div class="form-group">
                        <label for="internetChargeType">网络计费类型</label>
                        <select id="internetChargeType" name="internetChargeType">
                            <option value="PayByTraffic">按流量计费</option>
                            <option value="PayByBandwidth">按带宽计费</option>
                        </select>
                    </div>
                </div>

                <h2 class="section-title">🌐 弹性IP配置</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="enableEIP">
                            <input type="checkbox" id="enableEIP" name="enableEIP" onchange="toggleEIPOptions()" checked>
                            启用弹性IP (EIP)
                        </label>
                        <small class="form-help">启用后将自动创建弹性IP并绑定到实例，不分配公网IP</small>
                    </div>
                </div>

                <div id="eipOptions" >
                    <div class="form-row">
                        <div class="form-group">
                            <label for="eipBandwidth">EIP带宽 (Mbit/s)</label>
                            <input type="number" id="eipBandwidth" name="eipBandwidth" value="200" min="1" max="200">
                            <small class="form-help">留空则使用公网带宽设置</small>
                        </div>
                        <div class="form-group">
                            <label for="eipChargeType">EIP计费类型</label>
                            <select id="eipChargeType" name="eipChargeType">
                                <option value="PayByTraffic">按流量计费</option>
                                <option value="PayByBandwidth">按带宽计费</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="eipName">EIP名称</label>
                            <input type="text" id="eipName" name="eipName" placeholder="留空则自动生成">
                        </div>
                        <div class="form-group">
                            <label for="eipDescription">EIP描述</label>
                            <input type="text" id="eipDescription" name="eipDescription" placeholder="Created by aliyun-manager">
                        </div>
                    </div>
                </div>

                <h2 class="section-title">💾 存储配置</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="systemDiskSize">系统盘大小 (GB)</label>
                        <input type="number" id="systemDiskSize" name="systemDiskSize" value="40" min="20" max="500">
                    </div>
                    <div class="form-group">
                        <label for="systemDiskCategory">系统盘类型</label>
                        <select id="systemDiskCategory" name="systemDiskCategory">
                            <!-- <option value="cloud_efficiency">高效云盘</option> -->
                            <!-- <option value="cloud_ssd">SSD云盘</option> -->
                            <option value="cloud_essd">ESSD云盘</option>
                            <!-- <option value="cloud">普通云盘</option> -->
                        </select>
                    </div>
                </div>

                <h2 class="section-title">💰 计费配置</h2>
                <div class="form-group">
                    <label for="instanceChargeType">实例计费方式</label>
                    <select id="instanceChargeType" name="instanceChargeType">
                        <option value="PostPaid">按量付费</option>
                        <option value="PrePaid">包年包月</option>
                    </select>
                </div>

                <h2 class="section-title">ℹ️ 实例信息</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="instanceNamePrefix">实例名称前缀</label>
                        <input type="text" id="instanceNamePrefix" name="instanceNamePrefix" value="vm" placeholder="批量创建时的名称前缀">
                        <small style="color: #666;">批量创建时会自动添加序号，如：vm-001, vm-002</small>
                    </div>
                    <div class="form-group">
                        <label for="description">实例描述</label>
                        <input type="text" id="description" name="description" value="Created by aliyun-manager">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="password">登录密码（可选）</label>
                        <div style="position: relative;">
                            <input type="password" id="password" name="password" placeholder="8-30位，包含大小写字母和数字">
                            <button type="button" onclick="togglePasswordVisibility('password')" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); background: none; border: none; cursor: pointer; font-size: 14px;">👁️</button>
                        </div>
                        <small style="color: #666;">留空则使用密钥对登录。密码必须8-30位，包含大小写字母和数字</small>
                    </div>
                </div>

                <h2 class="section-title">🔢 批量配置</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="amount">创建实例数量</label>
                        <input type="number" id="amount" name="amount" value="1" min="1" max="100">
                        <small style="color: #666;">最多可创建100台实例</small>
                    </div>
                    <div class="form-group">
                        <label for="minAmount">最少创建数量（可选）</label>
                        <input type="number" id="minAmount" name="minAmount" value="0" min="0" max="100">
                        <small style="color: #666;">库存不足时可接受的最少数量</small>
                    </div>
                </div>

                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="dryRun" name="dryRun">
                        <label for="dryRun">预检模式 (只验证参数，不实际创建实例)</label>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <button type="button" class="btn btn-warning" onclick="validateConfig()">验证配置</button>
                    <button type="button" class="btn btn-secondary" onclick="saveConfig()">保存配置</button>
                    <button type="submit" class="btn">创建实例</button>
                </div>
            </form>
        </div>

        <div class="loading" id="loading">
            <div class="spinner-container">
                <div class="spinner"></div>
                <div class="loading-text">正在处理请求，请稍候...</div>
            </div>
        </div>
{{template "footer" .}}
