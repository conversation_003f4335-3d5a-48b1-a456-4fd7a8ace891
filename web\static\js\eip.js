// 弹性IP管理页面JavaScript

let eipList = [];
let instanceList = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    refreshEIPList();
    // refreshInstanceList();
});

// 创建弹性IP
async function createEIP() {
    const createBtn = document.querySelector('button[onclick="createEIP()"]');
    const originalText = createBtn.textContent;

    // 设置按钮加载状态
    createBtn.disabled = true;
    createBtn.innerHTML = '<span class="loading-spinner"></span> 创建中...';
    createBtn.style.opacity = '0.7';

    showLoading(true);
    try {
        const data = getFormData('createEipForm');

        // 验证必填字段
        if (!data.regionId || !data.eipBandwidth) {
            showAlert('❌ 请填写必填字段', 'error');
            return;
        }

        const result = await apiRequest('/api/eip/create', {
            method: 'POST',
            body: JSON.stringify(data)
        });

        if (result.success) {
            showAlert(`✅ 成功创建 ${data.eipCount} 个弹性IP！`, 'success');
            refreshEIPList();
        } else {
            showAlert('❌ 创建失败: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('❌ 创建请求失败: ' + error.message, 'error');
    } finally {
        // 恢复按钮状态
        createBtn.disabled = false;
        createBtn.textContent = originalText;
        createBtn.style.opacity = '1';
        showLoading(false);
    }
}

// 刷新弹性IP列表
async function refreshEIPList() {
    try {
        const regionId = document.getElementById('regionId').value || 'cn-hangzhou';
        const result = await apiRequest(`/api/eip/list?regionId=${regionId}`);
        
        if (result.success) {
            eipList = result.data || [];
            renderEIPTable();
        } else {
            showAlert('❌ 获取弹性IP列表失败: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('❌ 获取弹性IP列表失败: ' + error.message, 'error');
    }
}

// 渲染弹性IP表格
function renderEIPTable() {
    const tbody = document.getElementById('eipTableBody');
    
    if (eipList.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="no-data">暂无弹性IP</td></tr>';
        return;
    }
    
    tbody.innerHTML = eipList.map(eip => `
        <tr>
            <td><input type="checkbox" class="eip-checkbox" value="${eip.allocationId}" onchange="updateBindPairs()"></td>
            <td>${eip.allocationId}</td>
            <td>${eip.ipAddress}</td>
            <td><span class="status ${eip.status.toLowerCase()}">${eip.status}</span></td>
            <td>${eip.bandwidth}Mbps</td>
            <td>${eip.chargeType}</td>
            <td>${eip.instanceId || '-'}</td>
            <td>
                <button onclick="releaseEIP('${eip.allocationId}')" class="btn-danger">释放</button>
                ${eip.status === 'Available' ?
                    `<button onclick="showSingleBindModal('${eip.allocationId}')" class="btn-primary">绑定</button>` :
                    eip.instanceId ?
                        `<button onclick="unbindEIP('${eip.allocationId}')" class="btn-warning">解绑</button>` : ''
                }
            </td>
        </tr>
    `).join('');
}

// 刷新ECS实例列表
async function refreshInstanceList() {
    try {
        const regionId = document.getElementById('instanceRegionId').value || 'cn-hangzhou';
        const result = await apiRequest(`/api/instances/list?regionId=${regionId}`);
        
        if (result.success) {
            instanceList = result.data || [];
            renderInstanceTable();
        } else {
            showAlert('❌ 获取实例列表失败: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('❌ 获取实例列表失败: ' + error.message, 'error');
    }
}

// 渲染ECS实例表格
function renderInstanceTable() {
    const tbody = document.getElementById('instanceTableBody');
    
    if (instanceList.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="no-data">暂无实例</td></tr>';
        return;
    }
    
    tbody.innerHTML = instanceList.map(instance => `
        <tr>
            <td><input type="checkbox" class="instance-checkbox" value="${instance.instanceId}" onchange="updateBindPairs()"></td>
            <td>${instance.instanceId}</td>
            <td>${instance.instanceName || '-'}</td>
            <td><span class="status ${instance.status.toLowerCase()}">${instance.status}</span></td>
            <td>${instance.publicIpAddress || '-'}</td>
            <td>${instance.eipAddress || '-'}</td>
        </tr>
    `).join('');
}

// 释放单个弹性IP
async function releaseEIP(eipId) {
    const confirmed = await confirmAction({
        title: '确认释放弹性IP',
        message: `您确定要释放弹性IP ${eipId} 吗？`,
        details: '释放后将无法恢复，请确认操作。',
        confirmText: '确认释放',
        cancelText: '取消',
        type: 'danger'
    });
    
    if (!confirmed) return;
    
    showLoading(true);
    try {
        const result = await apiRequest('/api/eip/release', {
            method: 'POST',
            body: JSON.stringify({ eipIds: [eipId] })
        });
        
        if (result.success) {
            showAlert('✅ 弹性IP释放成功！', 'success');
            refreshEIPList();
        } else {
            showAlert('❌ 释放失败: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('❌ 释放请求失败: ' + error.message, 'error');
    }
    showLoading(false);
}

// 批量释放弹性IP
async function batchReleaseEIP() {
    const selectedEips = getSelectedEIPs();
    if (selectedEips.length === 0) {
        showAlert('❌ 请选择要释放的弹性IP', 'warning');
        return;
    }
    
    const confirmed = await confirmAction({
        title: '批量释放弹性IP',
        message: `您确定要释放 ${selectedEips.length} 个弹性IP吗？`,
        details: '释放后将无法恢复，请确认操作。',
        confirmText: '确认释放',
        cancelText: '取消',
        type: 'danger'
    });
    
    if (!confirmed) return;
    
    showLoading(true);
    try {
        const result = await apiRequest('/api/eip/release', {
            method: 'POST',
            body: JSON.stringify({ eipIds: selectedEips })
        });
        
        if (result.success) {
            showAlert(`✅ 成功释放 ${selectedEips.length} 个弹性IP！`, 'success');
            refreshEIPList();
        } else {
            showAlert('❌ 批量释放失败: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('❌ 批量释放请求失败: ' + error.message, 'error');
    }
    showLoading(false);
}

// 显示绑定模态框
function showBindModal() {
    const selectedEips = getSelectedEIPs();
    const selectedInstances = getSelectedInstances();
    
    if (selectedEips.length === 0 || selectedInstances.length === 0) {
        showAlert('❌ 请选择要绑定的弹性IP和实例', 'warning');
        return;
    }
    
    if (selectedEips.length !== selectedInstances.length) {
        showAlert('❌ 弹性IP和实例数量必须相等', 'warning');
        return;
    }
    
    const bindPairs = document.getElementById('bindPairs');
    bindPairs.innerHTML = selectedEips.map((eipId, index) => {
        const eip = eipList.find(e => e.allocationId === eipId);
        const instance = instanceList.find(i => i.instanceId === selectedInstances[index]);
        return `
            <div class="bind-pair">
                <span>弹性IP: ${eip.ipAddress} (${eipId})</span>
                <span>→</span>
                <span>实例: ${instance.instanceName || instance.instanceId}</span>
            </div>
        `;
    }).join('');
    
    document.getElementById('bindModal').style.display = 'block';
}

// 关闭绑定模态框
function closeBindModal() {
    document.getElementById('bindModal').style.display = 'none';
}

// 更新绑定对显示
function updateBindPairs() {
    const selectedEips = getSelectedEIPs();
    const selectedInstances = getSelectedInstances();
    const bindPairs = document.getElementById('bindPairs');

    if (selectedEips.length === 0 && selectedInstances.length === 0) {
        bindPairs.innerHTML = '<div class="bind-hint">请选择要绑定的弹性IP和实例</div>';
        return;
    }

    if (selectedEips.length === 0) {
        bindPairs.innerHTML = '<div class="bind-hint">请选择弹性IP</div>';
        return;
    }

    if (selectedInstances.length === 0) {
        bindPairs.innerHTML = '<div class="bind-hint">请选择实例</div>';
        return;
    }

    // 显示绑定对
    if (selectedEips.length === 1 && selectedInstances.length === 1) {
        // 单个绑定
        const eip = eipList.find(e => e.allocationId === selectedEips[0]);
        const instance = instanceList.find(i => i.instanceId === selectedInstances[0]);
        bindPairs.innerHTML = `
            <div class="bind-pair">
                <span>弹性IP: ${eip ? eip.ipAddress : selectedEips[0]} (${selectedEips[0]})</span>
                <span>→</span>
                <span>实例: ${instance ? (instance.instanceName || instance.instanceId) : selectedInstances[0]}</span>
            </div>
        `;
    } else if (selectedEips.length === selectedInstances.length) {
        // 批量绑定
        bindPairs.innerHTML = selectedEips.map((eipId, index) => {
            const eip = eipList.find(e => e.allocationId === eipId);
            const instance = instanceList.find(i => i.instanceId === selectedInstances[index]);
            return `
                <div class="bind-pair">
                    <span>弹性IP: ${eip ? eip.ipAddress : eipId} (${eipId})</span>
                    <span>→</span>
                    <span>实例: ${instance ? (instance.instanceName || instance.instanceId) : selectedInstances[index]}</span>
                </div>
            `;
        }).join('');
    } else {
        bindPairs.innerHTML = `
            <div class="bind-warning">
                已选择 ${selectedEips.length} 个弹性IP 和 ${selectedInstances.length} 个实例<br>
                请选择相同数量的EIP和实例，或选择1个EIP和1个实例
            </div>
        `;
    }
}

// 执行绑定
async function executeBind() {
    const selectedEips = getSelectedEIPs();
    const selectedInstances = getSelectedInstances();

    // 验证选择
    if (selectedEips.length === 0) {
        showAlert('❌ 请选择要绑定的弹性IP', 'warning');
        return;
    }

    if (selectedInstances.length === 0) {
        showAlert('❌ 请选择要绑定的实例', 'warning');
        return;
    }

    // 处理绑定逻辑
    let bindings = [];

    if (selectedEips.length === 1 && selectedInstances.length === 1) {
        // 单个EIP绑定单个实例
        bindings = [{
            eipId: selectedEips[0],
            instanceId: selectedInstances[0]
        }];
    } else if (selectedEips.length === selectedInstances.length) {
        // 一对一绑定
        bindings = selectedEips.map((eipId, index) => ({
            eipId: eipId,
            instanceId: selectedInstances[index]
        }));
    } else {
        showAlert('❌ 请选择相同数量的弹性IP和实例，或选择1个EIP和1个实例', 'warning');
        return;
    }

    showLoading(true);
    try {
        const result = await apiRequest('/api/eip/bind', {
            method: 'POST',
            body: JSON.stringify({ bindings })
        });

        if (result.success) {
            showAlert(`✅ 成功绑定 ${bindings.length} 个弹性IP！`, 'success');
            closeBindModal();
            refreshEIPList();
            // refreshInstanceList();
        } else {
            showAlert('❌ 绑定失败: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('❌ 绑定请求失败: ' + error.message, 'error');
    }
    showLoading(false);
}

// 获取选中的弹性IP
function getSelectedEIPs() {
    return Array.from(document.querySelectorAll('.eip-checkbox:checked')).map(cb => cb.value);
}

// 获取选中的实例
function getSelectedInstances() {
    return Array.from(document.querySelectorAll('.instance-checkbox:checked')).map(cb => cb.value);
}

// 切换所有弹性IP选择
function toggleAllEipSelection() {
    const selectAll = document.getElementById('selectAllEip');
    const checkboxes = document.querySelectorAll('.eip-checkbox');
    checkboxes.forEach(cb => cb.checked = selectAll.checked);
}

// 切换所有实例选择
function toggleAllInstanceSelection() {
    const selectAll = document.getElementById('selectAllInstance');
    const checkboxes = document.querySelectorAll('.instance-checkbox');
    checkboxes.forEach(cb => cb.checked = selectAll.checked);
}

// 显示单个EIP绑定模态框
function showSingleBindModal(eipId) {
    // 清除之前的选择
    document.querySelectorAll('.eip-checkbox').forEach(cb => cb.checked = false);
    document.querySelectorAll('.instance-checkbox').forEach(cb => cb.checked = false);

    // 选中指定的EIP
    const eipCheckbox = document.querySelector(`.eip-checkbox[value="${eipId}"]`);
    if (eipCheckbox) {
        eipCheckbox.checked = true;
    }

    // 显示绑定模态框
    document.getElementById('bindModal').style.display = 'block';

    // 更新模态框标题和提示
    const modalTitle = document.querySelector('#bindModal h3');
    if (modalTitle) {
        modalTitle.textContent = `绑定弹性IP: ${eipId}`;
    }

    // 更新绑定对显示
    updateBindPairs();

    // 显示提示信息
    showAlert('💡 请在下方实例列表中选择一个要绑定的实例', 'info');
}

// 解绑弹性IP
async function unbindEIP(eipId) {
    const confirmed = await confirmAction({
        title: '确认解绑弹性IP',
        message: `您确定要解绑弹性IP ${eipId} 吗？`,
        details: '解绑后EIP将变为可用状态，可以重新绑定到其他实例。',
        confirmText: '确认解绑',
        cancelText: '取消'
    });

    if (!confirmed) return;

    showLoading(true);
    try {
        const result = await apiRequest('/api/eip/unbind', {
            method: 'POST',
            body: JSON.stringify({
                eipIds: [eipId]
            })
        });

        if (result.success) {
            showAlert('✅ 弹性IP解绑成功！', 'success');
            refreshEIPList();
        } else {
            showAlert('❌ 解绑失败: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('❌ 解绑请求失败: ' + error.message, 'error');
    }
    showLoading(false);
}

// 批量解绑弹性IP
async function batchUnbindEIP() {
    const selectedEips = getSelectedEIPs();
    if (selectedEips.length === 0) {
        showAlert('❌ 请选择要解绑的弹性IP', 'warning');
        return;
    }

    const confirmed = await confirmAction({
        title: '确认批量解绑弹性IP',
        message: `您确定要解绑选中的 ${selectedEips.length} 个弹性IP吗？`,
        details: '解绑后这些EIP将变为可用状态，可以重新绑定到其他实例。',
        confirmText: '确认解绑',
        cancelText: '取消'
    });

    if (!confirmed) return;

    showLoading(true);
    try {
        const result = await apiRequest('/api/eip/unbind', {
            method: 'POST',
            body: JSON.stringify({
                eipIds: selectedEips
            })
        });

        if (result.success) {
            showAlert(`✅ 成功解绑 ${selectedEips.length} 个弹性IP！`, 'success');
            refreshEIPList();
        } else {
            showAlert('❌ 批量解绑失败: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('❌ 批量解绑请求失败: ' + error.message, 'error');
    }
    showLoading(false);
}
