package web

import (
	"os"
	"testing"

	"aliyun-manager/pkg/config"
)

func TestSaveAndLoadConfigToFile(t *testing.T) {
	// 创建测试服务器
	server := NewServer(8080)

	// 创建测试配置
	testConfig := &config.ECSInstanceConfig{
		RegionId:                "cn-hangzhou",
		ImageId:                 "ubuntu_20_04_x64_20G_alibase_20210420.vhd",
		InstanceType:            "ecs.t5-lc1m1.small",
		SecurityGroupId:         "sg-test123",
		VSwitchId:               "vsw-test123",
		InternetMaxBandwidthOut: func() *int { i := 1; return &i }(),
		InternetChargeType:      "PayByTraffic",
		SystemDiskSize:          func() *int { i := 40; return &i }(),
		SystemDiskCategory:      "cloud_efficiency",
		InstanceChargeType:      "PostPaid",
		InstanceName:            "test-instance",
		Description:             "Test instance created by unit test",
		DryRun:                  true,
		Amount:                  1,
		MinAmount:               1,
	}

	// 测试文件名
	testFilename := "web-config-test.json"

	// 清理测试文件（如果存在）
	defer func() {
		if _, err := os.Stat(testFilename); err == nil {
			os.Remove(testFilename)
		}
	}()

	// 测试保存配置
	t.Run("SaveConfig", func(t *testing.T) {
		err := server.saveConfigToFile(testConfig, testFilename)
		if err != nil {
			t.Fatalf("保存配置失败: %v", err)
		}

		// 检查文件是否存在
		if _, err := os.Stat(testFilename); os.IsNotExist(err) {
			t.Fatal("配置文件未创建")
		}
	})

	// 测试加载配置
	t.Run("LoadConfig", func(t *testing.T) {
		loadedConfig, err := server.loadConfigFromFile(testFilename)
		if err != nil {
			t.Fatalf("加载配置失败: %v", err)
		}

		// 验证配置内容
		if loadedConfig.RegionId != testConfig.RegionId {
			t.Errorf("RegionId 不匹配: 期望 %s, 实际 %s", testConfig.RegionId, loadedConfig.RegionId)
		}
		if loadedConfig.ImageId != testConfig.ImageId {
			t.Errorf("ImageId 不匹配: 期望 %s, 实际 %s", testConfig.ImageId, loadedConfig.ImageId)
		}
		if loadedConfig.InstanceType != testConfig.InstanceType {
			t.Errorf("InstanceType 不匹配: 期望 %s, 实际 %s", testConfig.InstanceType, loadedConfig.InstanceType)
		}
		if loadedConfig.InstanceName != testConfig.InstanceName {
			t.Errorf("InstanceName 不匹配: 期望 %s, 实际 %s", testConfig.InstanceName, loadedConfig.InstanceName)
		}
		if loadedConfig.DryRun != testConfig.DryRun {
			t.Errorf("DryRun 不匹配: 期望 %t, 实际 %t", testConfig.DryRun, loadedConfig.DryRun)
		}
	})

	// 测试列出配置文件
	t.Run("ListConfigs", func(t *testing.T) {
		configs, err := server.listConfigFiles()
		if err != nil {
			t.Fatalf("列出配置文件失败: %v", err)
		}

		// 检查是否包含测试文件
		found := false
		for _, cfg := range configs {
			if cfg.Filename == testFilename {
				found = true
				if cfg.Description != "ECS实例创建配置文件" {
					t.Errorf("配置文件描述不正确: %s", cfg.Description)
				}
				break
			}
		}
		if !found {
			t.Error("测试配置文件未在列表中找到")
		}
	})
}

func TestSaveConfigToFileWithNilConfig(t *testing.T) {
	server := NewServer(8080)

	err := server.saveConfigToFile(nil, "test.json")
	if err == nil {
		t.Error("期望返回错误，但没有返回")
	}
	if err.Error() != "配置不能为空" {
		t.Errorf("错误信息不正确: %s", err.Error())
	}
}

func TestLoadConfigFromNonExistentFile(t *testing.T) {
	server := NewServer(8080)

	_, err := server.loadConfigFromFile("non-existent-file.json")
	if err == nil {
		t.Error("期望返回错误，但没有返回")
	}
}
