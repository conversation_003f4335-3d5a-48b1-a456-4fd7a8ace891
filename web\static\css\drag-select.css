/* 拖拽多选组件样式 */

/* 选择框样式 */
.drag-selection-box {
    position: fixed;
    border: 2px dashed #667eea;
    background: rgba(102, 126, 234, 0.1);
    pointer-events: none;
    z-index: 9999;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    transition: opacity 0.1s ease;
}

/* 拖拽选中状态 */
.instance-card.drag-selected {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
    transform: translateY(-1px);
    transition: all 0.2s ease;
}

/* 拖拽选中状态的复选框 */
.instance-card.drag-selected input[type="checkbox"] {
    accent-color: #667eea;
}

/* 拖拽选中状态的实例名称 */
.instance-card.drag-selected .instance-name {
    color: #667eea;
    font-weight: 600;
}

/* 拖拽过程中的容器样式 */
.instances-list-container.dragging {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    cursor: crosshair;
}

/* 拖拽过程中的实例卡片 */
.instances-list-container.dragging .instance-card {
    cursor: crosshair;
}

/* 拖拽过程中禁用悬停效果 */
.instances-list-container.dragging .instance-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transform: none;
}

/* 键盘辅助选择提示 */
.drag-select-hint {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 15px;
    font-size: 14px;
    color: #6c757d;
    line-height: 1.4;
}

.drag-select-hint strong {
    color: #495057;
}

.drag-select-hint .hint-item {
    margin-bottom: 4px;
}

.drag-select-hint .hint-item:last-child {
    margin-bottom: 0;
}

/* 键盘快捷键标识 */
.keyboard-key {
    display: inline-block;
    background: #e9ecef;
    border: 1px solid #ced4da;
    border-radius: 3px;
    padding: 2px 6px;
    font-size: 12px;
    font-family: monospace;
    color: #495057;
    margin: 0 2px;
}

/* 选择计数器增强 */
.selection-counter {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #495057;
}

.selection-counter .count-badge {
    background: #667eea;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    min-width: 20px;
    text-align: center;
}

/* 拖拽选择状态指示器 */
.drag-select-status {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(102, 126, 234, 0.9);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;
    z-index: 1000;
}

.instances-list-container.dragging .drag-select-status {
    opacity: 1;
}

/* 多选模式下的实例卡片动画 */
.instance-card {
    transition: all 0.2s ease;
}

.instance-card.drag-selecting {
    background: rgba(102, 126, 234, 0.05);
    border-color: rgba(102, 126, 234, 0.3);
}

/* 范围选择预览 */
.instance-card.range-preview {
    background: rgba(102, 126, 234, 0.08);
    border-color: rgba(102, 126, 234, 0.4);
    border-style: dashed;
    animation: rangePreview 0.3s ease;
}

/* Ctrl选择反馈 */
.instance-card.ctrl-selected {
    animation: ctrlSelect 0.3s ease;
}

/* 范围选择反馈 */
.instance-card.range-selected {
    animation: rangeSelect 0.3s ease;
}

/* 动画定义 */
@keyframes rangePreview {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

@keyframes ctrlSelect {
    0% { transform: scale(1); box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
    50% { transform: scale(1.05); box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4); }
    100% { transform: scale(1); box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2); }
}

@keyframes rangeSelect {
    0% { background: rgba(102, 126, 234, 0.1); }
    50% { background: rgba(102, 126, 234, 0.2); }
    100% { background: rgba(102, 126, 234, 0.1); }
}

/* 移动设备适配 */
@media (max-width: 768px) {
    .drag-selection-box {
        border-width: 1px;
    }
    
    .drag-select-hint {
        font-size: 13px;
        padding: 10px;
    }
    
    .keyboard-key {
        font-size: 11px;
        padding: 1px 4px;
    }
    
    .selection-counter {
        font-size: 14px;
    }
    
    .selection-counter .count-badge {
        font-size: 11px;
        padding: 1px 6px;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .instances-list-container {
        /* 在触摸设备上禁用拖拽选择，避免与滚动冲突 */
    }
    
    .drag-select-hint .hint-item:first-child {
        display: none; /* 隐藏拖拽提示 */
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .drag-selection-box {
        border-color: #000;
        background: rgba(0, 0, 0, 0.1);
    }
    
    .instance-card.drag-selected {
        border-color: #000;
        background: rgba(0, 0, 0, 0.05);
    }
    
    .selection-counter .count-badge {
        background: #000;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    .drag-selection-box,
    .instance-card,
    .drag-select-status {
        transition: none;
    }
    
    .instance-card.drag-selected {
        transform: none;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .drag-select-hint {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .drag-select-hint strong {
        color: #f7fafc;
    }
    
    .keyboard-key {
        background: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }
    
    .drag-select-status {
        background: rgba(102, 126, 234, 0.95);
    }
}

/* 打印样式 */
@media print {
    .drag-selection-box,
    .drag-select-hint,
    .drag-select-status {
        display: none !important;
    }
    
    .instance-card.drag-selected {
        background: none !important;
        border-color: #000 !important;
        box-shadow: none !important;
        transform: none !important;
    }
}
