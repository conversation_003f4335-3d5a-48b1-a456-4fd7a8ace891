package main

import (
	"embed"
	"fmt"
	"log"
	"os"
	"os/exec"
	"runtime"
	"strconv"
	"time"

	"aliyun-manager/pkg/web"
)

//go:embed web/templates/*
var templatesFS embed.FS

//go:embed web/static/*
var staticFS embed.FS

func main() {
	// 解析命令行参数
	var webPort int = 8080

	// 简单的参数解析，只支持端口设置
	args := os.Args[1:]
	for i, arg := range args {
		switch arg {
		case "--port", "-p":
			if i+1 < len(args) {
				if port, err := strconv.Atoi(args[i+1]); err == nil {
					webPort = port
				}
			}
		case "--help", "-h":
			showHelp()
			return
		}
	}

	// 启动Web服务器
	server := web.NewServer(webPort)

	// 设置嵌入的文件系统
	server.SetEmbeddedFS(templatesFS, staticFS)

	// 在单独的goroutine中启动服务器，以便我们可以在启动后执行其他操作
	serverErr := make(chan error, 1)
	go func() {
		serverErr <- server.Start()
	}()

	// 等待一小段时间确保服务器启动
	time.Sleep(500 * time.Millisecond)

	// 构建URL并尝试打开浏览器
	url := fmt.Sprintf("http://localhost:%d", webPort)
	if err := openBrowser(url); err != nil {
		log.Printf("⚠️  无法自动打开浏览器: %v", err)
		log.Printf("🌐 请手动在浏览器中访问: %s", url)
	} else {
		log.Printf("🚀 浏览器已自动打开: %s", url)
	}

	// 等待服务器错误或阻塞
	if err := <-serverErr; err != nil {
		log.Fatalf("Web服务器启动失败: %v", err)
	}
}

// openBrowser 跨平台打开默认浏览器
func openBrowser(url string) error {
	var cmd string
	var args []string

	switch runtime.GOOS {
	case "windows":
		cmd = "rundll32"
		args = []string{"url.dll,FileProtocolHandler", url}
	case "darwin":
		cmd = "open"
		args = []string{url}
	case "linux":
		cmd = "xdg-open"
		args = []string{url}
	default:
		return fmt.Errorf("不支持的操作系统: %s", runtime.GOOS)
	}

	return exec.Command(cmd, args...).Start()
}

func showHelp() {
	log.Println("阿里云ECS管理器 - Web模式")
	log.Println("用法:")
	log.Println("  go run main.go [选项]")
	log.Println("")
	log.Println("选项:")
	log.Println("  --port, -p <端口>    指定Web服务器端口 (默认: 8080)")
	log.Println("  --help, -h          显示帮助信息")
	log.Println("")
	log.Println("示例:")
	log.Println("  go run main.go                # 使用默认端口8080启动")
	log.Println("  go run main.go --port 9000    # 使用端口9000启动")
	log.Println("")
	log.Println("注意:")
	log.Println("  启动后会自动在默认浏览器中打开Web界面")
}
