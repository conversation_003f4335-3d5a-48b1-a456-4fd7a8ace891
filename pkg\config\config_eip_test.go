package config

import (
	"testing"

	"github.com/alibabacloud-go/tea/tea"
)

// TestECSInstanceConfig_EIPValidation 测试EIP相关配置验证
func TestECSInstanceConfig_EIPValidation(t *testing.T) {
	tests := []struct {
		name        string
		config      *ECSInstanceConfig
		expectError bool
		errorMsg    string
	}{
		{
			name: "启用EIP且配置完整",
			config: &ECSInstanceConfig{
				RegionId:                "cn-hangzhou",
				ImageId:                 "ubuntu_20_04_x64_20G_alibase_20210420.vhd",
				InstanceType:            "ecs.t5-lc1m1.small",
				SecurityGroupId:         "sg-test123",
				VSwitchId:               "vsw-test123",
				InternetMaxBandwidthOut: tea.Int(5),
				SystemDiskSize:          tea.Int(40),
				EnableEIP:               true,
				EIPBandwidth:            tea.Int(10),
				EIPChargeType:           "PayByTraffic",
				EIPName:                 "test-eip",
				EIPDescription:          "Test EIP",
			},
			expectError: false,
		},
		{
			name: "启用EIP但未设置带宽，应使用默认值",
			config: &ECSInstanceConfig{
				RegionId:                "cn-hangzhou",
				ImageId:                 "ubuntu_20_04_x64_20G_alibase_20210420.vhd",
				InstanceType:            "ecs.t5-lc1m1.small",
				SecurityGroupId:         "sg-test123",
				VSwitchId:               "vsw-test123",
				InternetMaxBandwidthOut: tea.Int(5),
				SystemDiskSize:          tea.Int(40),
				EnableEIP:               true,
			},
			expectError: false,
		},
		{
			name: "启用EIP但计费类型无效",
			config: &ECSInstanceConfig{
				RegionId:                "cn-hangzhou",
				ImageId:                 "ubuntu_20_04_x64_20G_alibase_20210420.vhd",
				InstanceType:            "ecs.t5-lc1m1.small",
				SecurityGroupId:         "sg-test123",
				VSwitchId:               "vsw-test123",
				InternetMaxBandwidthOut: tea.Int(5),
				SystemDiskSize:          tea.Int(40),
				EnableEIP:               true,
				EIPChargeType:           "InvalidChargeType",
			},
			expectError: true,
			errorMsg:    "eipChargeType 必须是 PayByTraffic 或 PayByBandwidth",
		},
		{
			name: "启用EIP但带宽为0",
			config: &ECSInstanceConfig{
				RegionId:                "cn-hangzhou",
				ImageId:                 "ubuntu_20_04_x64_20G_alibase_20210420.vhd",
				InstanceType:            "ecs.t5-lc1m1.small",
				SecurityGroupId:         "sg-test123",
				VSwitchId:               "vsw-test123",
				InternetMaxBandwidthOut: tea.Int(5),
				SystemDiskSize:          tea.Int(40),
				EnableEIP:               true,
				EIPBandwidth:            tea.Int(0),
			},
			expectError: true,
			errorMsg:    "eipBandwidth 必须大于0",
		},
		{
			name: "未启用EIP",
			config: &ECSInstanceConfig{
				RegionId:                "cn-hangzhou",
				ImageId:                 "ubuntu_20_04_x64_20G_alibase_20210420.vhd",
				InstanceType:            "ecs.t5-lc1m1.small",
				SecurityGroupId:         "sg-test123",
				VSwitchId:               "vsw-test123",
				InternetMaxBandwidthOut: tea.Int(5),
				SystemDiskSize:          tea.Int(40),
				EnableEIP:               false,
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()

			if tt.expectError {
				if err == nil {
					t.Errorf("期望出现错误，但没有错误")
				} else if err.Error() != tt.errorMsg {
					t.Errorf("错误消息不匹配，期望: %s, 实际: %s", tt.errorMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("不期望出现错误，但出现了错误: %v", err)
				}
			}
		})
	}
}

// TestECSInstanceConfig_EIPDefaults 测试EIP默认值设置
func TestECSInstanceConfig_EIPDefaults(t *testing.T) {
	config := &ECSInstanceConfig{
		RegionId:                "cn-hangzhou",
		ImageId:                 "ubuntu_20_04_x64_20G_alibase_20210420.vhd",
		InstanceType:            "ecs.t5-lc1m1.small",
		SecurityGroupId:         "sg-test123",
		VSwitchId:               "vsw-test123",
		InternetMaxBandwidthOut: tea.Int(10),
		SystemDiskSize:          tea.Int(40),
		EnableEIP:               true,
		// 不设置EIPChargeType和EIPBandwidth，测试默认值
	}

	err := config.Validate()
	if err != nil {
		t.Errorf("配置验证失败: %v", err)
	}

	// 验证默认值
	if config.EIPChargeType != "PayByTraffic" {
		t.Errorf("EIP计费类型默认值应该是PayByTraffic，实际是: %s", config.EIPChargeType)
	}

	if config.EIPBandwidth == nil {
		t.Error("EIP带宽应该被设置为InternetMaxBandwidthOut的值")
	} else if *config.EIPBandwidth != 10 {
		t.Errorf("EIP带宽应该是10，实际是: %d", *config.EIPBandwidth)
	}
}

// TestECSInstanceConfig_EIPBandwidthInheritance 测试EIP带宽继承
func TestECSInstanceConfig_EIPBandwidthInheritance(t *testing.T) {
	tests := []struct {
		name                    string
		internetMaxBandwidthOut *int
		eipBandwidth            *int
		expectedEIPBandwidth    *int
	}{
		{
			name:                    "EIP带宽未设置，应继承公网带宽",
			internetMaxBandwidthOut: tea.Int(5),
			eipBandwidth:            nil,
			expectedEIPBandwidth:    tea.Int(5),
		},
		{
			name:                    "EIP带宽已设置，应使用设置值",
			internetMaxBandwidthOut: tea.Int(5),
			eipBandwidth:            tea.Int(10),
			expectedEIPBandwidth:    tea.Int(10),
		},
		{
			name:                    "公网带宽和EIP带宽都未设置",
			internetMaxBandwidthOut: nil,
			eipBandwidth:            nil,
			expectedEIPBandwidth:    nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := &ECSInstanceConfig{
				RegionId:                "cn-hangzhou",
				ImageId:                 "ubuntu_20_04_x64_20G_alibase_20210420.vhd",
				InstanceType:            "ecs.t5-lc1m1.small",
				SecurityGroupId:         "sg-test123",
				VSwitchId:               "vsw-test123",
				InternetMaxBandwidthOut: tt.internetMaxBandwidthOut,
				SystemDiskSize:          tea.Int(40),
				EnableEIP:               true,
				EIPBandwidth:            tt.eipBandwidth,
			}

			// 验证配置（这会触发默认值设置）
			config.Validate()

			// 检查EIP带宽是否符合预期
			if tt.expectedEIPBandwidth == nil {
				if config.EIPBandwidth != nil {
					t.Errorf("EIP带宽应该是nil，实际是: %v", config.EIPBandwidth)
				}
			} else {
				if config.EIPBandwidth == nil {
					t.Errorf("EIP带宽不应该是nil，期望: %d", *tt.expectedEIPBandwidth)
				} else if *config.EIPBandwidth != *tt.expectedEIPBandwidth {
					t.Errorf("EIP带宽不匹配，期望: %d, 实际: %d", *tt.expectedEIPBandwidth, *config.EIPBandwidth)
				}
			}
		})
	}
}

// TestECSInstanceConfig_EIPChargeTypes 测试EIP计费类型验证
func TestECSInstanceConfig_EIPChargeTypes(t *testing.T) {
	validChargeTypes := []string{"PayByTraffic", "PayByBandwidth"}
	invalidChargeTypes := []string{"", "PayByHour", "PayByMonth", "Invalid"}

	baseConfig := &ECSInstanceConfig{
		RegionId:                "cn-hangzhou",
		ImageId:                 "ubuntu_20_04_x64_20G_alibase_20210420.vhd",
		InstanceType:            "ecs.t5-lc1m1.small",
		SecurityGroupId:         "sg-test123",
		VSwitchId:               "vsw-test123",
		InternetMaxBandwidthOut: tea.Int(5),
		SystemDiskSize:          tea.Int(40),
		EnableEIP:               true,
		EIPBandwidth:            tea.Int(5),
	}

	// 测试有效的计费类型
	for _, chargeType := range validChargeTypes {
		t.Run("Valid_"+chargeType, func(t *testing.T) {
			config := *baseConfig
			config.EIPChargeType = chargeType

			err := config.Validate()
			if err != nil {
				t.Errorf("有效的计费类型 %s 应该通过验证，但出现错误: %v", chargeType, err)
			}
		})
	}

	// 测试无效的计费类型
	for _, chargeType := range invalidChargeTypes {
		t.Run("Invalid_"+chargeType, func(t *testing.T) {
			config := *baseConfig
			config.EIPChargeType = chargeType

			err := config.Validate()
			if chargeType == "" {
				// 空字符串应该被设置为默认值
				if err != nil {
					t.Errorf("空计费类型应该被设置为默认值，但出现错误: %v", err)
				}
				if config.EIPChargeType != "PayByTraffic" {
					t.Errorf("空计费类型应该被设置为PayByTraffic，实际是: %s", config.EIPChargeType)
				}
			} else {
				// 其他无效值应该报错
				if err == nil {
					t.Errorf("无效的计费类型 %s 应该报错", chargeType)
				}
			}
		})
	}
}
