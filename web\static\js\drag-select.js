/**
 * 拖拽多选组件
 * 支持鼠标拖拽框选、Ctrl+点击切换、Shift+点击范围选择
 */
class DragSelectManager {
    constructor(options = {}) {
        this.container = null;
        this.items = [];
        this.selectedItems = new Set();
        this.lastSelectedIndex = -1;
        
        // 拖拽状态
        this.isDragging = false;
        this.startX = 0;
        this.startY = 0;
        this.currentX = 0;
        this.currentY = 0;
        this.animationFrame = null;
        
        // 选择框元素
        this.selectionBox = null;
        
        // 配置选项
        this.options = {
            containerSelector: '.instances-list-container',
            itemSelector: '.instance-card',
            checkboxSelector: 'input[name="selectedInstances"]',
            enableDragSelect: true,
            enableKeyboardSelect: true,
            ...options
        };
        
        // 绑定方法上下文
        this.handleMouseDown = this.handleMouseDown.bind(this);
        this.handleMouseMove = this.handleMouseMove.bind(this);
        this.handleMouseUp = this.handleMouseUp.bind(this);
        this.handleItemClick = this.handleItemClick.bind(this);
        this.handleKeyDown = this.handleKeyDown.bind(this);
        this.handleKeyUp = this.handleKeyUp.bind(this);
        
        // 键盘状态
        this.keys = {
            ctrl: false,
            shift: false
        };
    }
    
    /**
     * 初始化拖拽选择功能
     */
    init() {
        this.container = document.querySelector(this.options.containerSelector);
        if (!this.container) {
            console.warn('DragSelect: Container not found');
            return false;
        }
        
        this.updateItems();
        this.createSelectionBox();
        this.bindEvents();
        
        console.log('DragSelect: Initialized successfully');
        return true;
    }
    
    /**
     * 更新可选择的项目列表
     */
    updateItems() {
        this.items = Array.from(this.container.querySelectorAll(this.options.itemSelector));
        console.log(`DragSelect: Found ${this.items.length} items`);
    }
    
    /**
     * 创建选择框元素
     */
    createSelectionBox() {
        this.selectionBox = document.createElement('div');
        this.selectionBox.className = 'drag-selection-box';
        this.selectionBox.style.display = 'none';
        document.body.appendChild(this.selectionBox);
    }
    
    /**
     * 绑定事件监听器
     */
    bindEvents() {
        if (this.options.enableDragSelect) {
            this.container.addEventListener('mousedown', this.handleMouseDown);
            document.addEventListener('mousemove', this.handleMouseMove);
            document.addEventListener('mouseup', this.handleMouseUp);
        }
        
        if (this.options.enableKeyboardSelect) {
            document.addEventListener('keydown', this.handleKeyDown);
            document.addEventListener('keyup', this.handleKeyUp);
            this.container.addEventListener('click', this.handleItemClick);
        }
        
        // 防止文本选择
        this.container.addEventListener('selectstart', (e) => {
            if (this.isDragging) {
                e.preventDefault();
            }
        });
    }
    
    /**
     * 处理鼠标按下事件
     */
    handleMouseDown(e) {
        // 如果点击的是复选框或其他交互元素，不启动拖拽
        if (e.target.matches('input, button, a, label')) {
            return;
        }

        // 如果点击的是实例卡片，检查是否需要特殊处理
        const clickedItem = e.target.closest(this.options.itemSelector);
        if (clickedItem && (this.keys.ctrl || this.keys.shift)) {
            return; // 让handleItemClick处理
        }

        this.isDragging = true;
        this.startX = e.clientX;
        this.startY = e.clientY;
        this.currentX = e.clientX;
        this.currentY = e.clientY;

        // 如果不是Ctrl键，清空之前的选择
        if (!this.keys.ctrl) {
            this.clearSelection();
        }

        // 添加拖拽状态类
        this.container.classList.add('dragging');

        this.showSelectionBox();
        e.preventDefault();
    }
    
    /**
     * 处理鼠标移动事件
     */
    handleMouseMove(e) {
        if (!this.isDragging) return;

        this.currentX = e.clientX;
        this.currentY = e.clientY;

        // 使用 requestAnimationFrame 优化性能
        if (!this.animationFrame) {
            this.animationFrame = requestAnimationFrame(() => {
                this.updateSelectionBox();
                this.updateSelection();
                this.animationFrame = null;
            });
        }
    }
    
    /**
     * 处理鼠标释放事件
     */
    handleMouseUp(e) {
        if (!this.isDragging) return;

        this.isDragging = false;

        // 移除拖拽状态类
        this.container.classList.remove('dragging');

        this.hideSelectionBox();

        // 同步到复选框
        this.syncToCheckboxes();

        // 触发选择变化事件
        this.onSelectionChange();
    }
    
    /**
     * 处理项目点击事件（键盘辅助选择）
     */
    handleItemClick(e) {
        const clickedItem = e.target.closest(this.options.itemSelector);
        if (!clickedItem) return;

        const itemIndex = this.items.indexOf(clickedItem);
        if (itemIndex === -1) return;

        if (this.keys.ctrl) {
            // Ctrl+点击：切换选择状态
            this.toggleItem(itemIndex);
            this.lastSelectedIndex = itemIndex;

            // 添加视觉反馈
            this.showTemporaryFeedback(clickedItem, 'ctrl-selected');
        } else if (this.keys.shift && this.lastSelectedIndex !== -1) {
            // Shift+点击：范围选择
            this.clearRangePreview();
            this.selectRange(this.lastSelectedIndex, itemIndex);

            // 添加视觉反馈
            this.showTemporaryFeedback(clickedItem, 'range-selected');
        } else {
            // 普通点击：让复选框处理
            return;
        }

        e.preventDefault();
        this.syncToCheckboxes();
        this.onSelectionChange();
    }
    
    /**
     * 处理键盘按下事件
     */
    handleKeyDown(e) {
        if (e.key === 'Control') {
            this.keys.ctrl = true;
        } else if (e.key === 'Shift') {
            this.keys.shift = true;
        }
    }
    
    /**
     * 处理键盘释放事件
     */
    handleKeyUp(e) {
        if (e.key === 'Control') {
            this.keys.ctrl = false;
        } else if (e.key === 'Shift') {
            this.keys.shift = false;
        }
    }
    
    /**
     * 显示选择框
     */
    showSelectionBox() {
        this.selectionBox.style.display = 'block';
        this.updateSelectionBox();
    }
    
    /**
     * 隐藏选择框
     */
    hideSelectionBox() {
        this.selectionBox.style.display = 'none';
    }
    
    /**
     * 更新选择框位置和大小
     */
    updateSelectionBox() {
        const left = Math.min(this.startX, this.currentX);
        const top = Math.min(this.startY, this.currentY);
        const width = Math.abs(this.currentX - this.startX);
        const height = Math.abs(this.currentY - this.startY);
        
        this.selectionBox.style.left = left + 'px';
        this.selectionBox.style.top = top + 'px';
        this.selectionBox.style.width = width + 'px';
        this.selectionBox.style.height = height + 'px';
    }
    
    /**
     * 更新选择状态（拖拽过程中）
     */
    updateSelection() {
        const selectionRect = {
            left: Math.min(this.startX, this.currentX),
            top: Math.min(this.startY, this.currentY),
            right: Math.max(this.startX, this.currentX),
            bottom: Math.max(this.startY, this.currentY)
        };
        
        this.items.forEach((item, index) => {
            const itemRect = item.getBoundingClientRect();
            const isIntersecting = this.isRectIntersecting(selectionRect, itemRect);
            
            if (isIntersecting) {
                this.selectedItems.add(index);
                item.classList.add('drag-selected');
            } else if (!this.keys.ctrl) {
                // 如果不是Ctrl模式，移除不在选择框内的项目
                this.selectedItems.delete(index);
                item.classList.remove('drag-selected');
            }
        });
    }
    
    /**
     * 检查两个矩形是否相交
     */
    isRectIntersecting(rect1, rect2) {
        return !(rect1.right < rect2.left || 
                rect1.left > rect2.right || 
                rect1.bottom < rect2.top || 
                rect1.top > rect2.bottom);
    }
    
    /**
     * 切换项目选择状态
     */
    toggleItem(index) {
        if (this.selectedItems.has(index)) {
            this.selectedItems.delete(index);
            this.items[index].classList.remove('drag-selected');
        } else {
            this.selectedItems.add(index);
            this.items[index].classList.add('drag-selected');
        }
    }
    
    /**
     * 范围选择
     */
    selectRange(startIndex, endIndex) {
        const start = Math.min(startIndex, endIndex);
        const end = Math.max(startIndex, endIndex);
        
        for (let i = start; i <= end; i++) {
            this.selectedItems.add(i);
            this.items[i].classList.add('drag-selected');
        }
    }
    
    /**
     * 清空选择
     */
    clearSelection() {
        this.selectedItems.clear();
        this.items.forEach(item => {
            item.classList.remove('drag-selected');
        });
    }
    
    /**
     * 同步选择状态到复选框
     */
    syncToCheckboxes() {
        this.items.forEach((item, index) => {
            const checkbox = item.querySelector(this.options.checkboxSelector);
            if (checkbox) {
                checkbox.checked = this.selectedItems.has(index);
            }
        });
    }
    
    /**
     * 从复选框同步选择状态
     */
    syncFromCheckboxes() {
        this.selectedItems.clear();
        this.items.forEach((item, index) => {
            const checkbox = item.querySelector(this.options.checkboxSelector);
            if (checkbox && checkbox.checked) {
                this.selectedItems.add(index);
                item.classList.add('drag-selected');
            } else {
                item.classList.remove('drag-selected');
            }
        });
    }
    
    /**
     * 获取选中的项目
     */
    getSelectedItems() {
        return Array.from(this.selectedItems).map(index => this.items[index]);
    }
    
    /**
     * 获取选中的项目值（通常是复选框的value）
     */
    getSelectedValues() {
        return Array.from(this.selectedItems).map(index => {
            const checkbox = this.items[index].querySelector(this.options.checkboxSelector);
            return checkbox ? checkbox.value : null;
        }).filter(value => value !== null);
    }
    
    /**
     * 选择变化回调
     */
    onSelectionChange() {
        // 触发自定义事件
        const event = new CustomEvent('dragselect:change', {
            detail: {
                selectedItems: this.getSelectedItems(),
                selectedValues: this.getSelectedValues(),
                count: this.selectedItems.size
            }
        });
        this.container.dispatchEvent(event);
        
        // 调用现有的更新函数（如果存在）
        if (typeof updateSelectedCount === 'function') {
            updateSelectedCount();
        }
    }
    
    /**
     * 显示临时视觉反馈
     */
    showTemporaryFeedback(element, className) {
        element.classList.add(className);
        setTimeout(() => {
            element.classList.remove(className);
        }, 300);
    }

    /**
     * 清除范围选择预览
     */
    clearRangePreview() {
        this.items.forEach(item => {
            item.classList.remove('range-preview');
        });
    }

    /**
     * 显示范围选择预览
     */
    showRangePreview(startIndex, endIndex) {
        this.clearRangePreview();
        const start = Math.min(startIndex, endIndex);
        const end = Math.max(startIndex, endIndex);

        for (let i = start; i <= end; i++) {
            if (this.items[i]) {
                this.items[i].classList.add('range-preview');
            }
        }
    }

    /**
     * 获取选择统计信息
     */
    getSelectionStats() {
        return {
            total: this.items.length,
            selected: this.selectedItems.size,
            percentage: this.items.length > 0 ? Math.round((this.selectedItems.size / this.items.length) * 100) : 0
        };
    }

    /**
     * 销毁实例
     */
    destroy() {
        // 移除事件监听器
        if (this.container) {
            this.container.removeEventListener('mousedown', this.handleMouseDown);
            this.container.removeEventListener('click', this.handleItemClick);
            this.container.classList.remove('dragging');
        }

        document.removeEventListener('mousemove', this.handleMouseMove);
        document.removeEventListener('mouseup', this.handleMouseUp);
        document.removeEventListener('keydown', this.handleKeyDown);
        document.removeEventListener('keyup', this.handleKeyUp);

        // 移除选择框
        if (this.selectionBox && this.selectionBox.parentNode) {
            this.selectionBox.parentNode.removeChild(this.selectionBox);
        }

        // 清理状态
        this.clearSelection();
        this.clearRangePreview();

        console.log('DragSelect: Destroyed');
    }
}

// 导出到全局作用域（兼容现有代码）
window.DragSelectManager = DragSelectManager;
