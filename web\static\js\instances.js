// 实例管理页面JavaScript

let instancesData = [];
let filteredInstances = [];
let selectedInstances = [];
let commandHistory = [];
let currentViewMode = 'compact'; // 'detailed' 或 ''

// 全局变量
// (保留其他全局变量定义)

// 专门用于文件下载的函数（返回文件内容，不是JSON）
async function downloadFileViaHTTP(params) {
    try {
        const response = await fetch('/api/instances/download-via-http', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(params)
        });

        // 检查响应状态
        if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
        }

        // 检查Content-Type
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            // 这可能是通配符下载响应或错误响应
            const jsonResult = await response.json();

            if (jsonResult.success && jsonResult.message === 'wildcard_download_ready') {
                // 通配符下载响应，开始循环下载
                return await handleWildcardDownload(jsonResult.data);
            } else if (!jsonResult.success) {
                // 错误响应
                throw new Error(jsonResult.error || '下载失败');
            }
        } else if (contentType && contentType.includes('text/html')) {
            // 这是HTML响应（通配符下载结果页面）
            const htmlContent = await response.text();

            // 在新窗口中显示HTML内容
            const newWindow = window.open('', '_blank');
            newWindow.document.write(htmlContent);
            newWindow.document.close();

            return {
                success: true,
                message: '通配符下载完成，已在新窗口中打开HTTP服务器页面'
            };
        }

        // 获取文件名
        const contentDisposition = response.headers.get('content-disposition');
        let fileName = 'downloaded_file';
        if (contentDisposition) {
            const fileNameMatch = contentDisposition.match(/filename="(.+)"/);
            if (fileNameMatch) {
                fileName = fileNameMatch[1];
            }
        }

        // 获取文件内容
        const blob = await response.blob();

        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        return {
            success: true,
            fileName: fileName,
            size: blob.size,
            message: `文件 ${fileName} 下载成功`
        };
    } catch (error) {
        console.error('文件下载失败:', error);
        throw error;
    }
}

// 处理通配符下载
async function handleWildcardDownload(data) {
    const { serverURL, pattern } = data;

    try {
        // 获取HTTP服务器的文件列表
        const listResponse = await fetch(serverURL);
        if (!listResponse.ok) {
            throw new Error(`无法访问HTTP服务器: ${listResponse.status}`);
        }

        const htmlContent = await listResponse.text();

        // 解析HTML内容，提取文件链接
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');
        const links = doc.querySelectorAll('a[href]');

        const matchedFiles = [];
        const patternRegex = convertWildcardToRegex(pattern);

        links.forEach(link => {
            const href = link.getAttribute('href');
            const fileName = decodeURIComponent(href);

            // 跳过目录和特殊链接
            if (href === '../' || href.startsWith('?') || href.endsWith('/')) {
                return;
            }

            // 检查文件名是否匹配通配符模式
            if (patternRegex.test(fileName)) {
                matchedFiles.push({
                    name: fileName,
                    url: serverURL + href
                });
            }
        });

        if (matchedFiles.length === 0) {
            throw new Error('未找到匹配的文件');
        }

        console.log(`🔍 通配符匹配到 ${matchedFiles.length} 个文件:`, matchedFiles.map(f => f.name));

        // 循环下载所有匹配的文件
        const downloadResults = [];
        for (const file of matchedFiles) {
            try {
                console.log(`📥 下载文件: ${file.name}`);
                const fileResponse = await fetch(file.url);

                if (!fileResponse.ok) {
                    throw new Error(`下载失败: ${fileResponse.status}`);
                }

                const blob = await fileResponse.blob();

                // 创建下载链接
                const downloadUrl = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = downloadUrl;
                a.download = file.name;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(downloadUrl);

                downloadResults.push({
                    name: file.name,
                    success: true,
                    size: blob.size
                });

                console.log(`✅ 文件下载成功: ${file.name} (${blob.size} bytes)`);

                // 添加小延迟避免浏览器阻止多个下载
                await new Promise(resolve => setTimeout(resolve, 500));

            } catch (error) {
                console.error(`❌ 下载文件失败 ${file.name}:`, error);
                downloadResults.push({
                    name: file.name,
                    success: false,
                    error: error.message
                });
            }
        }

        const successCount = downloadResults.filter(r => r.success).length;
        const failCount = downloadResults.filter(r => !r.success).length;

        return {
            success: true,
            message: `通配符下载完成: 成功 ${successCount} 个，失败 ${failCount} 个`,
            data: {
                total: matchedFiles.length,
                success: successCount,
                failed: failCount,
                results: downloadResults
            }
        };

    } catch (error) {
        console.error('通配符下载失败:', error);
        throw new Error(`通配符下载失败: ${error.message}`);
    }
}

// 将通配符模式转换为正则表达式
function convertWildcardToRegex(pattern) {
    // 提取文件名部分（去掉路径）
    const fileName = pattern.split('/').pop();

    // 转义正则表达式特殊字符，但保留通配符
    let regexPattern = fileName
        .replace(/[.+^${}()|[\]\\]/g, '\\$&') // 转义特殊字符
        .replace(/\*/g, '.*')                  // * 匹配任意字符
        .replace(/\?/g, '.');                  // ? 匹配单个字符

    return new RegExp(`^${regexPattern}$`, 'i'); // 不区分大小写
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadCommandHistory();
    loadViewPreference();
    setupEventListeners();
    initializeCreateInstanceModal();
    initializeScriptUpload();

    // 自动加载默认地域的实例列表
    const regionSelect = document.getElementById('regionId');
    if (regionSelect && regionSelect.value) {
        loadInstances();
    }
});

// 设置事件监听器
function setupEventListeners() {
    // 地域选择变化时自动加载实例
    document.getElementById('regionId').addEventListener('change', function() {
        if (this.value) {
            loadInstances();
        }
    });
}

// 加载实例列表
async function loadInstances() {
    const regionId = document.getElementById('regionId').value;
    if (!regionId) {
        showAlert('❌ 请先选择地域', 'error');
        return;
    }

    showLoading(true);
    try {
        const result = await apiRequest('/api/instances?regionId=' + encodeURIComponent(regionId), {
            method: 'GET'
        });
        
        if (result.success) {
            instancesData = result.data || [];
            filteredInstances = [...instancesData];
            renderInstancesList();
            document.getElementById('instancesContainer').style.display = 'block';
            showAlert('✅ 成功加载 ' + instancesData.length + ' 个实例', 'success');
        } else {
            showAlert('❌ 加载实例失败: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('❌ 加载实例请求失败: ' + error.message, 'error');
    }
    showLoading(false);
}

// 渲染实例列表 - 复刻释放页面功能
function renderInstancesList() {
    const container = document.getElementById('instancesList');
    if (!container) return;

    if (filteredInstances.length === 0) {
        container.innerHTML = '<div class="empty-state">当前地域没有找到实例</div>';
        return;
    }

    let html = '';
    filteredInstances.forEach(instance => {
        const statusClass = getStatusClass(instance.status);
        const protectionIcon = instance.deletionProtection ? '🛡️' : '❌';

        html += `
            <div class="instance-card"
                 data-instance-id="${instance.instanceId}"
                 onclick="handleCardClick(event, '${instance.instanceId}')">
                <div class="instance-select">
                    <input type="checkbox"
                           name="selectedInstances"
                           value="${instance.instanceId}"
                           id="instance-${instance.instanceId}"
                           onchange="handleCheckboxChange(this)"
                           onclick="event.stopPropagation()">
                </div>
                <div class="instance-header">
                    <div class="instance-info">
                        <div class="instance-name">${instance.instanceName || instance.hostName || '未命名实例'}</div>
                        <div class="instance-id">${instance.instanceId}</div>
                    </div>
                    <div class="instance-status ${statusClass}">${getStatusText(instance.status)}</div>
                </div>
                <div class="instance-details">
                    <div class="detail-item">
                        <div class="detail-label">实例规格</div>
                        <div class="detail-value">${instance.instanceType}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">CPU/内存</div>
                        <div class="detail-value">${instance.cpu}核/${instance.memory}MB</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">操作系统</div>
                        <div class="detail-value">${instance.osName || instance.osType || '未知'}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">地域/可用区</div>
                        <div class="detail-value">${instance.regionId}/${instance.zoneId}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">创建时间</div>
                        <div class="detail-value">${formatDateTime(instance.creationTime)}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">网络类型</div>
                        <div class="detail-value">${getNetworkTypeDisplay(instance)}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">公网IP</div>
                        <div class="detail-value">${getPublicIpDisplay(instance)}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">私网IP</div>
                        <div class="detail-value">${instance.privateIpAddress || '-'}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">删除保护</div>
                        <div class="detail-value">${protectionIcon} ${instance.deletionProtection ? '已启用' : '未启用'}</div>
                    </div>
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
    updateSelectedCount();
    updateSelectedSummary();

    // 初始化拖拽选择功能
    initDragSelect();
}

// 处理卡片点击事件 - 已移动到文件后面，避免重复定义

// 处理复选框变化事件
function handleCheckboxChange(checkbox) {
    const instanceId = checkbox.value;

    if (checkbox.checked) {
        if (!selectedInstances.includes(instanceId)) {
            selectedInstances.push(instanceId);
        }
    } else {
        const index = selectedInstances.indexOf(instanceId);
        if (index > -1) {
            selectedInstances.splice(index, 1);
        }
    }

    updateSelectedCount();
    updateSelectedSummary();
    updateSelectAllCheckbox();
}

// 初始化拖拽选择功能
function initDragSelect() {
    const container = document.getElementById('instancesListContainer');
    if (!container) return;

    let isSelecting = false;
    let startX, startY;
    let selectionBox = null;

    container.addEventListener('mousedown', (e) => {
        // 只在空白区域开始拖拽选择
        if (e.target.closest('.instance-card')) return;

        isSelecting = true;
        startX = e.clientX;
        startY = e.clientY;

        // 创建选择框
        selectionBox = document.createElement('div');
        selectionBox.className = 'selection-box';
        selectionBox.style.cssText = `
            position: fixed;
            border: 2px dashed #007bff;
            background: rgba(0, 123, 255, 0.1);
            pointer-events: none;
            z-index: 1000;
            left: ${startX}px;
            top: ${startY}px;
            width: 0;
            height: 0;
        `;
        document.body.appendChild(selectionBox);

        // 显示拖拽状态
        const dragStatus = container.querySelector('.drag-select-status');
        if (dragStatus) {
            dragStatus.style.display = 'block';
        }

        e.preventDefault();
    });

    document.addEventListener('mousemove', (e) => {
        if (!isSelecting || !selectionBox) return;

        const currentX = e.clientX;
        const currentY = e.clientY;

        const left = Math.min(startX, currentX);
        const top = Math.min(startY, currentY);
        const width = Math.abs(currentX - startX);
        const height = Math.abs(currentY - startY);

        selectionBox.style.left = left + 'px';
        selectionBox.style.top = top + 'px';
        selectionBox.style.width = width + 'px';
        selectionBox.style.height = height + 'px';
    });

    document.addEventListener('mouseup', (e) => {
        if (!isSelecting) return;

        isSelecting = false;

        // 隐藏拖拽状态
        const dragStatus = container.querySelector('.drag-select-status');
        if (dragStatus) {
            dragStatus.style.display = 'none';
        }

        if (selectionBox) {
            // 获取选择框的位置
            const rect = selectionBox.getBoundingClientRect();

            // 查找与选择框相交的实例卡片
            const cards = container.querySelectorAll('.instance-card');
            cards.forEach(card => {
                const cardRect = card.getBoundingClientRect();

                // 检查是否相交
                if (rect.left < cardRect.right &&
                    rect.right > cardRect.left &&
                    rect.top < cardRect.bottom &&
                    rect.bottom > cardRect.top) {

                    const checkbox = card.querySelector('input[type="checkbox"]');
                    if (checkbox) {
                        checkbox.checked = true;
                        handleCheckboxChange(checkbox);
                    }
                }
            });

            // 移除选择框
            document.body.removeChild(selectionBox);
            selectionBox = null;
        }
    });
}

// 获取状态样式类
function getStatusClass(status) {
    const statusMap = {
        'Running': 'status-running',
        'Stopped': 'status-stopped',
        'Starting': 'status-starting',
        'Stopping': 'status-stopping'
    };
    return statusMap[status] || 'status-stopped';
}

// 获取状态文本
function getStatusText(status) {
    const statusMap = {
        'Running': '运行中',
        'Stopped': '已停止',
        'Starting': '启动中',
        'Stopping': '停止中'
    };
    return statusMap[status] || status;
}

// 格式化日期时间
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN');
}

// 切换实例选择
function toggleInstanceSelection(instanceId) {
    const index = selectedInstances.indexOf(instanceId);
    const isSelected = index > -1;

    if (isSelected) {
        selectedInstances.splice(index, 1);
    } else {
        selectedInstances.push(instanceId);
    }

    // 更新复选框状态
    const checkbox = document.getElementById(`instance-${instanceId}`);
    if (checkbox) {
        checkbox.checked = !isSelected;
    }

    // 更新卡片样式
    const card = document.querySelector(`[data-instance-id="${instanceId}"]`);
    if (card) {
        card.classList.toggle('selected', selectedInstances.includes(instanceId));
    }

    updateSelectedCount();
    updateSelectedSummary();
    updateSelectAllCheckbox();
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const isChecked = selectAllCheckbox.checked;
    
    if (isChecked) {
        selectedInstances = filteredInstances.map(instance => instance.instanceId);
    } else {
        selectedInstances = [];
    }
    
    // 更新所有复选框
    filteredInstances.forEach(instance => {
        const checkbox = document.getElementById(`instance-${instance.instanceId}`);
        if (checkbox) {
            checkbox.checked = isChecked;
        }
        
        const card = document.querySelector(`[data-instance-id="${instance.instanceId}"]`);
        if (card) {
            card.classList.toggle('selected', isChecked);
        }
    });
    
    updateSelectedCount();
}

// 更新选中数量显示
function updateSelectedCount() {
    const countElement = document.getElementById('selectedCount');
    if (countElement) {
        countElement.textContent = `已选择 ${selectedInstances.length} 个实例`;
    }

    // 更新密码设置按钮状态
    updatePasswordButtonState();

    // 更新下载按钮状态
    updateDownloadButtonState();

    // 同时更新选中实例摘要
    updateSelectedSummary();
}

// 更新全选复选框状态
function updateSelectAllCheckbox() {
    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
        const totalVisible = filteredInstances.length;
        const selectedVisible = selectedInstances.filter(id => 
            filteredInstances.some(instance => instance.instanceId === id)
        ).length;
        
        selectAllCheckbox.checked = totalVisible > 0 && selectedVisible === totalVisible;
        selectAllCheckbox.indeterminate = selectedVisible > 0 && selectedVisible < totalVisible;
    }
}

// 筛选实例
function filterInstances() {
    const statusFilter = document.getElementById('statusFilter').value;
    const searchInput = document.getElementById('searchInput').value.toLowerCase();
    
    filteredInstances = instancesData.filter(instance => {
        const matchesStatus = !statusFilter || instance.status === statusFilter;
        const matchesSearch = !searchInput || 
            instance.instanceName.toLowerCase().includes(searchInput) ||
            instance.instanceId.toLowerCase().includes(searchInput);
        
        return matchesStatus && matchesSearch;
    });
    
    renderInstancesList();
    updateSelectAllCheckbox();
}

// 设置命令
function setCommand(command) {
    document.getElementById('commandInput').value = command;
}

// 清空命令
function clearCommand() {
    document.getElementById('commandInput').value = '';
}

// 执行命令
async function executeCommand() {
    const regionId = document.getElementById('regionId').value;
    const commandContent = document.getElementById('commandInput').value.trim();
    
    if (!regionId) {
        showAlert('❌ 请先选择地域', 'error');
        return;
    }
    
    if (selectedInstances.length === 0) {
        showAlert('❌ 请先选择要执行命令的实例', 'error');
        return;
    }
    
    if (!commandContent) {
        showAlert('❌ 请输入要执行的命令', 'error');
        return;
    }
    
    showLoading(true);
    try {
        const result = await apiRequest('/api/instances/run-command', {
            method: 'POST',
            body: JSON.stringify({
                regionId: regionId,
                instanceIds: selectedInstances,
                commandContent: commandContent,
                commandType: 'RunShellScript'
            })
        });
        
        if (result.success) {
            showAlert('✅ 命令执行成功，正在获取结果...', 'success');
            
            // 添加到命令历史
            addToCommandHistory(commandContent, selectedInstances.length);
            
            // 等待一段时间后获取结果
            setTimeout(() => {
                getCommandResult(regionId, result.data.invokeId, commandContent);
            }, 3000);
        } else {
            showAlert('❌ 命令执行失败: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('❌ 命令执行请求失败: ' + error.message, 'error');
    }
    showLoading(false);
}

// 获取命令执行结果
async function getCommandResult(regionId, invokeId, commandContent) {
    try {
        const result = await apiRequest(`/api/instances/get-command-result?regionId=${encodeURIComponent(regionId)}&invokeId=${encodeURIComponent(invokeId)}`, {
            method: 'GET'
        });

        if (result.success) {
            displayCommandResults(result.data.results, commandContent);
        } else {
            showAlert('❌ 获取命令结果失败: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('❌ 获取命令结果请求失败: ' + error.message, 'error');
    }
}

// 显示命令执行结果
function displayCommandResults(results, commandContent) {
    const container = document.getElementById('resultsContainer');
    const resultsSection = document.getElementById('commandResults');

    if (!container || !resultsSection) return;

    let html = `
        <div class="result-command">
            <strong>执行的命令:</strong> <code>${commandContent}</code>
        </div>
    `;

    results.forEach(result => {
        const statusClass = getResultStatusClass(result.invocationStatus);
        const instanceName = getInstanceName(result.instanceId);

        html += `
            <div class="result-item">
                <div class="result-header">
                    <div class="result-instance">${instanceName} (${result.instanceId})</div>
                    <div class="result-status ${statusClass}">${getResultStatusText(result.invocationStatus)}</div>
                </div>
        `;

        if (result.invocationStatus === 'Success' && result.output) {
            // 解码Base64输出
            const decodedOutput = decodeBase64(result.output);
            html += `<div class="result-output">${decodedOutput}</div>`;
        } else if (result.invocationStatus === 'Failed') {
            html += `
                <div class="result-error">
                    <strong>错误代码:</strong> ${result.errorCode}<br>
                    <strong>错误信息:</strong> ${result.errorInfo}
                </div>
            `;
        } else if (result.invocationStatus === 'Running') {
            html += `<div class="result-output">命令正在执行中...</div>`;
        }

        if (result.exitCode !== undefined) {
            html += `<div style="margin-top: 10px; font-size: 0.9em; color: #6c757d;">退出代码: ${result.exitCode}</div>`;
        }

        html += `</div>`;
    });

    container.innerHTML = html;
    resultsSection.style.display = 'block';

    // 滚动到结果区域
    resultsSection.scrollIntoView({ behavior: 'smooth' });
}

// 获取结果状态样式类
function getResultStatusClass(status) {
    const statusMap = {
        'Success': 'status-success',
        'Failed': 'status-failed',
        'Running': 'status-running',
        'Stopped': 'status-failed'
    };
    return statusMap[status] || 'status-failed';
}

// 获取结果状态文本
function getResultStatusText(status) {
    const statusMap = {
        'Success': '成功',
        'Failed': '失败',
        'Running': '执行中',
        'Stopped': '已停止'
    };
    return statusMap[status] || status;
}

// 根据实例ID获取实例名称
function getInstanceName(instanceId) {
    const instance = instancesData.find(inst => inst.instanceId === instanceId);
    return instance ? (instance.instanceName || '未命名实例') : '未知实例';
}

// Base64解码
function decodeBase64(str) {
    try {
        return atob(str);
    } catch (e) {
        return str; // 如果解码失败，返回原字符串
    }
}

// 添加到命令历史
function addToCommandHistory(command, instanceCount) {
    const historyItem = {
        command: command,
        instanceCount: instanceCount,
        timestamp: new Date().toLocaleString('zh-CN')
    };

    commandHistory.unshift(historyItem);

    // 限制历史记录数量
    if (commandHistory.length > 20) {
        commandHistory = commandHistory.slice(0, 20);
    }

    saveCommandHistory();
    renderCommandHistory();
}

// 渲染命令历史
function renderCommandHistory() {
    const container = document.getElementById('historyContainer');
    const historySection = document.getElementById('commandHistory');

    if (!container || !historySection) return;

    if (commandHistory.length === 0) {
        historySection.style.display = 'none';
        return;
    }

    let html = '';
    commandHistory.forEach((item, index) => {
        html += `
            <div class="history-item" onclick="setCommand('${item.command.replace(/'/g, "\\'")}')">
                <div class="history-command">${item.command}</div>
                <div class="history-time">${item.timestamp} - 执行于 ${item.instanceCount} 个实例</div>
            </div>
        `;
    });

    container.innerHTML = html;
    historySection.style.display = 'block';
}

// 保存命令历史到本地存储
function saveCommandHistory() {
    try {
        localStorage.setItem('commandHistory', JSON.stringify(commandHistory));
    } catch (e) {
        console.warn('无法保存命令历史到本地存储');
    }
}

// 从本地存储加载命令历史
function loadCommandHistory() {
    try {
        const saved = localStorage.getItem('commandHistory');
        if (saved) {
            commandHistory = JSON.parse(saved);
            renderCommandHistory();
        }
    } catch (e) {
        console.warn('无法从本地存储加载命令历史');
        commandHistory = [];
    }
}

// 处理卡片点击事件
function handleCardClick(event, instanceId) {
    // 阻止事件冒泡到父元素
    event.stopPropagation();

    // 如果点击的是复选框，不处理（复选框有自己的事件处理）
    if (event.target.type === 'checkbox') {
        return;
    }

    // 切换实例选择状态
    toggleInstanceSelection(instanceId);

    // 添加点击动画效果
    const card = event.currentTarget;
    card.style.transform = 'scale(0.98)';
    setTimeout(() => {
        card.style.transform = '';
    }, 150);
}

// 切换视图模式
function switchView(viewMode) {
    if (currentViewMode === viewMode) return;

    currentViewMode = viewMode;

    // 更新按钮状态
    document.getElementById('detailedViewBtn').classList.toggle('active', viewMode === 'detailed');
    document.getElementById('compactViewBtn').classList.toggle('active', viewMode === 'compact');

    // 重新渲染实例列表
    renderInstancesList();

    // 保存用户偏好
    saveViewPreference();

    // 显示切换提示
    const modeText = viewMode === 'detailed' ? '详细视图' : '紧凑视图';
    showAlert(`✅ 已切换到${modeText}`, 'success');
}

// 保存视图偏好到本地存储
function saveViewPreference() {
    try {
        localStorage.setItem('instanceViewMode', currentViewMode);
    } catch (e) {
        console.warn('无法保存视图偏好到本地存储');
    }
}

// 从本地存储加载视图偏好
function loadViewPreference() {
    try {
        const saved = localStorage.getItem('instanceViewMode');
        if (saved && (saved === 'detailed' || saved === 'compact')) {
            currentViewMode = saved;

            // 更新按钮状态
            document.getElementById('detailedViewBtn').classList.toggle('active', currentViewMode === 'detailed');
            document.getElementById('compactViewBtn').classList.toggle('active', currentViewMode === 'compact');
        }
    } catch (e) {
        console.warn('无法从本地存储加载视图偏好');
        currentViewMode = 'detailed';
    }
}

// 实例名称模式匹配相关变量
let instanceMatchedByName = [];
let instanceNameMatchTimeout = null;

// 处理实例名称模式输入
function handleInstanceNamePatternInput() {
    const input = document.getElementById('instanceNamePattern');
    const pattern = input.value.trim();

    // 清除之前的定时器
    if (instanceNameMatchTimeout) {
        clearTimeout(instanceNameMatchTimeout);
    }

    // 如果输入为空，隐藏预览
    if (!pattern) {
        hideInstanceNameMatchPreview();
        return;
    }

    // 延迟执行匹配，避免频繁计算
    instanceNameMatchTimeout = setTimeout(() => {
        performInstanceNameMatching(pattern);
    }, 300);
}

// 处理实例名称模式输入失焦
function handleInstanceNamePatternBlur() {
    // 延迟隐藏预览，给用户时间点击预览中的内容
    setTimeout(() => {
        const preview = document.getElementById('instanceNameMatchPreview');
        const confirm = document.getElementById('instanceNameMatchConfirm');
        if (preview && !preview.matches(':hover') && confirm && !confirm.matches(':hover')) {
            // 如果鼠标不在预览区域内，则隐藏
            // hideInstanceNameMatchPreview();
        }
    }, 200);
}

// 设置实例名称模式
function setInstanceNamePattern(pattern) {
    const input = document.getElementById('instanceNamePattern');
    input.value = pattern;
    input.focus();
    handleInstanceNamePatternInput();
}

// 执行实例名称匹配
function performInstanceNameMatching(pattern) {
    if (!instancesData || instancesData.length === 0) {
        showInstanceNameMatchError('请先查询实例列表');
        return;
    }

    // 验证通配符模式
    if (!validateWildcardPattern(pattern)) {
        showInstanceNameMatchError('无效的通配符模式');
        return;
    }

    // 执行匹配
    const matches = matchInstancesByNamePattern(pattern);

    if (matches.length === 0) {
        showInstanceNameMatchError('没有匹配到任何实例');
        return;
    }

    // 显示匹配结果
    showInstanceNameMatchPreview(matches, pattern);
    instanceMatchedByName = matches;
}

// 验证通配符模式
function validateWildcardPattern(pattern) {
    // 基本验证：不能只包含通配符
    if (/^[\*\?]+$/.test(pattern)) {
        return false;
    }

    // 检查是否包含无效字符
    if (/[<>:"/\\|]/.test(pattern)) {
        return false;
    }

    return true;
}

// 通过名称模式匹配实例
function matchInstancesByNamePattern(pattern) {
    const regex = createWildcardRegex(pattern);

    return instancesData.filter(instance => {
        const instanceName = instance.instanceName || '未命名实例';
        return regex.test(instanceName);
    });
}

// 创建通配符正则表达式
function createWildcardRegex(pattern) {
    // 转义特殊字符，但保留 * 和 ?
    let regexPattern = pattern
        .replace(/[.+^${}()|[\]\\]/g, '\\$&') // 转义正则特殊字符
        .replace(/\*/g, '.*')                  // * 替换为 .*
        .replace(/\?/g, '.');                  // ? 替换为 .

    return new RegExp('^' + regexPattern + '$', 'i'); // 不区分大小写
}

// 显示实例名称匹配预览
function showInstanceNameMatchPreview(matches, pattern) {
    const preview = document.getElementById('instanceNameMatchPreview');
    const matchCount = document.getElementById('instanceMatchCount');
    const matchedList = document.getElementById('instanceMatchedList');

    // 更新匹配数量
    matchCount.textContent = `${matches.length} 个匹配`;

    // 生成匹配实例列表
    let html = '';
    matches.forEach(instance => {
        const instanceName = instance.instanceName || '未命名实例';
        const highlightedName = highlightMatchedText(instanceName, pattern);
        const statusClass = getStatusClass(instance.status);
        const statusText = getStatusText(instance.status);

        html += `
            <div class="matched-instance-item">
                <div>
                    <div class="matched-instance-name">${highlightedName}</div>
                    <div class="matched-instance-id">${instance.instanceId}</div>
                </div>
                <div>
                    <div class="matched-instance-status ${statusClass}">${statusText}</div>
                </div>
            </div>
        `;
    });

    matchedList.innerHTML = html;
    preview.style.display = 'block';

    // 显示确认区域
    showInstanceNameMatchConfirm(matches);
}

// 高亮匹配的文本
function highlightMatchedText(text, pattern) {
    const regex = createWildcardRegex(pattern);

    // 简单的高亮实现，实际匹配整个文本
    if (regex.test(text)) {
        return `<span class="highlight-match">${text}</span>`;
    }

    return text;
}

// 显示实例名称匹配确认
function showInstanceNameMatchConfirm(matches) {
    const confirm = document.getElementById('instanceNameMatchConfirm');
    const confirmContent = document.getElementById('instanceConfirmContent');

    let html = `
        <div>即将选择 <strong>${matches.length}</strong> 个实例。</div>
    `;

    confirmContent.innerHTML = html;
    confirm.style.display = 'block';
}

// 隐藏实例名称匹配确认
function hideInstanceNameMatchConfirm() {
    const confirm = document.getElementById('instanceNameMatchConfirm');
    confirm.style.display = 'none';
}

// 显示实例名称匹配错误
function showInstanceNameMatchError(message) {
    const preview = document.getElementById('instanceNameMatchPreview');
    const matchCount = document.getElementById('instanceMatchCount');
    const matchedList = document.getElementById('instanceMatchedList');

    matchCount.textContent = '0 个匹配';
    matchedList.innerHTML = `
        <div style="text-align: center; padding: 20px; color: #6c757d;">
            <div style="font-size: 2em; margin-bottom: 10px;">🔍</div>
            <div>${message}</div>
        </div>
    `;

    preview.style.display = 'block';
    hideInstanceNameMatchConfirm();
}

// 隐藏实例名称匹配预览
function hideInstanceNameMatchPreview() {
    const preview = document.getElementById('instanceNameMatchPreview');
    preview.style.display = 'none';
    hideInstanceNameMatchConfirm();
    instanceMatchedByName = [];
}

// 取消实例名称匹配
function cancelInstanceNameMatch() {
    hideInstanceNameMatchPreview();
    document.getElementById('instanceNamePattern').value = '';
}

// 确认实例名称匹配
function confirmInstanceNameMatch() {
    if (instanceMatchedByName.length === 0) {
        return;
    }

    // 选中匹配的实例
    instanceMatchedByName.forEach(instance => {
        const instanceId = instance.instanceId;

        // 添加到选中列表
        if (!selectedInstances.includes(instanceId)) {
            selectedInstances.push(instanceId);
        }

        // 更新对应的复选框状态
        const checkbox = document.getElementById(`instance-${instanceId}`);
        if (checkbox) {
            checkbox.checked = true;
        }

        // 更新卡片样式
        const card = document.querySelector(`[data-instance-id="${instanceId}"]`);
        if (card) {
            card.classList.add('selected');
        }
    });

    // 更新选中状态
    updateSelectedCount();
    updateSelectAllCheckbox();

    // 隐藏预览
    hideInstanceNameMatchPreview();

    // 显示成功提示
    showAlert(`✅ 已选择 ${instanceMatchedByName.length} 个匹配的实例`, 'success');
}

// ==================== 创建实例模态框功能 ====================

// 打开创建实例模态框
function openCreateInstanceModal() {
    const modal = document.getElementById('createInstanceModal');
    const regionSelect = document.getElementById('regionId');
    const modalRegionSelect = document.getElementById('modalRegionId');

    // 同步地域选择
    if (regionSelect.value) {
        modalRegionSelect.value = regionSelect.value;
    }

    modal.style.display = 'flex';

    // 添加ESC键关闭功能
    document.addEventListener('keydown', handleModalEscape);
}

// 关闭创建实例模态框
function closeCreateInstanceModal() {
    const modal = document.getElementById('createInstanceModal');
    modal.style.display = 'none';

    // 重置表单
    document.getElementById('createInstanceForm').reset();

    // 隐藏EIP配置
    const eipOptions = document.getElementById('modalEipOptions');
    if (eipOptions) {
        eipOptions.style.display = 'none';
    }
    const enableEIP = document.getElementById('modalEnableEIP');
    if (enableEIP) {
        enableEIP.checked = false;
    }

    // 恢复公网带宽设置
    const internetBandwidth = document.getElementById('modalInternetMaxBandwidthOut');
    if (internetBandwidth) {
        internetBandwidth.style.backgroundColor = '';
        internetBandwidth.title = '';
    }

    // 移除ESC键监听
    document.removeEventListener('keydown', handleModalEscape);
}

// 处理ESC键关闭模态框
function handleModalEscape(event) {
    if (event.key === 'Escape') {
        closeCreateInstanceModal();
    }
}

// 切换EIP配置显示
function toggleModalEIPOptions() {
    const enableEIP = document.getElementById('modalEnableEIP');
    const eipOptions = document.getElementById('modalEipOptions');

    if (enableEIP && eipOptions) {
        if (enableEIP.checked) {
            eipOptions.style.display = 'block';
            // 如果启用EIP，提示用户公网带宽将被忽略
            const internetBandwidth = document.getElementById('modalInternetMaxBandwidthOut');
            if (internetBandwidth) {
                internetBandwidth.style.backgroundColor = '#f0f0f0';
                internetBandwidth.title = '启用EIP时，此设置将被忽略';
            }
        } else {
            eipOptions.style.display = 'none';
            // 恢复公网带宽设置
            const internetBandwidth = document.getElementById('modalInternetMaxBandwidthOut');
            if (internetBandwidth) {
                internetBandwidth.style.backgroundColor = '';
                internetBandwidth.title = '';
            }
        }
    }
}

// 验证模态框配置
async function validateModalConfig() {
    showLoading(true, '正在验证配置...');
    try {
        const data = getModalFormData();
        const result = await apiRequest('/api/validate', {
            method: 'POST',
            body: JSON.stringify(data)
        });

        if (result.success) {
            showAlert('✅ 配置验证通过！', 'success');
        } else {
            showAlert('❌ 配置验证失败: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('❌ 验证请求失败: ' + error.message, 'error');
    }
    showLoading(false);
}

// 获取模态框表单数据
function getModalFormData() {
    const form = document.getElementById('createInstanceForm');
    const formData = new FormData(form);
    const data = {};

    for (let [key, value] of formData.entries()) {
        // 处理数字类型
        if (key === 'internetMaxBandwidthOut' || key === 'systemDiskSize' ||
            key === 'amount' || key === 'minAmount' || key === 'eipBandwidth') {
            data[key] = parseInt(value) || 0;
        }
        // 处理布尔类型
        else if (key === 'dryRun' || key === 'enableEIP') {
            data[key] = true;
        }
        // 处理字符串类型
        else {
            data[key] = value;
        }
    }

    // 处理未选中的复选框
    const checkboxes = ['dryRun', 'enableEIP'];
    checkboxes.forEach(checkbox => {
        if (!formData.has(checkbox)) {
            data[checkbox] = false;
        }
    });

    return data;
}

// 验证密码复杂度
function validatePassword(password) {
    if (!password) {
        return null; // 密码为空时不验证
    }

    if (password.length < 8 || password.length > 30) {
        return '密码长度必须在8-30位之间';
    }

    if (!/[A-Z]/.test(password)) {
        return '密码必须包含至少一个大写字母';
    }

    if (!/[a-z]/.test(password)) {
        return '密码必须包含至少一个小写字母';
    }

    if (!/[0-9]/.test(password)) {
        return '密码必须包含至少一个数字';
    }

    return null;
}

// 验证模态框表单数据
function validateModalCreateForm(data) {
    // 验证必填字段
    const requiredFields = ['regionId', 'imageId', 'instanceType', 'securityGroupId', 'vSwitchId'];
    const error = validateRequired(data, requiredFields);
    if (error) {
        showAlert('❌ ' + error, 'error');
        return false;
    }

    // 验证密码复杂度
    const passwordError = validatePassword(data.password);
    if (passwordError) {
        showAlert('❌ ' + passwordError, 'error');
        return false;
    }

    // 验证数量关系
    if (data.minAmount > data.amount) {
        showAlert('❌ 最少创建数量不能超过创建数量', 'error');
        return false;
    }

    // 验证系统盘大小
    if (data.systemDiskSize < 20 || data.systemDiskSize > 500) {
        showAlert('❌ 系统盘大小必须在20-500GB之间', 'error');
        return false;
    }

    // 验证创建数量
    if (data.amount < 1 || data.amount > 100) {
        showAlert('❌ 创建数量必须在1-100之间', 'error');
        return false;
    }

    return true;
}

// 从模态框创建实例
async function createInstanceFromModal() {
    const createBtn = document.querySelector('.create-instance-modal .btn-success');
    const spinner = createBtn.querySelector('.loading-spinner');
    const originalText = createBtn.textContent;

    const data = getModalFormData();

    // 验证表单
    if (!validateModalCreateForm(data)) {
        return;
    }

    // 处理批量创建的实例名称
    if (data.instanceNamePrefix && data.amount > 1) {
        // 批量创建时，后端会自动处理名称前缀
        data.instanceName = data.instanceNamePrefix;
    } else if (data.instanceNamePrefix) {
        // 单个创建时，直接使用前缀作为名称
        data.instanceName = data.instanceNamePrefix;
    }

    // 确认创建
    if (!data.dryRun) {
        const instanceText = data.amount > 1 ? `${data.amount} 台ECS实例` : '1 台ECS实例';
        const nameText = data.instanceNamePrefix ?
            (data.amount > 1 ? `名称前缀: ${data.instanceNamePrefix}` : `实例名称: ${data.instanceNamePrefix}`) :
            '使用默认名称';

        const details = `地域: ${data.regionId}\n规格: ${data.instanceType}\n镜像: ${data.imageId}\n${nameText}\n带宽: ${data.internetMaxBandwidthOut}Mbps`;

        const confirmed = await confirmAction({
            title: '确认创建ECS实例',
            message: `您即将创建 ${instanceText}，此操作将产生费用。`,
            details: details,
            confirmText: '确认创建',
            cancelText: '取消',
            type: 'primary'
        });

        if (!confirmed) {
            return;
        }
    }

    // 设置按钮加载状态
    createBtn.disabled = true;
    spinner.style.display = 'inline-block';
    createBtn.innerHTML = '<span class="loading-spinner"></span> 创建中...';

    try {
        const result = await apiRequest('/api/create', {
            method: 'POST',
            body: JSON.stringify(data)
        });

        if (result.success) {
            if (data.dryRun) {
                showAlert('✅ 预检验证通过！您可以取消预检模式来实际创建 ' + data.amount + ' 台实例', 'success');
            } else {
                const instanceText = data.amount > 1 ?
                    `${data.amount} 台ECS实例（${data.instanceNamePrefix}-001 到 ${data.instanceNamePrefix}-${String(data.amount).padStart(3, '0')}）` :
                    `1 台ECS实例（${data.instanceNamePrefix}）`;
                showAlert('✅ 成功创建 ' + instanceText + '！', 'success');

                // 关闭模态框
                closeCreateInstanceModal();

                // 刷新实例列表
                setTimeout(() => {
                    loadInstances();
                }, 2000);
            }
        } else {
            showAlert('❌ 创建失败: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('❌ 创建请求失败: ' + error.message, 'error');
    } finally {
        // 恢复按钮状态
        createBtn.disabled = false;
        spinner.style.display = 'none';
        createBtn.textContent = originalText;
    }
}

// 获取字段标签
function getFieldLabel(fieldName) {
    const labels = {
        'regionId': '地域ID',
        'instanceType': '实例规格',
        'imageId': '镜像ID',
        'instanceName': '实例名称',
        'securityGroupId': '安全组ID'
    };
    return labels[fieldName] || fieldName;
}

// 模态框数量验证
function setupModalAmountValidation() {
    const amountInput = document.getElementById('modalAmount');
    const minAmountInput = document.getElementById('modalMinAmount');

    if (amountInput && minAmountInput) {
        amountInput.addEventListener('change', function() {
            const amount = parseInt(this.value);
            const minAmount = parseInt(minAmountInput.value);

            if (minAmount > amount) {
                minAmountInput.value = amount;
            }
            minAmountInput.max = amount;
        });

        minAmountInput.addEventListener('change', function() {
            const minAmount = parseInt(this.value);
            const amount = parseInt(amountInput.value);

            if (minAmount > amount) {
                this.value = amount;
                showAlert('⚠️ 最少创建数量不能超过创建数量', 'warning');
            }
        });
    }
}

// 初始化模态框
function initializeCreateInstanceModal() {
    // 设置数量验证
    setupModalAmountValidation();

    // 设置默认值
    const dryRunCheckbox = document.getElementById('modalDryRun');
    if (dryRunCheckbox) {
        dryRunCheckbox.checked = false;
    }
}

// ==================== 释放实例功能 ====================

// 通配符匹配相关变量
let matchedInstancesByName = [];
let nameMatchTimeout = null;

// 处理实例名称输入
function handleInstanceNameInput() {
    const input = document.getElementById('instanceName');
    const pattern = input.value.trim();

    // 清除之前的定时器
    if (nameMatchTimeout) {
        clearTimeout(nameMatchTimeout);
    }

    if (!pattern) {
        hideNameMatchPreview();
        return;
    }

    // 延迟执行匹配，避免频繁计算
    nameMatchTimeout = setTimeout(() => {
        if (instancesData && instancesData.length > 0) {
            performNameMatching(pattern);
        } else {
            showNameMatchError('请先加载实例列表');
        }
    }, 200);
}

// 设置实例名称模式
function setInstanceNamePattern(pattern) {
    const input = document.getElementById('instanceName');
    input.value = pattern;
    input.focus();
    handleInstanceNameInput();
}

// 执行名称匹配
function performNameMatching(pattern) {
    if (!instancesData || instancesData.length === 0) {
        showNameMatchError('没有可匹配的实例');
        return;
    }

    try {
        // 将通配符模式转换为正则表达式
        const regexPattern = pattern
            .replace(/[.*+?^${}()|[\]\\]/g, '\\$&') // 转义特殊字符
            .replace(/\\\*/g, '.*') // * 匹配任意字符序列
            .replace(/\\\?/g, '.'); // ? 匹配单个字符

        const regex = new RegExp(`^${regexPattern}$`, 'i'); // 不区分大小写

        // 匹配实例
        matchedInstancesByName = instancesData.filter(instance => {
            const instanceName = instance.instanceName || instance.instanceId;
            return regex.test(instanceName);
        });

        if (matchedInstancesByName.length === 0) {
            showNameMatchError(`没有找到匹配模式 "${pattern}" 的实例`);
        } else {
            showNameMatchPreview(pattern, matchedInstancesByName);
        }
    } catch (error) {
        showNameMatchError('无效的匹配模式');
    }
}

// 显示名称匹配预览
function showNameMatchPreview(pattern, matches) {
    const preview = document.getElementById('nameMatchPreview');
    const matchCount = document.getElementById('matchCount');
    const matchedInstances = document.getElementById('matchedInstances');

    matchCount.textContent = `${matches.length} 个匹配`;

    let html = '';
    matches.forEach(instance => {
        const instanceName = instance.instanceName || instance.instanceId;
        const statusClass = getStatusClass(instance.status);
        html += `
            <div class="matched-instance">
                <span class="instance-name">${instanceName}</span>
                <span class="instance-status ${statusClass}">${getStatusText(instance.status)}</span>
                <span class="instance-id">(${instance.instanceId})</span>
            </div>
        `;
    });

    matchedInstances.innerHTML = html;
    preview.style.display = 'block';

    // 显示确认区域
    showNameMatchConfirm(pattern, matches.length);
}

// 显示名称匹配确认
function showNameMatchConfirm(pattern, count) {
    const confirm = document.getElementById('nameMatchConfirm');
    const content = document.getElementById('confirmContent');

    content.innerHTML = `
        <div>模式 <code>${pattern}</code> 匹配到 <strong>${count}</strong> 个实例</div>
        <div style="margin-top: 8px; color: #6c757d;">点击"确认选择"将自动选中这些实例</div>
    `;

    confirm.style.display = 'block';
}

// 隐藏名称匹配确认
function hideNameMatchConfirm() {
    const confirm = document.getElementById('nameMatchConfirm');
    confirm.style.display = 'none';
}

// 显示名称匹配错误
function showNameMatchError(message) {
    const preview = document.getElementById('nameMatchPreview');
    const matchCount = document.getElementById('matchCount');
    const matchedInstances = document.getElementById('matchedInstances');

    matchCount.textContent = '0 个匹配';
    matchedInstances.innerHTML = `
        <div style="text-align: center; padding: 20px; color: #6c757d;">
            <div style="font-size: 2em; margin-bottom: 10px;">🔍</div>
            <div>${message}</div>
        </div>
    `;

    preview.style.display = 'block';
    hideNameMatchConfirm();
}

// 隐藏名称匹配预览
function hideNameMatchPreview() {
    const preview = document.getElementById('nameMatchPreview');
    preview.style.display = 'none';
    hideNameMatchConfirm();
    matchedInstancesByName = [];
}

// 取消名称匹配
function cancelNameMatch() {
    hideNameMatchPreview();
    document.getElementById('instanceName').value = '';
}

// 确认名称匹配
function confirmNameMatch() {
    if (matchedInstancesByName.length === 0) {
        return;
    }

    // 选中匹配的实例
    const checkboxes = document.querySelectorAll('input[name="selectedInstances"]');
    checkboxes.forEach(checkbox => {
        const instanceId = checkbox.value;
        const isMatched = matchedInstancesByName.some(instance => instance.instanceId === instanceId);
        checkbox.checked = isMatched;
    });

    // 更新选中状态
    updateSelectedCount();
    updateSelectedSummary();

    // 隐藏预览
    hideNameMatchPreview();

    // 显示成功提示
    showAlert(`✅ 已选择 ${matchedInstancesByName.length} 个匹配的实例`, 'success');
}

// 获取选中的实例ID列表
function getSelectedInstances() {
    return selectedInstances;
}

// 释放选中的实例
async function releaseSelectedInstances() {
    const selectedInstanceIds = getSelectedInstances();

    if (selectedInstanceIds.length === 0) {
        showAlert('❌ 请先选择要释放的实例', 'error');
        return;
    }

    const forceRelease = document.getElementById('forceRelease').checked;
    const releaseEip = document.getElementById('releaseEip').checked;

    // 构建释放数据
    const data = {
        regionId: document.getElementById('regionId').value,
        instanceIds: selectedInstanceIds.join(','),
        force: forceRelease,
        releaseEip: releaseEip
    };

    // 确认释放操作
    const instanceText = selectedInstanceIds.length === 1 ? '1 台实例' : `${selectedInstanceIds.length} 台实例`;
    const forceText = forceRelease ? '（强制释放）' : '';
    const eipText = releaseEip ? '，同时释放绑定的弹性IP' : '';

    const confirmed = await confirmAction({
        title: '确认释放实例',
        message: `您即将释放 ${instanceText}${forceText}${eipText}。此操作不可逆，请确认。`,
        details: `实例ID: ${selectedInstanceIds.join(', ')}`,
        confirmText: '确认释放',
        cancelText: '取消',
        type: 'danger'
    });

    if (!confirmed) {
        return;
    }

    const releaseBtn = document.getElementById('releaseBtn');
    const originalText = releaseBtn.textContent;

    try {
        releaseBtn.disabled = true;
        releaseBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 释放中...';

        const result = await apiRequest('/api/release', {
            method: 'POST',
            body: JSON.stringify(data)
        });

        if (result.success) {
            showAlert('✅ 实例释放成功！', 'success');

            // 刷新实例列表
            setTimeout(() => {
                loadInstances();
            }, 2000);
        } else {
            showAlert('❌ 释放失败: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('❌ 释放请求失败: ' + error.message, 'error');
    } finally {
        releaseBtn.disabled = false;
        releaseBtn.textContent = originalText;
    }
}

// 更新选中实例摘要
function updateSelectedSummary() {
    const selectedInstanceIds = getSelectedInstances();
    const summaryDiv = document.getElementById('selectedSummary');
    const releaseBtn = document.getElementById('releaseBtn');

    if (selectedInstanceIds.length === 0) {
        summaryDiv.style.display = 'none';
        releaseBtn.disabled = true;
        return;
    }

    releaseBtn.disabled = false;

    // 获取选中实例的详细信息
    const selectedInstancesData = instancesData.filter(instance =>
        selectedInstanceIds.includes(instance.instanceId)
    );

    let html = `
        <div class="summary-header">
            <strong>已选择 ${selectedInstanceIds.length} 台实例：</strong>
        </div>
        <div class="summary-list">
    `;

    selectedInstancesData.forEach(instance => {
        const statusClass = getStatusClass(instance.status);
        const protectionIcon = instance.deletionProtection ? '🛡️' : '';
        html += `
            <div class="summary-item">
                <span class="instance-name">${instance.instanceName || '未命名'}</span>
                <span class="instance-status ${statusClass}">${getStatusText(instance.status)}</span>
                ${protectionIcon}
                <span class="instance-id">(${instance.instanceId})</span>
            </div>
        `;
    });

    html += '</div>';

    // 检查是否有删除保护的实例
    const protectedInstances = selectedInstancesData.filter(instance => instance.deletionProtection);
    if (protectedInstances.length > 0) {
        html += `
            <div class="protection-warning">
                ⚠️ 其中 ${protectedInstances.length} 台实例启用了删除保护，需要勾选"强制释放"才能释放
            </div>
        `;
    }

    summaryDiv.innerHTML = html;
    summaryDiv.style.display = 'block';
}

// 处理实例名称输入失焦
function handleInstanceNameBlur() {
    // 延迟隐藏预览，给用户时间点击确认按钮
    setTimeout(() => {
        const input = document.getElementById('instanceName');
        if (!input.value.trim()) {
            hideNameMatchPreview();
        }
    }, 200);
}



// ==================== HTTP文件下载功能 ====================

// 启动HTTP服务器
async function startHTTPServer() {
    const regionId = document.getElementById('regionId').value;
    const port = parseInt(document.getElementById('httpServerPort').value) || 8000;
    const directory = document.getElementById('httpServerDirectory').value.trim() || '/tmp';

    if (!regionId) {
        showAlert('❌ 请先选择地域', 'error');
        return;
    }

    if (selectedInstances.length === 0) {
        showAlert('❌ 请先选择要启动HTTP服务器的实例', 'error');
        return;
    }

    if (selectedInstances.length > 1) {
        showAlert('❌ HTTP服务器模式每次只能选择一个实例', 'error');
        return;
    }

    const instanceId = selectedInstances[0];

    // 确认对话框
    const confirmMessage = `确定要启动HTTP服务器吗？\n\n` +
        `目标实例: ${instanceId}\n` +
        `端口: ${port}\n` +
        `根目录: ${directory}\n` +
        `\n⚠️ 服务器启动后可通过HTTP协议访问文件`;

    if (!confirm(confirmMessage)) {
        return;
    }

    showLoading(true, '正在启动HTTP服务器...');

    try {
        const response = await fetch('/api/instances/start-http-server', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                regionId: regionId,
                instanceId: instanceId,
                port: port,
                directory: directory
            })
        });

        const result = await response.json();

        if (result.success) {
            showAlert('✅ HTTP服务器启动命令已发送', 'success');

            // 显示服务器状态
            const statusDiv = document.getElementById('httpServerStatus');
            const infoDiv = document.getElementById('httpServerInfo');

            infoDiv.innerHTML = `
                <p><strong>实例:</strong> ${instanceId}</p>
                <p><strong>端口:</strong> ${result.data.port}</p>
                <p><strong>根目录:</strong> ${result.data.directory}</p>
                <p><strong>执行ID:</strong> ${result.data.invokeId}</p>
                <p class="status-pending">⏳ 正在启动服务器...</p>
            `;
            statusDiv.style.display = 'block';

            // 等待服务器启动完成
            setTimeout(() => {
                checkHTTPServerStatus(regionId, result.data.invokeId);
            }, 5000);

        } else {
            showAlert('❌ 启动HTTP服务器失败: ' + result.error, 'error');
        }

    } catch (error) {
        console.error('启动HTTP服务器失败:', error);
        showAlert('❌ 启动HTTP服务器失败: ' + error.message, 'error');
    } finally {
        showLoading(false);
    }
}

// 停止HTTP服务器
async function stopHTTPServer() {
    const regionId = document.getElementById('regionId').value;
    const port = parseInt(document.getElementById('httpServerPort').value) || 8000;

    if (!regionId) {
        showAlert('❌ 请先选择地域', 'error');
        return;
    }

    if (selectedInstances.length === 0) {
        showAlert('❌ 请先选择实例', 'error');
        return;
    }

    const instanceId = selectedInstances[0];

    if (!confirm('确定要停止HTTP服务器吗？')) {
        return;
    }

    showLoading(true, '正在停止HTTP服务器...');

    try {
        const response = await fetch('/api/instances/stop-http-server', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                regionId: regionId,
                instanceId: instanceId,
                port: port
            })
        });

        const result = await response.json();

        if (result.success) {
            showAlert('✅ HTTP服务器停止命令已发送', 'success');

            // 隐藏服务器状态
            document.getElementById('httpServerStatus').style.display = 'none';

        } else {
            showAlert('❌ 停止HTTP服务器失败: ' + result.error, 'error');
        }

    } catch (error) {
        console.error('停止HTTP服务器失败:', error);
        showAlert('❌ 停止HTTP服务器失败: ' + error.message, 'error');
    } finally {
        showLoading(false);
    }
}

// 检查HTTP服务器状态
async function checkHTTPServerStatus(regionId, invokeId) {
    try {
        const response = await fetch(`/api/instances/get-command-result?regionId=${regionId}&invokeId=${invokeId}`);
        const result = await response.json();

        if (result.success && result.data.results && result.data.results.length > 0) {
            const commandResult = result.data.results[0];

            if (commandResult.status === 'Success') {
                const output = commandResult.output;

                // 解析HTTP服务器信息
                if (output.includes('HTTP_SERVER_STARTED')) {
                    const portMatch = output.match(/PORT:(\d+)/);
                    const directoryMatch = output.match(/DIRECTORY:(.+)/);

                    if (portMatch && directoryMatch) {
                        const actualPort = portMatch[1];
                        const actualDirectory = directoryMatch[1];

                        const infoDiv = document.getElementById('httpServerInfo');
                        infoDiv.innerHTML = `
                            <p><strong>实例:</strong> ${selectedInstances[0]}</p>
                            <p><strong>端口:</strong> ${actualPort}</p>
                            <p><strong>根目录:</strong> ${actualDirectory}</p>
                            <p class="status-success">✅ HTTP服务器运行中</p>
                        `;

                        showAlert('✅ HTTP服务器启动成功！端口: ' + actualPort, 'success');
                    }
                } else if (output.includes('ERROR') || output.includes('❌')) {
                    const infoDiv = document.getElementById('httpServerInfo');
                    infoDiv.innerHTML = `
                        <p class="status-error">❌ HTTP服务器启动失败</p>
                        <pre>${output}</pre>
                    `;
                    showAlert('❌ HTTP服务器启动失败', 'error');
                }
            } else if (commandResult.status === 'Failed') {
                showAlert('❌ HTTP服务器启动失败', 'error');
            } else {
                // 命令还在执行中，继续等待
                setTimeout(() => {
                    checkHTTPServerStatus(regionId, invokeId);
                }, 3000);
            }
        }
    } catch (error) {
        console.error('检查HTTP服务器状态失败:', error);
    }
}



// 获取实例信息
function getInstanceInfo(instanceId) {
    // 从实例数据中查找实例信息
    const instance = instancesData.find(inst => inst.instanceId === instanceId);
    if (!instance) {
        return null;
    }

    return {
        instanceId: instanceId,
        publicIp: instance.effectivePublicIp || '',
        privateIp: instance.privateIpAddress || '',
        hasPublicIp: instance.hasPublicIp || false,
        networkType: instance.networkType || '',
        eipAddress: instance.eipAddress || '',
        publicIpAddress: instance.publicIpAddress || '',
        instance: instance // 返回完整的实例对象
    };
}






















// ==================== 密码重置功能 ====================

// 切换密码可见性
function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const button = input.parentElement.querySelector('.password-toggle');

    if (input.type === 'password') {
        input.type = 'text';
        button.textContent = '🙈';
    } else {
        input.type = 'password';
        button.textContent = '👁️';
    }
}

// 切换创建用户选项
function toggleCreateUser() {
    const createUserCheckbox = document.getElementById('createNewUser');
    const usernameInput = document.getElementById('passwordUsername');

    if (createUserCheckbox.checked) {
        usernameInput.placeholder = '输入新用户名';
        if (usernameInput.value === 'root') {
            usernameInput.value = '';
        }
    } else {
        usernameInput.placeholder = '默认为 root';
        if (usernameInput.value === '') {
            usernameInput.value = 'root';
        }
    }
}

// 验证密码复杂度
function validatePassword(password) {
    if (password.length < 8 || password.length > 30) {
        return '密码长度必须在8-30位之间';
    }

    let hasUpper = /[A-Z]/.test(password);
    let hasLower = /[a-z]/.test(password);
    let hasDigit = /[0-9]/.test(password);
    let hasSpecial = /[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password);

    let count = 0;
    if (hasUpper) count++;
    if (hasLower) count++;
    if (hasDigit) count++;
    if (hasSpecial) count++;

    if (count < 3) {
        return '密码必须包含大写字母、小写字母、数字、特殊字符中的至少3种';
    }

    return null;
}

// 为选中的实例重置密码
async function setPasswordForSelected() {
    const selectedInstanceIds = getSelectedInstances();

    if (selectedInstanceIds.length === 0) {
        showAlert('❌ 请先选择要重置密码的实例', 'error');
        return;
    }

    const username = document.getElementById('passwordUsername').value.trim() || 'root';
    const password = document.getElementById('passwordInput').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const createUser = document.getElementById('createNewUser').checked;

    // 验证输入
    if (!password) {
        showAlert('❌ 请输入密码', 'error');
        return;
    }

    if (password !== confirmPassword) {
        showAlert('❌ 两次输入的密码不一致', 'error');
        return;
    }

    // 验证密码复杂度
    const passwordError = validatePassword(password);
    if (passwordError) {
        showAlert('❌ ' + passwordError, 'error');
        return;
    }

    // 确认操作
    const instanceText = selectedInstanceIds.length > 1 ?
        `${selectedInstanceIds.length} 个实例` : '1 个实例';

    const confirmed = await confirmAction({
        title: '确认重置密码',
        message: `您即将为 ${instanceText} 重置密码`,
        details: `用户名: ${username}\n${createUser ? '如果用户不存在将自动创建' : ''}`,
        confirmText: '确认重置',
        cancelText: '取消',
        type: 'warning'
    });

    if (!confirmed) {
        return;
    }

    const setPasswordBtn = document.getElementById('setPasswordBtn');
    const originalText = setPasswordBtn.textContent;

    try {
        setPasswordBtn.disabled = true;
        setPasswordBtn.innerHTML = '<span class="loading-spinner"></span> 重置中...';

        const regionId = document.getElementById('regionId').value;
        if (!regionId) {
            showAlert('❌ 请先选择地域', 'error');
            return;
        }

        const result = await apiRequest('/api/instances/reset-password', {
            method: 'POST',
            body: JSON.stringify({
                regionId: regionId,
                instanceIds: selectedInstanceIds,
                password: password,
                username: username,
                createUser: createUser
            })
        });

        if (result.success) {
            showAlert(`✅ 密码重置命令已发送到 ${result.data.instanceCount} 个实例`, 'success');

            // 显示执行ID，用户可以查看执行结果
            showAlert(`📋 执行ID: ${result.data.invokeId}`, 'info');

            // 清空密码表单
            clearPasswordForm();
        } else {
            showAlert('❌ 重置密码失败: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('❌ 重置密码失败: ' + error.message, 'error');
    } finally {
        setPasswordBtn.disabled = false;
        setPasswordBtn.textContent = originalText;
    }
}

// 清空密码表单
function clearPasswordForm() {
    document.getElementById('passwordUsername').value = 'root';
    document.getElementById('passwordInput').value = '';
    document.getElementById('confirmPassword').value = '';
    document.getElementById('createNewUser').checked = false;
}

// 更新密码重置按钮状态
function updatePasswordButtonState() {
    const setPasswordBtn = document.getElementById('setPasswordBtn');
    const selectedInstanceIds = getSelectedInstances();

    if (setPasswordBtn) {
        setPasswordBtn.disabled = selectedInstanceIds.length === 0;
    }
}

// 更新下载按钮状态
function updateDownloadButtonState() {
    const batchDownload = document.getElementById('batchDownload').checked;
    const downloadBtn = document.getElementById('downloadBtn');
    const selectedInstanceIds = getSelectedInstances();

    if (batchDownload && downloadBtn) {
        downloadBtn.disabled = selectedInstanceIds.length === 0;
    }
}









// 切换密码显示/隐藏
function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const button = input.nextElementSibling;

    if (input.type === 'password') {
        input.type = 'text';
        button.textContent = '🙈';
    } else {
        input.type = 'password';
        button.textContent = '👁️';
    }
}

// 切换操作部分的折叠/展开
function toggleOperationSection(titleElement) {
    const content = titleElement.nextElementSibling;
    const icon = titleElement.querySelector('.collapse-icon');

    if (content.style.display === 'none') {
        content.style.display = 'block';
        icon.textContent = '▼';
    } else {
        content.style.display = 'none';
        icon.textContent = '▶';
    }
}

// ==================== 脚本文件上传功能 ====================

// 全局变量存储上传的文件信息
let uploadedScript = null;

// 支持的脚本文件类型
const SUPPORTED_SCRIPT_TYPES = {
    '.sh': { icon: '🐚', executor: 'bash', name: 'Shell脚本' },
    '.py': { icon: '🐍', executor: 'python3', name: 'Python脚本' },
    '.ps1': { icon: '💙', executor: 'powershell', name: 'PowerShell脚本' },
    '.bat': { icon: '⚫', executor: 'cmd', name: '批处理脚本' },
    '.js': { icon: '🟨', executor: 'node', name: 'JavaScript脚本' },
    '.pl': { icon: '🐪', executor: 'perl', name: 'Perl脚本' },
    '.txt': { icon: '📄', executor: 'cat', name: '文本文件' }
};

// 最大文件大小 (10MB)
const MAX_FILE_SIZE = 10 * 1024 * 1024;

// 初始化拖拽上传功能
function initializeScriptUpload() {
    const dropZone = document.getElementById('uploadDropZone');

    if (!dropZone) return;

    // 拖拽事件处理
    dropZone.addEventListener('dragover', handleDragOver);
    dropZone.addEventListener('dragleave', handleDragLeave);
    dropZone.addEventListener('drop', handleDrop);
    dropZone.addEventListener('click', () => {
        document.getElementById('scriptFileInput').click();
    });
}

// 处理拖拽悬停
function handleDragOver(e) {
    e.preventDefault();
    e.stopPropagation();
    e.currentTarget.classList.add('drag-over');
}

// 处理拖拽离开
function handleDragLeave(e) {
    e.preventDefault();
    e.stopPropagation();
    e.currentTarget.classList.remove('drag-over');
}

// 处理文件拖拽放置
function handleDrop(e) {
    e.preventDefault();
    e.stopPropagation();
    e.currentTarget.classList.remove('drag-over');

    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFileSelect({ target: { files: files } });
    }
}

// 处理文件选择
function handleFileSelect(event) {
    const files = event.target.files;
    if (files.length === 0) return;

    const file = files[0];

    // 验证文件
    const validation = validateScriptFile(file);
    if (!validation.valid) {
        showAlert('❌ ' + validation.error, 'error');
        return;
    }

    // 开始上传
    uploadScriptFile(file);
}

// 验证脚本文件
function validateScriptFile(file) {
    // 检查文件大小
    if (file.size > MAX_FILE_SIZE) {
        return {
            valid: false,
            error: `文件大小超过限制 (${(file.size / 1024 / 1024).toFixed(2)}MB > 10MB)`
        };
    }

    // 检查文件类型
    const extension = '.' + file.name.split('.').pop().toLowerCase();
    if (!SUPPORTED_SCRIPT_TYPES[extension]) {
        return {
            valid: false,
            error: `不支持的文件类型: ${extension}。支持的类型: ${Object.keys(SUPPORTED_SCRIPT_TYPES).join(', ')}`
        };
    }

    return { valid: true };
}

// 上传脚本文件
async function uploadScriptFile(file) {
    const progressElement = document.getElementById('uploadProgress');
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');

    // 显示进度条
    progressElement.style.display = 'block';
    progressFill.style.width = '0%';
    progressText.textContent = '上传中... 0%';

    try {
        // 读取文件内容
        const fileContent = await readFileAsText(file);

        // 模拟上传进度
        for (let i = 0; i <= 100; i += 10) {
            progressFill.style.width = i + '%';
            progressText.textContent = `上传中... ${i}%`;
            await new Promise(resolve => setTimeout(resolve, 50));
        }

        // 存储文件信息
        const extension = '.' + file.name.split('.').pop().toLowerCase();
        uploadedScript = {
            name: file.name,
            size: file.size,
            type: extension,
            content: fileContent,
            executor: SUPPORTED_SCRIPT_TYPES[extension].executor,
            icon: SUPPORTED_SCRIPT_TYPES[extension].icon,
            typeName: SUPPORTED_SCRIPT_TYPES[extension].name
        };

        // 隐藏进度条
        progressElement.style.display = 'none';

        // 显示文件信息
        displayFileInfo();

        // 显示脚本预览
        displayScriptPreview();

        // 显示执行参数输入
        document.getElementById('scriptParams').style.display = 'block';

        // 显示上传并执行按钮
        document.getElementById('uploadAndExecuteBtn').style.display = 'inline-block';

        showAlert('✅ 文件上传成功', 'success');

    } catch (error) {
        progressElement.style.display = 'none';
        showAlert('❌ 文件上传失败: ' + error.message, 'error');
    }
}

// 读取文件内容为文本
function readFileAsText(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = e => resolve(e.target.result);
        reader.onerror = () => reject(new Error('文件读取失败'));
        reader.readAsText(file, 'UTF-8');
    });
}

// 显示文件信息
function displayFileInfo() {
    if (!uploadedScript) return;

    const fileInfo = document.getElementById('fileInfo');
    const fileIcon = document.getElementById('fileIcon');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const fileType = document.getElementById('fileType');

    fileIcon.textContent = uploadedScript.icon;
    fileName.textContent = uploadedScript.name;
    fileSize.textContent = formatFileSize(uploadedScript.size);
    fileType.textContent = uploadedScript.typeName;

    fileInfo.style.display = 'block';
}

// 显示脚本预览
function displayScriptPreview() {
    if (!uploadedScript) return;

    const scriptPreview = document.getElementById('scriptPreview');
    const scriptContent = document.getElementById('scriptContent');

    // 显示前100行
    const lines = uploadedScript.content.split('\n');
    const previewLines = lines.slice(0, 100);
    scriptContent.textContent = previewLines.join('\n');

    if (lines.length > 100) {
        scriptContent.textContent += '\n\n... (还有 ' + (lines.length - 100) + ' 行)';
    }

    scriptPreview.style.display = 'block';
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 清空上传的文件
function clearUploadedFile() {
    uploadedScript = null;

    // 隐藏相关元素
    document.getElementById('fileInfo').style.display = 'none';
    document.getElementById('scriptPreview').style.display = 'none';
    document.getElementById('scriptParams').style.display = 'none';
    document.getElementById('uploadAndExecuteBtn').style.display = 'none';
    document.getElementById('uploadProgress').style.display = 'none';

    // 清空文件输入
    document.getElementById('scriptFileInput').value = '';
    document.getElementById('scriptArgs').value = '';

    showAlert('📄 已清空上传的文件', 'info');
}

// 切换完整预览
function toggleFullPreview() {
    if (!uploadedScript) return;

    const scriptContent = document.getElementById('scriptContent');
    const button = document.querySelector('.preview-actions button');

    if (button.textContent === '查看完整内容') {
        scriptContent.textContent = uploadedScript.content;
        button.textContent = '收起内容';
    } else {
        const lines = uploadedScript.content.split('\n');
        const previewLines = lines.slice(0, 100);
        scriptContent.textContent = previewLines.join('\n');

        if (lines.length > 100) {
            scriptContent.textContent += '\n\n... (还有 ' + (lines.length - 100) + ' 行)';
        }

        button.textContent = '查看完整内容';
    }
}

// 上传并执行脚本
async function uploadAndExecuteScript() {
    if (!uploadedScript) {
        showAlert('❌ 请先上传脚本文件', 'error');
        return;
    }

    const regionId = document.getElementById('regionId').value;
    if (!regionId) {
        showAlert('❌ 请先选择地域', 'error');
        return;
    }

    if (selectedInstances.length === 0) {
        showAlert('❌ 请先选择要执行脚本的实例', 'error');
        return;
    }

    // 获取执行参数
    const scriptArgs = document.getElementById('scriptArgs').value.trim();

    // 确认对话框
    const confirmMessage = `确定要在 ${selectedInstances.length} 个实例上执行脚本吗？\n\n` +
        `脚本文件: ${uploadedScript.name}\n` +
        `脚本类型: ${uploadedScript.typeName}\n` +
        `执行器: ${uploadedScript.executor}\n` +
        (scriptArgs ? `参数: ${scriptArgs}\n` : '') +
        `\n⚠️ 请确保脚本内容安全，执行后无法撤销！`;

    if (!confirm(confirmMessage)) {
        return;
    }

    showLoading(true, '正在上传并执行脚本...');

    try {
        // 构建执行命令
        let executeCommand = '';
        const scriptPath = `/tmp/uploaded_script_${Date.now()}${uploadedScript.type}`;

        // 根据脚本类型构建命令
        if (uploadedScript.type === '.sh') {
            executeCommand = `cat > ${scriptPath} << 'EOF'\n${uploadedScript.content}\nEOF\nchmod +x ${scriptPath}\n${uploadedScript.executor} ${scriptPath}${scriptArgs ? ' ' + scriptArgs : ''}\nrm -f ${scriptPath}`;
        } else if (uploadedScript.type === '.py') {
            executeCommand = `cat > ${scriptPath} << 'EOF'\n${uploadedScript.content}\nEOF\n${uploadedScript.executor} ${scriptPath}${scriptArgs ? ' ' + scriptArgs : ''}\nrm -f ${scriptPath}`;
        } else if (uploadedScript.type === '.ps1') {
            executeCommand = `$scriptPath = "${scriptPath}"\n@"\n${uploadedScript.content}\n"@ | Out-File -FilePath $scriptPath -Encoding UTF8\n${uploadedScript.executor} -ExecutionPolicy Bypass -File $scriptPath${scriptArgs ? ' ' + scriptArgs : ''}\nRemove-Item -Path $scriptPath -Force`;
        } else if (uploadedScript.type === '.bat') {
            executeCommand = `echo @echo off > ${scriptPath}\necho ${uploadedScript.content.replace(/\n/g, '\necho ')} >> ${scriptPath}\n${scriptPath}${scriptArgs ? ' ' + scriptArgs : ''}\ndel ${scriptPath}`;
        } else {
            // 其他类型直接执行
            executeCommand = `cat > ${scriptPath} << 'EOF'\n${uploadedScript.content}\nEOF\n${uploadedScript.executor} ${scriptPath}${scriptArgs ? ' ' + scriptArgs : ''}\nrm -f ${scriptPath}`;
        }

        // 发送执行请求
        const result = await apiRequest('/api/instances/run-command', {
            method: 'POST',
            body: JSON.stringify({
                regionId: regionId,
                instanceIds: selectedInstances,
                commandContent: executeCommand,
                commandType: uploadedScript.type === '.ps1' ? 'RunPowerShellScript' : 'RunShellScript'
            })
        });

        if (result.success) {
            showAlert('✅ 脚本上传并执行成功，正在获取结果...', 'success');

            // 添加到命令历史
            addToCommandHistory(`执行脚本: ${uploadedScript.name}`, selectedInstances.length);

            // 等待一段时间后获取结果
            setTimeout(() => {
                getCommandResult(regionId, result.data.invokeId, `执行脚本: ${uploadedScript.name}`);
            }, 3000);
        } else {
            showAlert('❌ 脚本执行失败: ' + result.error, 'error');
        }
    } catch (error) {
        showAlert('❌ 脚本执行请求失败: ' + error.message, 'error');
    }

    showLoading(false);
}

// ==================== 下载相关工具函数 ====================

// 切换操作区域显示/隐藏
function toggleOperationSection(element) {
    const content = element.nextElementSibling;
    const icon = element.querySelector('.collapse-icon');

    if (content.style.display === 'none' || content.style.display === '') {
        content.style.display = 'block';
        icon.textContent = '▼';
    } else {
        content.style.display = 'none';
        icon.textContent = '▶';
    }
}

// 更新密码设置按钮状态
function updatePasswordButtonState() {
    // 密码设置功能不需要特殊的按钮状态更新
    // 保留此函数以避免引用错误
}

// 更新下载按钮状态
function updateDownloadButtonState() {
    // HTTP下载功能不需要特殊的按钮状态更新
    // 保留此函数以避免引用错误
}

// 清空下载表单
function clearDownloadForm() {
    document.getElementById('httpServerPort').value = '8000';
    document.getElementById('httpServerDirectory').value = '/tmp';
    document.getElementById('downloadFilePath').value = '';
    document.getElementById('httpServerStatus').style.display = 'none';
}

// 获取网络类型显示
function getNetworkTypeDisplay(instance) {
    if (!instance.networkType) return '-';

    const typeMap = {
        'EIP': '🌐 弹性IP',
        'PublicIP': '🌍 公网IP',
        'PrivateOnly': '🔒 仅私网'
    };

    return typeMap[instance.networkType] || instance.networkType;
}

// 获取公网IP显示
function getPublicIpDisplay(instance) {
    if (instance.eipAddress) {
        return `${instance.eipAddress} (EIP)`;
    } else if (instance.publicIpAddress) {
        return `${instance.publicIpAddress} (公网IP)`;
    } else {
        return '-';
    }
}

// 检查实例是否有公网IP（用于HTTP下载）
function hasPublicIp(instance) {
    return instance.hasPublicIp === true || instance.effectivePublicIp !== '';
}

// 获取实例的有效公网IP
function getEffectivePublicIp(instance) {
    return instance.effectivePublicIp || '';
}

// 获取网络状态消息
function getNetworkStatusMessage(instance) {
    if (!instance) return '实例信息不可用';

    let message = '📊 网络状态：\n';
    message += `• 网络类型：${getNetworkTypeDisplay(instance)}\n`;

    if (instance.eipAddress) {
        message += `• 弹性IP：${instance.eipAddress}\n`;
    }

    if (instance.publicIpAddress) {
        message += `• 公网IP：${instance.publicIpAddress}\n`;
    }

    if (instance.privateIpAddress) {
        message += `• 私网IP：${instance.privateIpAddress}\n`;
    }

    if (!instance.hasPublicIp) {
        message += '\n🔧 解决方案：\n';
        message += '• 申请并绑定弹性IP\n';
        message += '• 使用云助手下载（推荐）\n';
        message += '• 通过跳板机访问';
    }

    return message;
}

// 检查选中实例的网络状态
function checkNetworkStatus() {
    if (selectedInstances.length === 0) {
        showAlert('❌ 请先选择实例', 'error');
        return;
    }

    const resultDiv = document.getElementById('networkStatusResult');
    resultDiv.style.display = 'block';

    let html = '<h5>📊 实例网络状态检查结果</h5>';
    let hasPublicIpCount = 0;
    let noPublicIpCount = 0;

    const statusList = selectedInstances.map(instanceId => {
        const instanceInfo = getInstanceInfo(instanceId);
        if (!instanceInfo) {
            noPublicIpCount++;
            return {
                instanceId,
                status: 'unknown',
                message: '❓ 实例信息不可用'
            };
        }

        const instance = instanceInfo.instance;
        if (instanceInfo.hasPublicIp) {
            hasPublicIpCount++;
            return {
                instanceId,
                status: 'ok',
                message: `✅ ${getNetworkTypeDisplay(instance)} - ${instanceInfo.publicIp}`,
                publicIp: instanceInfo.publicIp
            };
        } else {
            noPublicIpCount++;
            return {
                instanceId,
                status: 'no-ip',
                message: `❌ ${getNetworkTypeDisplay(instance)} - 无公网IP`
            };
        }
    });

    // 汇总信息
    html += `<div class="network-summary">`;
    html += `<p><strong>📈 汇总：</strong></p>`;
    html += `<p>• ✅ 可用于HTTP下载：${hasPublicIpCount} 个实例</p>`;
    html += `<p>• ❌ 无法HTTP下载：${noPublicIpCount} 个实例</p>`;
    html += `</div>`;

    // 详细列表
    html += `<div class="network-details">`;
    html += `<p><strong>📋 详细状态：</strong></p>`;
    statusList.forEach(item => {
        const instanceName = instancesData.find(inst => inst.instanceId === item.instanceId)?.instanceName || '未命名实例';
        html += `<div class="network-item">`;
        html += `<span class="instance-name">${instanceName}</span>`;
        html += `<span class="instance-id">(${item.instanceId})</span><br>`;
        html += `<span class="network-status">${item.message}</span>`;
        html += `</div>`;
    });
    html += `</div>`;

    // 建议
    if (noPublicIpCount > 0) {
        html += `<div class="network-suggestions">`;
        html += `<p><strong>💡 建议：</strong></p>`;
        html += `<p>• 对于无公网IP的实例，建议使用云助手文件下载</p>`;
        html += `<p>• 可以申请并绑定弹性IP来启用HTTP下载</p>`;
        html += `<p>• 使用混合下载模式（HTTP + 云助手）</p>`;
        html += `</div>`;
    }

    resultDiv.innerHTML = html;
}

// 批量HTTP下载
async function startBatchHttpDownload() {
    const filePath = document.getElementById('downloadFilePath').value.trim();

    if (!filePath) {
        showAlert('❌ 请输入要下载的文件路径', 'error');
        return;
    }

    if (selectedInstances.length === 0) {
        showAlert('❌ 请先选择实例', 'error');
        return;
    }

    // 检查哪些实例有公网IP
    const availableInstances = [];
    const unavailableInstances = [];

    selectedInstances.forEach(instanceId => {
        const instanceInfo = getInstanceInfo(instanceId);
        if (instanceInfo && instanceInfo.hasPublicIp) {
            availableInstances.push({
                instanceId,
                instanceInfo,
                instance: instanceInfo.instance
            });
        } else {
            unavailableInstances.push({
                instanceId,
                instanceInfo,
                instance: instanceInfo ? instanceInfo.instance : null
            });
        }
    });

    if (availableInstances.length === 0) {
        showAlert('❌ 选中的实例都没有公网IP，无法进行HTTP下载\n\n💡 建议：为实例绑定弹性IP或使用其他下载方式', 'error');
        return;
    }

    // 显示确认信息
    let confirmMessage = `🚀 批量HTTP下载确认\n\n`;
    confirmMessage += `📁 文件路径：${filePath}\n`;
    confirmMessage += `✅ 可下载实例：${availableInstances.length} 个\n`;

    if (unavailableInstances.length > 0) {
        confirmMessage += `❌ 无公网IP实例：${unavailableInstances.length} 个\n\n`;
        confirmMessage += `⚠️ 无公网IP的实例将被跳过\n`;
    }

    confirmMessage += `\n是否继续？`;

    if (!confirm(confirmMessage)) {
        return;
    }

    // 开始批量下载
    const progressDiv = document.getElementById('batchHttpProgress');
    const progressBar = document.getElementById('batchHttpProgressBar');
    const progressStats = document.getElementById('batchHttpProgressStats');
    const progressDetails = document.getElementById('batchHttpProgressDetails');
    const cancelBtn = document.getElementById('cancelBatchHttpDownload');

    progressDiv.style.display = 'block';
    cancelBtn.style.display = 'inline-block';

    let completed = 0;
    let failed = 0;
    const total = availableInstances.length;
    const results = [];

    // 重置取消状态
    batchDownloadCancelled = false;

    progressStats.textContent = `进度：0/${total} (0%)`;
    progressDetails.innerHTML = '开始批量HTTP下载...';

    // 并发下载（限制并发数为3）
    const concurrency = 3;
    const chunks = [];
    for (let i = 0; i < availableInstances.length; i += concurrency) {
        chunks.push(availableInstances.slice(i, i + concurrency));
    }

    for (const chunk of chunks) {
        if (batchDownloadCancelled) break;

        const promises = chunk.map(async (item) => {
            if (batchDownloadCancelled) return null;

            try {
                const result = await downloadFromInstance(item, filePath);
                completed++;
                results.push({
                    instanceId: item.instanceId,
                    success: true,
                    result: result
                });

                // 更新进度
                const progress = Math.round((completed + failed) / total * 100);
                progressBar.style.width = progress + '%';
                progressStats.textContent = `进度：${completed + failed}/${total} (${progress}%) - 成功：${completed}，失败：${failed}`;

                return result;
            } catch (error) {
                failed++;
                results.push({
                    instanceId: item.instanceId,
                    success: false,
                    error: error.message
                });

                // 更新进度
                const progress = Math.round((completed + failed) / total * 100);
                progressBar.style.width = progress + '%';
                progressStats.textContent = `进度：${completed + failed}/${total} (${progress}%) - 成功：${completed}，失败：${failed}`;

                return null;
            }
        });

        await Promise.all(promises);
    }

    // 完成后的处理
    cancelBtn.style.display = 'none';

    if (!batchDownloadCancelled) {
        // 显示结果汇总
        let resultMessage = `📊 批量HTTP下载完成\n\n`;
        resultMessage += `✅ 成功：${completed} 个\n`;
        resultMessage += `❌ 失败：${failed} 个\n`;

        if (unavailableInstances.length > 0) {
            resultMessage += `⏭️ 跳过：${unavailableInstances.length} 个（无公网IP）\n`;
        }

        // 显示详细结果
        progressDetails.innerHTML = generateBatchDownloadReport(results, unavailableInstances);

        if (completed > 0) {
            showAlert(resultMessage, 'success');
        } else {
            showAlert(resultMessage + '\n💡 建议：检查网络连接和文件路径', 'warning');
        }
    }
}

// 从单个实例下载文件
async function downloadFromInstance(item, filePath) {
    const { instanceId, instanceInfo } = item;
    const port = parseInt(document.getElementById('httpServerPort').value) || 8000;
    const serverDir = document.getElementById('httpServerDirectory').value || '/tmp';

    // 1. 启动HTTP服务器
    const startResult = await apiRequest('/api/instances/start-http-server', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            regionId: document.getElementById('regionId').value,
            instanceId: instanceId,
            port: port,
            directory: serverDir
        })
    });

    if (!startResult.success) {
        throw new Error(`启动HTTP服务器失败: ${startResult.error}`);
    }

    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 2000));

    try {
        // 2. 下载文件
        const downloadResult = await downloadFileViaHTTP({
            instanceIP: instanceInfo.publicIp,
            port: port,
            filePath: filePath,
            instanceId: instanceId,
            instanceName: instanceInfo.instance.instanceName || instanceId,
            regionId: document.getElementById('regionId').value,
            directory: serverDir
        });

        // 检查是否是通配符下载（通过消息内容或数据结构判断）
        const isWildcardDownload = (downloadResult.message && downloadResult.message.includes('通配符下载完成')) ||
                                  (downloadResult.data && downloadResult.data.total !== undefined);

        if (!isWildcardDownload) {
            // 3. 非通配符下载：停止HTTP服务器
            try {
                await apiRequest('/api/instances/stop-http-server', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        regionId: document.getElementById('regionId').value,
                        instanceId: instanceId,
                        port: port
                    })
                });
            } catch (error) {
                console.warn(`停止HTTP服务器失败: ${error.message}`);
            }
        } else {
            // 通配符下载：保持HTTP服务器运行
            console.log(`通配符下载完成，HTTP服务器保持运行: http://${instanceInfo.publicIp}:${port}/`);
        }

        return downloadResult;
    } catch (error) {
        // 发生错误时停止HTTP服务器
        try {
            await apiRequest('/api/instances/stop-http-server', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    regionId: document.getElementById('regionId').value,
                    instanceId: instanceId,
                    port: port
                })
            });
        } catch (stopError) {
            console.warn(`停止HTTP服务器失败: ${stopError.message}`);
        }
        throw error;
    }
}

// 生成批量下载报告
function generateBatchDownloadReport(results, unavailableInstances) {
    let html = '<div class="download-report">';
    html += '<h5>📋 下载详细报告</h5>';

    // 成功的下载
    const successResults = results.filter(r => r.success);
    if (successResults.length > 0) {
        html += '<div class="success-section">';
        html += `<h6>✅ 成功下载 (${successResults.length})</h6>`;
        successResults.forEach(result => {
            const instanceName = instancesData.find(inst => inst.instanceId === result.instanceId)?.instanceName || '未命名实例';
            html += `<div class="report-item success">`;
            html += `<span class="instance-name">${instanceName}</span> `;
            html += `<span class="instance-id">(${result.instanceId})</span>`;
            html += `</div>`;
        });
        html += '</div>';
    }

    // 失败的下载
    const failedResults = results.filter(r => !r.success);
    if (failedResults.length > 0) {
        html += '<div class="failed-section">';
        html += `<h6>❌ 下载失败 (${failedResults.length})</h6>`;
        failedResults.forEach(result => {
            const instanceName = instancesData.find(inst => inst.instanceId === result.instanceId)?.instanceName || '未命名实例';
            html += `<div class="report-item failed">`;
            html += `<span class="instance-name">${instanceName}</span> `;
            html += `<span class="instance-id">(${result.instanceId})</span><br>`;
            html += `<span class="error-message">错误：${result.error}</span>`;
            html += `</div>`;
        });
        html += '</div>';
    }

    // 跳过的实例
    if (unavailableInstances.length > 0) {
        html += '<div class="skipped-section">';
        html += `<h6>⏭️ 跳过实例 (${unavailableInstances.length})</h6>`;
        unavailableInstances.forEach(item => {
            const instanceName = item.instance?.instanceName || '未命名实例';
            html += `<div class="report-item skipped">`;
            html += `<span class="instance-name">${instanceName}</span> `;
            html += `<span class="instance-id">(${item.instanceId})</span><br>`;
            html += `<span class="skip-reason">原因：无公网IP</span>`;
            html += `</div>`;
        });
        html += '</div>';
    }

    html += '</div>';
    return html;
}

// ==================== CORS绕过批量下载功能 ====================

// 复制文件到HTTP服务器目录
async function copyFilesToHTTPDirectory(instances, filePath) {
    const copyPromises = instances.map(async (instance) => {
        try {
            const response = await apiRequest('/api/instances/download-via-http', {
                method: 'POST',
                body: JSON.stringify({
                    instanceIP: instance.publicIp,
                    port: 8000,
                    filePath: filePath,
                    instanceId: instance.instanceId,
                    instanceName: instance.instanceName || instance.instanceId,
                    regionId: instance.regionId || 'cn-hongkong',
                    directory: '/tmp',
                    copyOnly: true  // 只复制文件，不下载
                })
            });

            if (response.success) {
                console.log(`✅ 文件复制成功: ${instance.instanceName || instance.instanceId}`);
                return { success: true, instance: instance };
            } else {
                console.error(`❌ 文件复制失败: ${instance.instanceName || instance.instanceId} - ${response.error}`);
                return { success: false, instance: instance, error: response.error };
            }
        } catch (error) {
            console.error(`❌ 文件复制异常: ${instance.instanceName || instance.instanceId} - ${error.message}`);
            return { success: false, instance: instance, error: error.message };
        }
    });

    const results = await Promise.all(copyPromises);
    const successCount = results.filter(r => r.success).length;
    const failedCount = results.length - successCount;

    if (failedCount > 0) {
        const failedInstances = results.filter(r => !r.success).map(r => r.instance.instanceName || r.instance.instanceId);
        showAlert(`⚠️ 文件复制完成：成功 ${successCount} 个，失败 ${failedCount} 个\n\n失败实例：${failedInstances.join(', ')}`, 'warning');
    } else {
        showAlert(`✅ 所有文件复制成功：${successCount} 个实例`, 'success');
    }

    return results;
}

// 统一下载功能
async function startUnifiedDownload() {
    const filePath = document.getElementById('downloadFilePath').value.trim();

    if (!filePath) {
        showAlert('❌ 请输入要下载的文件路径', 'error');
        return;
    }

    if (selectedInstances.length === 0) {
        showAlert('❌ 请先选择要下载文件的实例', 'error');
        return;
    }

    // 获取选中的实例数据
    const selectedInstancesData = instancesData.filter(instance =>
        selectedInstances.includes(instance.instanceId)
    );

    // 检查实例是否有公网IP
    const availableInstances = [];
    const unavailableInstances = [];

    for (const instance of selectedInstancesData) {
        // 检查所有可能的公网IP字段
        const publicIp = instance.effectivePublicIp ||
                         instance.eipAddress ||
                         instance.publicIpAddress ||
                         instance.publicIp ||
                         instance.PublicIpAddress;

        if (publicIp && publicIp !== '' && publicIp !== '-') {
            availableInstances.push({
                ...instance,
                publicIp: publicIp
            });
        } else {
            unavailableInstances.push(instance);
        }
    }

    if (availableInstances.length === 0) {
        showAlert('❌ 选中的实例都没有公网IP，无法进行下载\n\n💡 建议：为实例绑定弹性IP', 'error');
        return;
    }

    // 验证和修正文件路径
    const correctedFilePath = validateAndCorrectFilePath(filePath);
    let finalFilePath = filePath;

    if (correctedFilePath !== filePath) {
        const useCorrection = confirm(`🔧 文件路径建议修正：\n\n原路径：${filePath}\n建议路径：${correctedFilePath}\n\n是否使用建议路径？`);
        if (useCorrection) {
            document.getElementById('downloadFilePath').value = correctedFilePath;
            finalFilePath = correctedFilePath;
        }
    }

    // 构建下载请求
    const files = availableInstances.map(instance => ({
        instanceId: instance.instanceId,
        instanceIp: instance.publicIp,
        filePath: finalFilePath,
        fileName: `${instance.instanceName || instance.instanceId}_${finalFilePath.split('/').pop()}`,
        port: 8000
    }));

    // 显示确认信息
    let confirmMessage = `🚀 批量下载确认\n\n`;
    confirmMessage += `📁 文件路径：${filePath}\n`;
    confirmMessage += `✅ 可下载实例：${availableInstances.length} 个\n`;

    if (unavailableInstances.length > 0) {
        confirmMessage += `❌ 无公网IP实例：${unavailableInstances.length} 个\n\n`;
        confirmMessage += `跳过的实例：${unavailableInstances.map(i => i.instanceName || i.instanceId).join(', ')}\n\n`;
    }

    confirmMessage += `文件将保存到服务器的 downloads/ 目录，是否继续？`;

    if (!confirm(confirmMessage)) {
        return;
    }

    try {
        // 显示进度
        showAlert('🚀 开始批量下载...', 'info');

        // 首先尝试启动HTTP服务器
        showAlert('🌐 正在启动HTTP服务器...', 'info');
        await startHTTPServerForInstances(availableInstances);

        // 等待服务器启动
        await new Promise(resolve => setTimeout(resolve, 3000));

        // 复制文件到HTTP服务器目录
        showAlert('📋 正在复制文件到HTTP服务器目录...', 'info');
        await copyFilesToHTTPDirectory(availableInstances, finalFilePath);

        // 等待文件复制完成
        await new Promise(resolve => setTimeout(resolve, 2000));
        showAlert('📥 开始下载文件...', 'info');

        // 构建请求数据
        const requestData = {
            files: files,
            config: {
                maxConcurrency: 3,
                retryAttempts: 2,
                timeoutSeconds: 30
            }
        };

        // 调用API
        const response = await apiRequest('/api/instances/unified-download', {
            method: 'POST',
            body: JSON.stringify(requestData)
        });

        if (response.success) {
            const stats = response.data.stats;
            let resultMessage = `📊 批量下载完成\n\n`;
            resultMessage += `✅ 成功：${stats.success} 个\n`;
            resultMessage += `❌ 失败：${stats.failed} 个\n`;
            resultMessage += `📈 成功率：${stats.successRate.toFixed(1)}%\n`;
            resultMessage += `📦 总大小：${formatFileSize(stats.totalSize)}\n`;
            resultMessage += `📁 文件保存在：downloads/ 目录`;

            showAlert(resultMessage, stats.failed === 0 ? 'success' : 'warning');

            // 在控制台显示详细结果
            console.log('批量下载详细结果:', response.data.results);

            // 下载完成后自动停止HTTP服务器
            showAlert('🛑 正在停止HTTP服务器...', 'info');
            await stopHTTPServerForInstances(availableInstances);
            showAlert('✅ HTTP服务器已停止', 'success');
        } else {
            throw new Error(response.error || '下载失败');
        }

    } catch (error) {
        console.error('批量下载失败:', error);
        showAlert(`❌ 批量下载失败：${error.message}`, 'error');

        // 即使下载失败也要停止HTTP服务器
        try {
            showAlert('🛑 正在停止HTTP服务器...', 'info');
            await stopHTTPServerForInstances(availableInstances);
            showAlert('✅ HTTP服务器已停止', 'success');
        } catch (stopError) {
            console.error('停止HTTP服务器失败:', stopError);
            showAlert('⚠️ HTTP服务器停止失败，请手动停止', 'warning');
        }
    }
}

// 检查下载服务状态
async function checkDownloadService() {
    try {
        // 检查当前Web服务是否支持统一下载API
        const response = await fetch('/api/instances/unified-download', {
            method: 'OPTIONS'
        });

        if (response.ok) {
            showAlert('✅ 统一下载功能可用\n\n此功能已集成到当前Web服务中，支持并发下载和自动重试', 'success');
        } else {
            showAlert('❌ 统一下载功能不可用\n\n请确保使用最新版本的Web服务', 'error');
        }
    } catch (error) {
        showAlert(`❌ 检查服务状态失败：${error.message}\n\n可能是网络连接问题或服务未启动`, 'error');
    }
}

// 验证和修正文件路径
function validateAndCorrectFilePath(filePath) {
    // 常见的文件路径映射
    const pathMappings = {
        'hosts': '/etc/hosts',
        '/hosts': '/etc/hosts',
        'hostname': '/etc/hostname',
        '/hostname': '/etc/hostname',
        'passwd': '/etc/passwd',
        '/passwd': '/etc/passwd',
        'shadow': '/etc/shadow',
        '/shadow': '/etc/shadow',
        'fstab': '/etc/fstab',
        '/fstab': '/etc/fstab',
        'resolv.conf': '/etc/resolv.conf',
        '/resolv.conf': '/etc/resolv.conf'
    };

    // 如果是映射中的简化路径，返回完整路径
    if (pathMappings[filePath]) {
        return pathMappings[filePath];
    }

    // 如果路径不以/开头，假设是相对于/tmp的
    if (!filePath.startsWith('/')) {
        return `/tmp/${filePath}`;
    }

    // 如果路径看起来像是相对于根目录但缺少前缀，尝试修正
    if (filePath.startsWith('etc/') || filePath.startsWith('var/') || filePath.startsWith('usr/')) {
        return `/${filePath}`;
    }

    return filePath;
}

// 为实例启动HTTP服务器
async function startHTTPServerForInstances(instances) {
    const regionId = document.getElementById('regionId').value;
    const port = parseInt(document.getElementById('httpServerPort').value) || 8000;
    // 使用/tmp目录，系统文件已复制到此目录
    const directory = '/tmp';

    const startPromises = instances.map(async (instance) => {
        try {
            const response = await apiRequest('/api/instances/start-http-server', {
                method: 'POST',
                body: JSON.stringify({
                    regionId: regionId,
                    instanceId: instance.instanceId,
                    port: port,
                    directory: directory
                })
            });

            if (!response.success) {
                console.warn(`启动HTTP服务器失败 ${instance.instanceId}: ${response.error}`);
            } else {
                console.log(`✅ HTTP服务器启动成功 ${instance.instanceId}: http://${instance.publicIp}:${port}/`);
                console.log(`📁 服务器根目录: ${directory}`);
                console.log(`🔗 测试URL示例: http://${instance.publicIp}:${port}/etc/hosts`);
            }
            return response;
        } catch (error) {
            console.error(`启动HTTP服务器异常 ${instance.instanceId}:`, error);
            return { success: false, error: error.message };
        }
    });

    await Promise.all(startPromises);
}

// 为实例停止HTTP服务器
async function stopHTTPServerForInstances(instances) {
    const regionId = document.getElementById('regionId').value;
    const port = parseInt(document.getElementById('httpServerPort').value) || 8000;

    const stopPromises = instances.map(async (instance) => {
        try {
            const response = await apiRequest('/api/instances/stop-http-server', {
                method: 'POST',
                body: JSON.stringify({
                    regionId: regionId,
                    instanceId: instance.instanceId,
                    port: port
                })
            });

            if (!response.success) {
                console.warn(`停止HTTP服务器失败 ${instance.instanceId}: ${response.error}`);
            } else {
                console.log(`✅ HTTP服务器停止成功 ${instance.instanceId}`);
            }
            return response;
        } catch (error) {
            console.error(`停止HTTP服务器异常 ${instance.instanceId}:`, error);
            return { success: false, error: error.message };
        }
    });

    await Promise.all(stopPromises);
}

// 设置文件路径的辅助函数
function setFilePath(path) {
    document.getElementById('downloadFilePath').value = path;
}

// 格式化文件大小的辅助函数
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 格式化日期时间
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';

    try {
        const date = new Date(dateTimeStr);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    } catch (error) {
        return dateTimeStr;
    }
}
