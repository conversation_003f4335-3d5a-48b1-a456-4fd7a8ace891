{{template "header" .}}
            <!-- 创建弹性IP -->
            <section class="card">
                <h2>🆕 创建弹性IP</h2>
                <form id="createEipForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="regionId">地域ID *</label>
                            <input type="text" id="regionId" name="regionId" value="cn-hongkong" required>
                        </div>
                        <div class="form-group">
                            <label for="eipBandwidth">带宽 (Mbps) *</label>
                            <input type="number" id="eipBandwidth" name="eipBandwidth" value="200" min="1" max="200" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="eipChargeType">计费类型</label>
                            <select id="eipChargeType" name="eipChargeType">
                                <option value="PayByTraffic">按流量计费</option>
                                <option value="PayByBandwidth">按带宽计费</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="eipName">EIP名称</label>
                            <input type="text" id="eipName" name="eipName" placeholder="留空则自动生成">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="eipDescription">EIP描述</label>
                            <input type="text" id="eipDescription" name="eipDescription" value="Created by aliyun-manager">
                        </div>
                        <div class="form-group">
                            <label for="eipCount">创建数量</label>
                            <input type="number" id="eipCount" name="eipCount" value="1" min="1" max="10">
                        </div>
                    </div>
                    
                    <div class="button-group">
                        <button type="button" onclick="createEIP()">创建弹性IP</button>
                        <button type="button" onclick="refreshEIPList()">刷新列表</button>
                    </div>
                </form>
            </section>

            <!-- 弹性IP列表 -->
            <section class="card">
                <h2>📋 弹性IP列表</h2>
                <div class="table-container">
                    <table id="eipTable">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="selectAllEip" onchange="toggleAllEipSelection()"></th>
                                <th>EIP ID</th>
                                <th>IP地址</th>
                                <th>状态</th>
                                <th>带宽</th>
                                <th>计费类型</th>
                                <th>绑定实例</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="eipTableBody">
                            <tr>
                                <td colspan="8" class="loading">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="batch-operations">
                    <button onclick="batchReleaseEIP()">批量释放</button>
                    <button onclick="showBindModal()">批量绑定实例</button>
                    <button onclick="batchUnbindEIP()">批量解绑</button>
                </div>
            </section>

            <!-- ECS实例列表（用于绑定） -->
            <!-- <section class="card">
                <h2>🖥️ ECS实例列表</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="instanceRegionId">地域ID</label>
                        <input type="text" id="instanceRegionId" name="instanceRegionId" value="cn-hongkong">
                    </div>
                    <div class="form-group">
                        <button type="button" onclick="refreshInstanceList()">刷新实例列表</button>
                    </div>
                </div>
                
                <div class="table-container">
                    <table id="instanceTable">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="selectAllInstance" onchange="toggleAllInstanceSelection()"></th>
                                <th>实例ID</th>
                                <th>实例名称</th>
                                <th>状态</th>
                                <th>公网IP</th>
                                <th>弹性IP</th>
                            </tr>
                        </thead>
                        <tbody id="instanceTableBody">
                            <tr>
                                <td colspan="6" class="loading">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section> -->
<!-- 绑定模态框 -->
<div id="bindModal" class="bind-modal" style="display: none;">
    <div class="modal-content">
        <span class="close" onclick="closeBindModal()">&times;</span>
        <h3>批量绑定弹性IP到实例</h3>
        <div id="bindPairs"></div>
        <div class="modal-buttons">
            <button onclick="executeBind()">执行绑定</button>
            <button onclick="closeBindModal()">取消</button>
        </div>
    </div>
</div>

{{template "footer" .}}
