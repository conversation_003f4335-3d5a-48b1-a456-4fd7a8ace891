/* 弹性IP管理页面样式 */

/* 页面头部样式 */
header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: center;
}

header h1 {
    font-size: 2.2em;
    margin-bottom: 10px;
    font-weight: 600;
}

/* 导航样式 */
nav {
    margin-top: 15px;
}

nav a {
    color: white;
    text-decoration: none;
    margin: 0 10px;
    padding: 8px 16px;
    background: rgba(255,255,255,0.2);
    border-radius: 4px;
    transition: all 0.3s ease;
    font-weight: 500;
}

nav a:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-1px);
}

nav a.active {
    background: rgba(255,255,255,0.4);
    font-weight: 600;
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

.card h2 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.5em;
    font-weight: 600;
    border-bottom: 2px solid #28a745;
    padding-bottom: 10px;
}

/* 表单样式 */
.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.form-group {
    flex: 1;
    min-width: 200px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #28a745;
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.2);
}

/* 按钮组样式 */
.button-group {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.button-group button {
    padding: 12px 24px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #28a745;
    color: white;
}

.button-group button:hover {
    background: #218838;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.button-group button:active {
    transform: translateY(0);
}

/* 表格样式 */
.table-container {
    overflow-x: auto;
    margin-bottom: 20px;
}

table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

table th {
    background: #f8f9fa;
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
}

table td {
    padding: 12px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: middle;
}

table tr:hover {
    background: #f8f9fa;
}

table tr:last-child td {
    border-bottom: none;
}

/* 状态标签样式 */
.status-available {
    background: #d4edda;
    color: #155724;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-inuse {
    background: #d1ecf1;
    color: #0c5460;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-associating {
    background: #fff3cd;
    color: #856404;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-unassociating {
    background: #f8d7da;
    color: #721c24;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

/* 批量操作按钮样式 */
.batch-operations {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.batch-operations button {
    padding: 8px 16px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background: white;
    color: #495057;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.batch-operations button:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
}

.batch-operations button:first-child {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

.batch-operations button:first-child:hover {
    background: #c82333;
    border-color: #bd2130;
}

/* 绑定模态框样式 */
.bind-modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background: white;
    padding: 30px;
    border-radius: 8px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-content h3 {
    margin-bottom: 20px;
    color: #333;
    font-size: 1.3em;
}

.close {
    position: absolute;
    right: 15px;
    top: 15px;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: #aaa;
    transition: color 0.3s ease;
}

.close:hover {
    color: #333;
}

.modal-buttons {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    justify-content: flex-end;
}

.modal-buttons button {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.modal-buttons button:first-child {
    background: #28a745;
    color: white;
}

.modal-buttons button:first-child:hover {
    background: #218838;
}

.modal-buttons button:last-child {
    background: #6c757d;
    color: white;
}

.modal-buttons button:last-child:hover {
    background: #5a6268;
}

/* 加载状态样式 */
.loading {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
}

/* 按钮加载动画 */
.loading-spinner {
    display: inline-block;
    width: 14px;
    height: 14px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .form-row {
        flex-direction: column;
        gap: 15px;
    }

    .form-group {
        min-width: auto;
    }

    .button-group {
        flex-direction: column;
    }

    .button-group button {
        width: 100%;
    }

    .batch-operations {
        flex-direction: column;
    }

    .batch-operations button {
        width: 100%;
    }

    nav a {
        display: block;
        margin: 5px 0;
        text-align: center;
    }

    .table-container {
        font-size: 14px;
    }

    table th,
    table td {
        padding: 8px 6px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.card {
    animation: fadeIn 0.5s ease-out;
}

/* 复选框样式优化 */
input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

/* 操作按钮样式 */
.action-btn {
    padding: 6px 12px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background: white;
    color: #495057;
    cursor: pointer;
    font-size: 12px;
    margin: 0 2px;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
}

.action-btn.bind {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.action-btn.bind:hover {
    background: #0056b3;
    border-color: #004085;
}

.action-btn.unbind {
    background: #ffc107;
    color: #212529;
    border-color: #ffc107;
}

.action-btn.unbind:hover {
    background: #e0a800;
    border-color: #d39e00;
}

.action-btn.release {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

.action-btn.release:hover {
    background: #c82333;
    border-color: #bd2130;
}
