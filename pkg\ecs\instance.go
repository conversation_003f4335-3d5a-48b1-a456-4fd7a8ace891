package ecs

import (
	"fmt"
	"regexp"
	"strings"

	"aliyun-manager/pkg/client"
	"aliyun-manager/pkg/config"

	ecs "github.com/alibabacloud-go/ecs-20140526/v3/client"
	console "github.com/alibabacloud-go/tea-console/client"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
)

// InstanceManager ECS实例管理器
type InstanceManager struct {
	client *ecs.Client
}

// NewInstanceManager 创建新的实例管理器
func NewInstanceManager(client *ecs.Client) *InstanceManager {
	return &InstanceManager{
		client: client,
	}
}

// CreateInstanceResult 实例创建结果
type CreateInstanceResult struct {
	InstanceIds []string
	EIPIds      []string
	Success     bool
	Error       error
}

// CreateInstance 创建ECS实例
// 该方法实现了通过备选实例规格创建ECS实例的最佳实践
// 在调用RunInstances创建ECS实例时判断是否发生库存不足等错误，
// 如果发生错误，将调用DescribeRecommendInstanceType查询备选实例，
// 然后通过备选实例规格重新创建ECS实例。
// 如果启用了EIP功能，会在实例创建成功后自动创建和绑定EIP。
func (im *InstanceManager) CreateInstance(cfg *config.ECSInstanceConfig) error {
	result, err := im.CreateInstanceWithEIP(cfg, nil)
	if err != nil {
		return err
	}
	if !result.Success {
		return result.Error
	}
	return nil
}

// CreateInstanceWithEIP 创建ECS实例并可选地创建和绑定EIP
func (im *InstanceManager) CreateInstanceWithEIP(cfg *config.ECSInstanceConfig, eipManager *EIPManager) (*CreateInstanceResult, error) {
	// 如果启用EIP，确保不分配公网IP
	internetBandwidth := cfg.InternetMaxBandwidthOut
	if cfg.EnableEIP {
		// 启用EIP时，不分配公网IP
		internetBandwidth = tea.Int(0)
		console.Log(tea.String("--------------------启用EIP模式，将不分配公网IP--------------------"))
	}

	request := &ecs.RunInstancesRequest{
		RegionId:        tea.String(cfg.RegionId),
		ImageId:         tea.String(cfg.ImageId),
		InstanceType:    tea.String(cfg.InstanceType),
		SecurityGroupId: tea.String(cfg.SecurityGroupId),
		VSwitchId:       tea.String(cfg.VSwitchId),
		InstanceName: func() *string {
			// 如果是批量创建且数量大于1，使用UniqueSuffix让阿里云自动添加后缀
			if cfg.Amount > 1 && cfg.InstanceName != "" {
				return tea.String(cfg.InstanceName)
			}
			return tea.String(cfg.InstanceName)
		}(),
		UniqueSuffix: func() *bool {
			// 批量创建时启用唯一后缀
			if cfg.Amount > 1 {
				return tea.Bool(true)
			}
			return nil
		}(),
		Description: tea.String(cfg.Description),
		Password: func() *string {
			// 只有在设置了密码时才添加Password参数
			if cfg.Password != "" {
				return tea.String(cfg.Password)
			}
			return nil
		}(),
		InternetMaxBandwidthOut: tea.ToInt32(internetBandwidth),
		InternetChargeType:      tea.String(cfg.InternetChargeType),
		InstanceChargeType:      tea.String(cfg.InstanceChargeType),
		// 批量创建ECS实例，根据配置设置数量
		// 在预检模式下，Amount设置为1进行验证
		Amount: func() *int32 {
			if cfg.DryRun {
				return tea.Int32(1)
			}
			return tea.Int32(int32(cfg.Amount))
		}(),
		// 如果缺少库存可以接受的最低创建数量
		// 在预检模式下，MinAmount设置为1
		MinAmount: func() *int32 {
			if cfg.DryRun {
				return tea.Int32(1)
			}
			if cfg.MinAmount > 0 {
				return tea.Int32(int32(cfg.MinAmount))
			}
			return nil
		}(),
		// 预检参数功能，根据配置决定是否实际创建ECS实例
		DryRun: tea.Bool(cfg.DryRun),
		SystemDisk: &ecs.RunInstancesRequestSystemDisk{
			Size:     tea.String(fmt.Sprintf("%d", tea.IntValue(cfg.SystemDiskSize))),
			Category: tea.String(cfg.SystemDiskCategory),
		},
	}

	return im.runInstancesWithEIPHandling(request, cfg, eipManager)
}

// runInstancesWithEIPHandling 执行实例创建并处理EIP绑定
func (im *InstanceManager) runInstancesWithEIPHandling(request *ecs.RunInstancesRequest, cfg *config.ECSInstanceConfig, eipManager *EIPManager) (*CreateInstanceResult, error) {
	result := &CreateInstanceResult{
		InstanceIds: []string{},
		EIPIds:      []string{},
		Success:     false,
		Error:       nil,
	}

	// 执行实例创建
	console.Log(tea.String("--------------------批量创建实例开始--------------------"))

	response, err := im.client.RunInstances(request)
	if err != nil {
		im.handleCreateInstanceError(err)
		result.Error = err
		return result, err
	}

	// 获取创建的实例ID列表
	instanceIds := make([]string, 0)
	if response.Body.InstanceIdSets != nil && response.Body.InstanceIdSets.InstanceIdSet != nil {
		for _, instanceId := range response.Body.InstanceIdSets.InstanceIdSet {
			instanceIds = append(instanceIds, tea.StringValue(instanceId))
		}
	}
	result.InstanceIds = instanceIds

	console.Log(tea.String("--------------------创建实例成功，实例ID:" +
		tea.StringValue(util.ToJSONString(response.Body.InstanceIdSets.InstanceIdSet)) +
		"--------------------"))

	// 如果是预检模式或未启用EIP，直接返回成功
	if cfg.DryRun || !cfg.EnableEIP {
		result.Success = true
		return result, nil
	}

	// 如果启用EIP且不是预检模式，创建和绑定EIP
	if eipManager != nil {
		eipIds, err := im.createAndBindEIPs(instanceIds, cfg, eipManager)
		if err != nil {
			// EIP创建或绑定失败，需要回滚
			console.Log(tea.String("--------------------EIP创建或绑定失败，开始回滚--------------------"))
			im.rollbackInstances(instanceIds, eipIds, eipManager)
			result.Error = err
			return result, err
		}
		result.EIPIds = eipIds
	}

	result.Success = true
	return result, nil
}

// runInstancesWithErrorHandling 执行实例创建并处理错误（保持向后兼容）
func (im *InstanceManager) runInstancesWithErrorHandling(request *ecs.RunInstancesRequest) error {
	result, err := im.runInstancesWithEIPHandling(request, &config.ECSInstanceConfig{}, nil)
	if err != nil {
		return err
	}
	if !result.Success {
		return result.Error
	}
	return nil
}

// handleCreateInstanceError 处理创建实例时的错误
func (im *InstanceManager) handleCreateInstanceError(err error) {
	var sdkError = &tea.SDKError{}
	if _t, ok := err.(*tea.SDKError); ok {
		sdkError = _t
	} else {
		sdkError.Message = tea.String(err.Error())
	}

	console.Log(tea.String("--------------------创建实例失败:" +
		tea.StringValue(util.ToJSONString(sdkError.Code)) +
		"--------------------"))
}

// DescribeInstances 查询ECS实例
func (im *InstanceManager) DescribeInstances(regionId, instanceIds, instanceName string) (*ecs.DescribeInstancesResponse, error) {
	request := &ecs.DescribeInstancesRequest{
		RegionId: tea.String(regionId),
		PageSize: tea.Int32(100),
	}

	// 检查实例名称是否包含通配符
	hasWildcard := instanceName != "" && (strings.Contains(instanceName, "*") || strings.Contains(instanceName, "?"))

	// 如果提供了实例名称且不包含通配符，设置实例名称进行精确匹配
	if instanceName != "" && !hasWildcard {
		request.InstanceName = tea.String(instanceName)
	}

	// 如果提供了实例ID列表，解析并设置
	if instanceIds != "" {
		// 分割实例ID字符串
		ids := strings.Split(instanceIds, ",")
		var instanceIdList []*string
		for _, id := range ids {
			if strings.TrimSpace(id) != "" {
				instanceIdList = append(instanceIdList, tea.String(strings.TrimSpace(id)))
			}
		}
		if len(instanceIdList) > 0 {
			// 将实例ID列表转换为JSON字符串格式
			idsJson := util.ToJSONString(instanceIdList)
			request.InstanceIds = idsJson
		}
	}

	console.Log(tea.String("--------------------查询ECS实例--------------------"))
	response, err := im.client.DescribeInstances(request)
	if err != nil {
		return nil, err
	}

	// 如果实例名称包含通配符，需要在后端进行过滤
	if hasWildcard {
		filteredResponse := &ecs.DescribeInstancesResponse{
			Headers:    response.Headers,
			StatusCode: response.StatusCode,
			Body: &ecs.DescribeInstancesResponseBody{
				RequestId:  response.Body.RequestId,
				TotalCount: tea.Int32(0),
				PageNumber: response.Body.PageNumber,
				PageSize:   response.Body.PageSize,
				Instances: &ecs.DescribeInstancesResponseBodyInstances{
					Instance: []*ecs.DescribeInstancesResponseBodyInstancesInstance{},
				},
			},
		}

		// 创建通配符正则表达式
		wildcardRegex := createWildcardRegex(instanceName)

		// 过滤匹配的实例
		var matchedInstances []*ecs.DescribeInstancesResponseBodyInstancesInstance
		for _, instance := range response.Body.Instances.Instance {
			instanceNameValue := tea.StringValue(instance.InstanceName)
			if wildcardRegex.MatchString(instanceNameValue) {
				matchedInstances = append(matchedInstances, instance)
			}
		}

		filteredResponse.Body.Instances.Instance = matchedInstances
		filteredResponse.Body.TotalCount = tea.Int32(int32(len(matchedInstances)))

		return filteredResponse, nil
	}

	return response, nil
}

// createWildcardRegex 创建通配符正则表达式
func createWildcardRegex(pattern string) *regexp.Regexp {
	// 转义特殊字符，但保留 * 和 ?
	regexPattern := regexp.QuoteMeta(pattern)
	regexPattern = strings.ReplaceAll(regexPattern, `\*`, `.*`)
	regexPattern = strings.ReplaceAll(regexPattern, `\?`, `.`)

	// 不区分大小写匹配
	regex, _ := regexp.Compile("(?i)^" + regexPattern + "$")
	return regex
}

// DescribeInstancesForWeb 为Web界面查询ECS实例，返回简化的实例信息
func (im *InstanceManager) DescribeInstancesForWeb(regionId string) ([]map[string]interface{}, error) {
	request := &ecs.DescribeInstancesRequest{
		RegionId: tea.String(regionId),
		PageSize: tea.Int32(100),
	}

	response, err := im.client.DescribeInstances(request)
	if err != nil {
		return nil, err
	}

	var instances []map[string]interface{}
	for _, instance := range response.Body.Instances.Instance {
		// 处理公网IP地址
		var publicIpAddress string
		if instance.PublicIpAddress != nil && len(instance.PublicIpAddress.IpAddress) > 0 {
			publicIpAddress = tea.StringValue(instance.PublicIpAddress.IpAddress[0])
		}

		// 处理弹性IP地址
		var eipAddress string
		var eipAllocationId string
		if instance.EipAddress != nil {
			eipAddress = tea.StringValue(instance.EipAddress.IpAddress)
			eipAllocationId = tea.StringValue(instance.EipAddress.AllocationId)
		}

		// 处理私网IP地址
		var privateIpAddress string
		if instance.InnerIpAddress != nil && len(instance.InnerIpAddress.IpAddress) > 0 {
			privateIpAddress = tea.StringValue(instance.InnerIpAddress.IpAddress[0])
		} else if instance.VpcAttributes != nil && len(instance.VpcAttributes.PrivateIpAddress.IpAddress) > 0 {
			privateIpAddress = tea.StringValue(instance.VpcAttributes.PrivateIpAddress.IpAddress[0])
		}

		// 确定有效的公网IP（优先使用EIP，其次使用公网IP）
		var effectivePublicIp string
		var networkType string
		if eipAddress != "" {
			effectivePublicIp = eipAddress
			networkType = "EIP"
		} else if publicIpAddress != "" {
			effectivePublicIp = publicIpAddress
			networkType = "PublicIP"
		} else {
			networkType = "PrivateOnly"
		}

		instanceInfo := map[string]interface{}{
			"instanceId":         tea.StringValue(instance.InstanceId),
			"instanceName":       tea.StringValue(instance.InstanceName),
			"hostName":           tea.StringValue(instance.HostName),
			"status":             tea.StringValue(instance.Status),
			"instanceType":       tea.StringValue(instance.InstanceType),
			"cpu":                tea.Int32Value(instance.Cpu),
			"memory":             tea.Int32Value(instance.Memory),
			"osType":             tea.StringValue(instance.OSType),
			"osName":             tea.StringValue(instance.OSName),
			"creationTime":       tea.StringValue(instance.CreationTime),
			"regionId":           tea.StringValue(instance.RegionId),
			"zoneId":             tea.StringValue(instance.ZoneId),
			"deletionProtection": tea.BoolValue(instance.DeletionProtection),
			// 网络相关信息
			"publicIpAddress":   publicIpAddress,
			"eipAddress":        eipAddress,
			"eipAllocationId":   eipAllocationId,
			"privateIpAddress":  privateIpAddress,
			"effectivePublicIp": effectivePublicIp,
			"networkType":       networkType,
			"hasPublicIp":       effectivePublicIp != "",
		}
		instances = append(instances, instanceInfo)
	}

	return instances, nil
}

// ModifyInstanceAttribute 修改实例属性（主要用于取消删除保护）
func (im *InstanceManager) ModifyInstanceAttribute(instanceId string, deletionProtection bool) error {
	request := &ecs.ModifyInstanceAttributeRequest{
		InstanceId:         tea.String(instanceId),
		DeletionProtection: tea.Bool(deletionProtection),
	}

	_, err := im.client.ModifyInstanceAttribute(request)
	if err != nil {
		return err
	}

	status := "启用"
	if !deletionProtection {
		status = "取消"
	}
	console.Log(tea.String(fmt.Sprintf("--------------------实例 %s 删除保护%s成功--------------------", instanceId, status)))
	return nil
}

// DeleteInstances 批量删除ECS实例
func (im *InstanceManager) DeleteInstances(regionId string, instanceIds []string, force bool) error {
	if len(instanceIds) == 0 {
		console.Log(tea.String("--------------------无有效实例可删除--------------------"))
		return nil
	}

	// 转换为tea.String切片
	var instanceIdList []*string
	for _, id := range instanceIds {
		instanceIdList = append(instanceIdList, tea.String(id))
	}

	request := &ecs.DeleteInstancesRequest{
		RegionId:   tea.String(regionId),
		InstanceId: instanceIdList,
		Force:      tea.Bool(force),
	}

	response, err := im.client.DeleteInstances(request)
	if err != nil {
		return err
	}

	console.Log(tea.String("--------------------实例释放成功--------------------"))
	console.Log(tea.String(fmt.Sprintf("删除的实例数量: %d", len(instanceIds))))
	console.Log(util.ToJSONString(tea.ToMap(response)))
	return nil
}

// ReleaseInstances 综合释放实例方法（包含查询、取消保护、删除、释放弹性IP）
func (im *InstanceManager) ReleaseInstances(regionId, instanceIds, instanceName string, deleteProtected, force bool) error {
	var finalInstanceIds []string
	var instanceEipMap = make(map[string][]string) // 实例ID -> 弹性IP列表

	// 如果需要处理有删除保护的实例
	if deleteProtected {
		// 先查询实例
		response, err := im.DescribeInstances(regionId, instanceIds, instanceName)
		if err != nil {
			return fmt.Errorf("查询实例失败: %v", err)
		}

		// 收集实例ID、弹性IP信息并处理删除保护
		for _, instance := range response.Body.Instances.Instance {
			instanceId := tea.StringValue(instance.InstanceId)
			finalInstanceIds = append(finalInstanceIds, instanceId)

			// 收集弹性IP信息
			if instance.EipAddress != nil && instance.EipAddress.AllocationId != nil {
				eipId := tea.StringValue(instance.EipAddress.AllocationId)
				if eipId != "" {
					instanceEipMap[instanceId] = append(instanceEipMap[instanceId], eipId)
					console.Log(tea.String(fmt.Sprintf("实例 %s 绑定了弹性IP: %s", instanceId, eipId)))
				}
			}

			// 如果实例有删除保护，先取消保护
			if tea.BoolValue(instance.DeletionProtection) {
				if err := im.ModifyInstanceAttribute(instanceId, false); err != nil {
					return fmt.Errorf("取消实例 %s 删除保护失败: %v", instanceId, err)
				}
			}
		}
	} else {
		// 如果提供了实例名称，需要先查询获取实例ID
		if instanceName != "" {
			response, err := im.DescribeInstances(regionId, instanceIds, instanceName)
			if err != nil {
				return fmt.Errorf("查询实例失败: %v", err)
			}

			// 收集实例ID和弹性IP信息
			for _, instance := range response.Body.Instances.Instance {
				instanceId := tea.StringValue(instance.InstanceId)
				finalInstanceIds = append(finalInstanceIds, instanceId)

				// 收集弹性IP信息
				if instance.EipAddress != nil && instance.EipAddress.AllocationId != nil {
					eipId := tea.StringValue(instance.EipAddress.AllocationId)
					if eipId != "" {
						instanceEipMap[instanceId] = append(instanceEipMap[instanceId], eipId)
						console.Log(tea.String(fmt.Sprintf("实例 %s 绑定了弹性IP: %s", instanceId, eipId)))
					}
				}
			}
		} else if instanceIds != "" {
			// 直接使用提供的实例ID，但需要查询弹性IP信息
			ids := strings.Split(instanceIds, ",")
			for _, id := range ids {
				if strings.TrimSpace(id) != "" {
					finalInstanceIds = append(finalInstanceIds, strings.TrimSpace(id))
				}
			}

			// 查询这些实例的弹性IP信息
			if len(finalInstanceIds) > 0 {
				response, err := im.DescribeInstances(regionId, strings.Join(finalInstanceIds, ","), "")
				if err != nil {
					console.Log(tea.String(fmt.Sprintf("查询实例弹性IP信息失败: %v", err)))
				} else {
					for _, instance := range response.Body.Instances.Instance {
						instanceId := tea.StringValue(instance.InstanceId)
						if instance.EipAddress != nil && instance.EipAddress.AllocationId != nil {
							eipId := tea.StringValue(instance.EipAddress.AllocationId)
							if eipId != "" {
								instanceEipMap[instanceId] = append(instanceEipMap[instanceId], eipId)
								console.Log(tea.String(fmt.Sprintf("实例 %s 绑定了弹性IP: %s", instanceId, eipId)))
							}
						}
					}
				}
			}
		}
	}

	// 执行批量删除实例
	if err := im.DeleteInstances(regionId, finalInstanceIds, force); err != nil {
		return err
	}

	// 释放绑定的弹性IP
	return im.releaseInstanceEIPs(regionId, instanceEipMap)
}

// RunCommand 在ECS实例上执行Shell命令
func (im *InstanceManager) RunCommand(regionId string, instanceIds []string, commandContent string, commandType string) (*ecs.RunCommandResponse, error) {
	if len(instanceIds) == 0 {
		return nil, fmt.Errorf("实例ID列表不能为空")
	}

	// 转换为tea.String切片
	var instanceIdList []*string
	for _, id := range instanceIds {
		instanceIdList = append(instanceIdList, tea.String(id))
	}

	// 默认命令类型为RunShellScript
	if commandType == "" {
		commandType = "RunShellScript"
	}

	request := &ecs.RunCommandRequest{
		RegionId:       tea.String(regionId),
		InstanceId:     instanceIdList,
		CommandContent: tea.String(commandContent),
		Type:           tea.String(commandType),
		Name:           tea.String("WebCommand"),
		Description:    tea.String("通过Web界面执行的命令"),
		Timeout:        tea.Int64(60), // 60秒超时
	}

	console.Log(tea.String("--------------------执行云助手命令--------------------"))
	console.Log(tea.String(fmt.Sprintf("命令内容: %s", commandContent)))
	console.Log(tea.String(fmt.Sprintf("目标实例: %v", instanceIds)))

	response, err := im.client.RunCommand(request)
	if err != nil {
		return nil, err
	}

	console.Log(tea.String(fmt.Sprintf("命令执行ID: %s", *response.Body.InvokeId)))
	return response, nil
}

// DescribeInvocations 查询命令执行结果
func (im *InstanceManager) DescribeInvocations(regionId string, invokeId string) (*ecs.DescribeInvocationsResponse, error) {
	request := &ecs.DescribeInvocationsRequest{
		RegionId: tea.String(regionId),
		InvokeId: tea.String(invokeId),
	}

	response, err := im.client.DescribeInvocations(request)
	if err != nil {
		return nil, err
	}

	return response, nil
}

// createAndBindEIPs 为实例创建和绑定EIP
// 优化策略：先批量创建所有EIP，然后再批量绑定
func (im *InstanceManager) createAndBindEIPs(instanceIds []string, cfg *config.ECSInstanceConfig, eipManager *EIPManager) ([]string, error) {
	if eipManager == nil {
		return nil, fmt.Errorf("EIP管理器不能为空")
	}

	eipIds := make([]string, 0)
	eipResults := make([]*EIPCreateResult, 0)

	console.Log(tea.String(fmt.Sprintf("--------------------开始为 %d 个实例创建EIP--------------------", len(instanceIds))))

	// 第一阶段：批量创建EIP
	for i := 0; i < len(instanceIds); i++ {
		console.Log(tea.String(fmt.Sprintf("创建第 %d/%d 个EIP", i+1, len(instanceIds))))

		// 创建EIP
		eipResult, err := eipManager.CreateEIP(cfg)
		if err != nil {
			// 创建失败，回滚已创建的EIP
			im.rollbackEIPs(eipIds, eipManager)
			return nil, fmt.Errorf("创建第%d个EIP失败: %v", i+1, err)
		}

		if !eipResult.Success {
			// 创建失败，回滚已创建的EIP
			im.rollbackEIPs(eipIds, eipManager)
			return nil, fmt.Errorf("创建第%d个EIP失败: %v", i+1, eipResult.Error)
		}

		eipIds = append(eipIds, eipResult.EIPId)
		eipResults = append(eipResults, eipResult)
	}

	console.Log(tea.String("--------------------所有EIP创建完成，等待状态就绪--------------------"))

	// 第二阶段：等待所有EIP状态变为Available
	for i, eipResult := range eipResults {
		console.Log(tea.String(fmt.Sprintf("等待第 %d/%d 个EIP状态就绪: %s", i+1, len(eipResults), eipResult.EIPId)))

		err := eipManager.WaitForEIPStatus(eipResult.EIPId, "Available", 60)
		if err != nil {
			// 等待失败，回滚已创建的EIP
			im.rollbackEIPs(eipIds, eipManager)
			return nil, fmt.Errorf("等待第%d个EIP状态失败: %v", i+1, err)
		}
	}

	console.Log(tea.String("--------------------开始绑定EIP到实例--------------------"))

	// 第三阶段：批量绑定EIP到实例
	for i, instanceId := range instanceIds {
		eipResult := eipResults[i]
		console.Log(tea.String(fmt.Sprintf("绑定第 %d/%d 个EIP到实例: %s -> %s", i+1, len(instanceIds), eipResult.EIPId, instanceId)))

		// 绑定EIP到实例
		err := eipManager.AssociateEIP(eipResult.EIPId, instanceId)
		if err != nil {
			// 绑定失败，回滚已创建的EIP
			im.rollbackEIPs(eipIds, eipManager)
			return nil, fmt.Errorf("绑定第%d个EIP失败: %v", i+1, err)
		}

		console.Log(tea.String(fmt.Sprintf("实例 %s 的EIP绑定成功，EIP ID: %s, IP地址: %s",
			instanceId, eipResult.EIPId, eipResult.IPAddress)))
	}

	console.Log(tea.String("--------------------所有EIP创建和绑定完成--------------------"))
	return eipIds, nil
}

// rollbackInstances 回滚实例和EIP
func (im *InstanceManager) rollbackInstances(instanceIds []string, eipIds []string, eipManager *EIPManager) {
	console.Log(tea.String("--------------------开始回滚操作--------------------"))

	// 先回滚EIP
	if len(eipIds) > 0 && eipManager != nil {
		im.rollbackEIPs(eipIds, eipManager)
	}

	// 再删除实例
	if len(instanceIds) > 0 {
		console.Log(tea.String("--------------------回滚：删除已创建的实例--------------------"))
		for _, instanceId := range instanceIds {
			// 先取消删除保护
			err := im.ModifyInstanceAttribute(instanceId, false)
			if err != nil {
				console.Log(tea.String(fmt.Sprintf("取消实例 %s 删除保护失败: %v", instanceId, err)))
			}

			// 删除实例
			err = im.DeleteInstances("", []string{instanceId}, true)
			if err != nil {
				console.Log(tea.String(fmt.Sprintf("删除实例 %s 失败: %v", instanceId, err)))
			} else {
				console.Log(tea.String(fmt.Sprintf("实例 %s 已删除", instanceId)))
			}
		}
	}

	console.Log(tea.String("--------------------回滚操作完成--------------------"))
}

// rollbackEIPs 回滚EIP
func (im *InstanceManager) rollbackEIPs(eipIds []string, eipManager *EIPManager) {
	if eipManager == nil {
		return
	}

	console.Log(tea.String("--------------------回滚：释放已创建的EIP--------------------"))
	for _, eipId := range eipIds {
		// 先解绑EIP
		err := eipManager.DisassociateEIP(eipId)
		if err != nil {
			console.Log(tea.String(fmt.Sprintf("解绑EIP %s 失败: %v", eipId, err)))
		}

		// 等待EIP状态变为Available
		err = eipManager.WaitForEIPStatus(eipId, "Available", 30)
		if err != nil {
			console.Log(tea.String(fmt.Sprintf("等待EIP %s 状态变为Available失败: %v", eipId, err)))
		}

		// 释放EIP
		err = eipManager.ReleaseEIP(eipId)
		if err != nil {
			console.Log(tea.String(fmt.Sprintf("释放EIP %s 失败: %v", eipId, err)))
		} else {
			console.Log(tea.String(fmt.Sprintf("EIP %s 已释放", eipId)))
		}
	}
}

// releaseInstanceEIPs 释放实例绑定的弹性IP
func (im *InstanceManager) releaseInstanceEIPs(regionId string, instanceEipMap map[string][]string) error {
	if len(instanceEipMap) == 0 {
		console.Log(tea.String("没有需要释放的弹性IP"))
		return nil
	}

	// 需要导入VPC客户端来操作弹性IP
	// 这里我们需要创建VPC客户端
	vpcClient, err := client.NewVPCClient(regionId)
	if err != nil {
		console.Log(tea.String(fmt.Sprintf("创建VPC客户端失败: %v", err)))
		return fmt.Errorf("创建VPC客户端失败: %v", err)
	}

	// 创建EIP管理器
	eipManager := NewEIPManager(vpcClient.GetClient())

	// 收集所有需要释放的EIP
	var allEipIds []string
	for instanceId, eipIds := range instanceEipMap {
		console.Log(tea.String(fmt.Sprintf("实例 %s 的弹性IP将被释放: %v", instanceId, eipIds)))
		allEipIds = append(allEipIds, eipIds...)
	}

	// 批量释放弹性IP
	console.Log(tea.String(fmt.Sprintf("--------------------开始释放 %d 个弹性IP--------------------", len(allEipIds))))

	var failedEips []string
	for _, eipId := range allEipIds {
		console.Log(tea.String(fmt.Sprintf("正在释放弹性IP: %s", eipId)))

		// 先解绑EIP（如果还绑定着）
		err := eipManager.DisassociateEIP(eipId)
		if err != nil {
			console.Log(tea.String(fmt.Sprintf("解绑EIP %s 失败: %v", eipId, err)))
			// 解绑失败不影响继续释放
		}

		// 等待EIP状态变为Available
		err = eipManager.WaitForEIPStatus(eipId, "Available", 30)
		if err != nil {
			console.Log(tea.String(fmt.Sprintf("等待EIP %s 状态变为Available失败: %v", eipId, err)))
		}

		// 释放EIP
		err = eipManager.ReleaseEIP(eipId)
		if err != nil {
			console.Log(tea.String(fmt.Sprintf("释放EIP %s 失败: %v", eipId, err)))
			failedEips = append(failedEips, eipId)
		} else {
			console.Log(tea.String(fmt.Sprintf("EIP %s 已成功释放", eipId)))
		}
	}

	if len(failedEips) > 0 {
		console.Log(tea.String(fmt.Sprintf("部分弹性IP释放失败: %v", failedEips)))
		return fmt.Errorf("部分弹性IP释放失败: %v", failedEips)
	}

	console.Log(tea.String(fmt.Sprintf("--------------------成功释放 %d 个弹性IP--------------------", len(allEipIds))))
	return nil
}
