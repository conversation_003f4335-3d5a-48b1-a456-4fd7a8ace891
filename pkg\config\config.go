package config

import (
	"errors"
	"regexp"
)

// ECSInstanceConfig ECS实例创建配置
type ECSInstanceConfig struct {
	RegionId                string `json:"regionId"`                // 地域ID
	ImageId                 string `json:"imageId"`                 // 镜像ID
	InstanceType            string `json:"instanceType"`            // 实例规格
	SecurityGroupId         string `json:"securityGroupId"`         // 安全组ID
	VSwitchId               string `json:"vSwitchId"`               // 虚拟交换机ID
	InternetMaxBandwidthOut *int   `json:"internetMaxBandwidthOut"` // 公网出带宽最大值
	InternetChargeType      string `json:"internetChargeType"`      // 网络计费类型
	SystemDiskSize          *int   `json:"systemDiskSize"`          // 系统盘大小
	SystemDiskCategory      string `json:"systemDiskCategory"`      // 系统盘类型
	InstanceChargeType      string `json:"instanceChargeType"`      // 实例计费方式
	InstanceName            string `json:"instanceName,omitempty"`  // 实例名称（可选）
	Description             string `json:"description,omitempty"`   // 实例描述（可选）
	Password                string `json:"password,omitempty"`      // 实例登录密码（可选，8-30位，包含大小写字母和数字）
	DryRun                  bool   `json:"dryRun,omitempty"`        // 是否为预检模式（可选）
	Amount                  int    `json:"amount,omitempty"`        // 创建实例数量（可选，默认1）
	MinAmount               int    `json:"minAmount,omitempty"`     // 最少创建数量（可选）

	// EIP相关配置
	EnableEIP      bool   `json:"enableEIP,omitempty"`      // 是否启用弹性IP（可选，默认false）
	EIPBandwidth   *int   `json:"eipBandwidth,omitempty"`   // EIP带宽（可选，默认使用InternetMaxBandwidthOut）
	EIPChargeType  string `json:"eipChargeType,omitempty"`  // EIP计费类型（可选，默认PayByTraffic）
	EIPName        string `json:"eipName,omitempty"`        // EIP名称（可选）
	EIPDescription string `json:"eipDescription,omitempty"` // EIP描述（可选）
}

// ValidatePassword 验证密码复杂度
// 密码必须满足以下要求：
// 1. 长度8-30位
// 2. 包含大写字母
// 3. 包含小写字母
// 4. 包含数字
func ValidatePassword(password string) error {
	if password == "" {
		return nil // 密码为空时不验证，允许使用密钥对登录
	}

	// 检查长度
	if len(password) < 8 || len(password) > 30 {
		return errors.New("密码长度必须在8-30位之间")
	}

	// 检查是否包含大写字母
	hasUpper, _ := regexp.MatchString(`[A-Z]`, password)
	if !hasUpper {
		return errors.New("密码必须包含至少一个大写字母")
	}

	// 检查是否包含小写字母
	hasLower, _ := regexp.MatchString(`[a-z]`, password)
	if !hasLower {
		return errors.New("密码必须包含至少一个小写字母")
	}

	// 检查是否包含数字
	hasDigit, _ := regexp.MatchString(`[0-9]`, password)
	if !hasDigit {
		return errors.New("密码必须包含至少一个数字")
	}

	return nil
}

// ECSReleaseConfig ECS实例释放配置
type ECSReleaseConfig struct {
	RegionId        string `json:"regionId"`                  // 地域ID
	InstanceIds     string `json:"instanceIds,omitempty"`     // 实例ID列表，逗号分隔
	InstanceName    string `json:"instanceName,omitempty"`    // 实例名称（支持通配符）
	DeleteProtected bool   `json:"deleteProtected,omitempty"` // 是否删除有保护的实例
	Force           bool   `json:"force,omitempty"`           // 是否强制删除运行中的实例
	DryRun          bool   `json:"dryRun,omitempty"`          // 是否为预检模式
}

// Validate 验证ECS实例创建配置参数
func (c *ECSInstanceConfig) Validate() error {
	if c.RegionId == "" {
		return errors.New("regionId 不能为空")
	}
	if c.ImageId == "" {
		return errors.New("imageId 不能为空")
	}
	if c.InstanceType == "" {
		return errors.New("instanceType 不能为空")
	}
	if c.SecurityGroupId == "" {
		return errors.New("securityGroupId 不能为空")
	}
	if c.VSwitchId == "" {
		return errors.New("vSwitchId 不能为空")
	}

	// 验证系统盘大小
	if c.SystemDiskSize == nil || *c.SystemDiskSize <= 0 {
		return errors.New("systemDiskSize 必须大于0")
	}

	// 验证密码复杂度
	if err := ValidatePassword(c.Password); err != nil {
		return err
	}

	// 验证实例数量
	if c.Amount <= 0 {
		c.Amount = 1 // 默认创建1台
	}
	if c.MinAmount < 0 {
		c.MinAmount = 0
	}
	if c.MinAmount > c.Amount {
		return errors.New("minAmount 不能大于 amount")
	}

	// 验证EIP相关配置
	if c.EnableEIP {
		// 如果启用EIP，设置默认值
		if c.EIPChargeType == "" {
			c.EIPChargeType = "PayByTraffic" // 默认按流量计费
		}
		// 验证EIP计费类型
		if c.EIPChargeType != "PayByTraffic" && c.EIPChargeType != "PayByBandwidth" {
			return errors.New("eipChargeType 必须是 PayByTraffic 或 PayByBandwidth")
		}
		// 如果未设置EIP带宽，使用InternetMaxBandwidthOut的值
		if c.EIPBandwidth == nil && c.InternetMaxBandwidthOut != nil {
			c.EIPBandwidth = c.InternetMaxBandwidthOut
		}
		// 验证EIP带宽
		if c.EIPBandwidth != nil && *c.EIPBandwidth <= 0 {
			return errors.New("eipBandwidth 必须大于0")
		}
	}

	return nil
}

// ValidateRelease 验证ECS实例释放配置参数
func (c *ECSReleaseConfig) ValidateRelease() error {
	if c.RegionId == "" {
		return errors.New("regionId 不能为空")
	}

	// 至少需要提供实例ID或实例名称之一
	if c.InstanceIds == "" && c.InstanceName == "" {
		return errors.New("必须提供 instanceIds 或 instanceName 之一")
	}

	return nil
}
