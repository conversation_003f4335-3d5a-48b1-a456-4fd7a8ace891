package ecs

import (
	"fmt"
	"time"

	"aliyun-manager/pkg/config"

	console "github.com/alibabacloud-go/tea-console/client"
	"github.com/alibabacloud-go/tea/tea"
	vpc "github.com/alibabacloud-go/vpc-20160428/v6/client"
)

// EIPManager EIP管理器
type EIPManager struct {
	client *vpc.Client
}

// NewEIPManager 创建新的EIP管理器
func NewEIPManager(client *vpc.Client) *EIPManager {
	return &EIPManager{
		client: client,
	}
}

// EIPCreateResult EIP创建结果
type EIPCreateResult struct {
	EIPId     string
	IPAddress string
	Success   bool
	Error     error
}

// CreateEIP 创建弹性IP
func (em *EIPManager) CreateEIP(cfg *config.ECSInstanceConfig) (*EIPCreateResult, error) {
	if !cfg.EnableEIP {
		return &EIPCreateResult{Success: false, Error: fmt.Errorf("EIP功能未启用")}, nil
	}

	// 设置默认带宽
	bandwidth := 1 // 默认1Mbps
	if cfg.EIPBandwidth != nil && *cfg.EIPBandwidth > 0 {
		bandwidth = *cfg.EIPBandwidth
	} else if cfg.InternetMaxBandwidthOut != nil && *cfg.InternetMaxBandwidthOut > 0 {
		bandwidth = *cfg.InternetMaxBandwidthOut
	}

	// 设置默认计费类型
	chargeType := "PayByTraffic"
	if cfg.EIPChargeType != "" {
		chargeType = cfg.EIPChargeType
	}

	// 设置EIP名称
	eipName := "EIP-" + time.Now().Format("20060102-150405")
	if cfg.EIPName != "" {
		eipName = cfg.EIPName
	}

	// 设置EIP描述
	eipDescription := "Created by aliyun-manager"
	if cfg.EIPDescription != "" {
		eipDescription = cfg.EIPDescription
	}

	request := &vpc.AllocateEipAddressRequest{
		RegionId:           tea.String(cfg.RegionId),
		Bandwidth:          tea.String(fmt.Sprintf("%d", bandwidth)),
		InternetChargeType: tea.String(chargeType),
		Name:               tea.String(eipName),
		Description:        tea.String(eipDescription),
		InstanceChargeType: tea.String("PostPaid"), // 默认后付费
	}

	console.Log(tea.String("--------------------开始创建弹性IP--------------------"))
	console.Log(tea.String(fmt.Sprintf("EIP配置: 带宽=%dMbps, 计费类型=%s, 名称=%s", bandwidth, chargeType, eipName)))

	response, err := em.client.AllocateEipAddress(request)
	if err != nil {
		console.Log(tea.String("--------------------创建弹性IP失败--------------------"))
		console.Log(tea.String(fmt.Sprintf("错误信息: %s", err.Error())))
		return &EIPCreateResult{Success: false, Error: err}, err
	}

	eipId := tea.StringValue(response.Body.AllocationId)
	ipAddress := tea.StringValue(response.Body.EipAddress)

	console.Log(tea.String(fmt.Sprintf("--------------------创建弹性IP成功，EIP ID: %s, IP地址: %s--------------------", eipId, ipAddress)))

	return &EIPCreateResult{
		EIPId:     eipId,
		IPAddress: ipAddress,
		Success:   true,
		Error:     nil,
	}, nil
}

// AssociateEIP 绑定弹性IP到ECS实例
func (em *EIPManager) AssociateEIP(eipId, instanceId string) error {
	if eipId == "" || instanceId == "" {
		return fmt.Errorf("EIP ID和实例ID不能为空")
	}

	request := &vpc.AssociateEipAddressRequest{
		AllocationId: tea.String(eipId),
		InstanceId:   tea.String(instanceId),
		InstanceType: tea.String("EcsInstance"),
	}

	console.Log(tea.String("--------------------开始绑定弹性IP到ECS实例--------------------"))
	console.Log(tea.String(fmt.Sprintf("EIP ID: %s, 实例ID: %s", eipId, instanceId)))

	_, err := em.client.AssociateEipAddress(request)
	if err != nil {
		console.Log(tea.String("--------------------绑定弹性IP失败--------------------"))
		console.Log(tea.String(fmt.Sprintf("错误信息: %s", err.Error())))
		return err
	}

	console.Log(tea.String(fmt.Sprintf("--------------------绑定弹性IP成功，EIP ID: %s 已绑定到实例: %s--------------------", eipId, instanceId)))
	return nil
}

// DisassociateEIP 解绑弹性IP
func (em *EIPManager) DisassociateEIP(eipId string) error {
	if eipId == "" {
		return fmt.Errorf("EIP ID不能为空")
	}

	request := &vpc.UnassociateEipAddressRequest{
		AllocationId: tea.String(eipId),
		InstanceType: tea.String("EcsInstance"),
	}

	console.Log(tea.String("--------------------开始解绑弹性IP--------------------"))
	console.Log(tea.String(fmt.Sprintf("EIP ID: %s", eipId)))

	_, err := em.client.UnassociateEipAddress(request)
	if err != nil {
		console.Log(tea.String("--------------------解绑弹性IP失败--------------------"))
		console.Log(tea.String(fmt.Sprintf("错误信息: %s", err.Error())))
		return err
	}

	console.Log(tea.String(fmt.Sprintf("--------------------解绑弹性IP成功，EIP ID: %s--------------------", eipId)))
	return nil
}

// ReleaseEIP 释放弹性IP
func (em *EIPManager) ReleaseEIP(eipId string) error {
	if eipId == "" {
		return fmt.Errorf("EIP ID不能为空")
	}

	request := &vpc.ReleaseEipAddressRequest{
		AllocationId: tea.String(eipId),
	}

	console.Log(tea.String("--------------------开始释放弹性IP--------------------"))
	console.Log(tea.String(fmt.Sprintf("EIP ID: %s", eipId)))

	_, err := em.client.ReleaseEipAddress(request)
	if err != nil {
		console.Log(tea.String("--------------------释放弹性IP失败--------------------"))
		console.Log(tea.String(fmt.Sprintf("错误信息: %s", err.Error())))
		return err
	}

	console.Log(tea.String(fmt.Sprintf("--------------------释放弹性IP成功，EIP ID: %s--------------------", eipId)))
	return nil
}

// DescribeEIP 查询弹性IP信息
func (em *EIPManager) DescribeEIP(eipId string) (*vpc.DescribeEipAddressesResponseBodyEipAddressesEipAddress, error) {
	if eipId == "" {
		return nil, fmt.Errorf("EIP ID不能为空")
	}

	request := &vpc.DescribeEipAddressesRequest{
		AllocationId: tea.String(eipId),
	}

	response, err := em.client.DescribeEipAddresses(request)
	if err != nil {
		return nil, err
	}

	if response.Body.EipAddresses == nil || len(response.Body.EipAddresses.EipAddress) == 0 {
		return nil, fmt.Errorf("未找到EIP信息: %s", eipId)
	}

	return response.Body.EipAddresses.EipAddress[0], nil
}

// WaitForEIPStatus 等待EIP状态变更
func (em *EIPManager) WaitForEIPStatus(eipId string, targetStatus string, timeoutSeconds int) error {
	if timeoutSeconds <= 0 {
		timeoutSeconds = 60 // 默认60秒超时
	}

	console.Log(tea.String(fmt.Sprintf("等待EIP %s 状态变更为 %s，超时时间: %d秒", eipId, targetStatus, timeoutSeconds)))

	for i := 0; i < timeoutSeconds; i++ {
		eipInfo, err := em.DescribeEIP(eipId)
		if err != nil {
			return err
		}

		currentStatus := tea.StringValue(eipInfo.Status)
		if currentStatus == targetStatus {
			console.Log(tea.String(fmt.Sprintf("EIP %s 状态已变更为 %s", eipId, targetStatus)))
			return nil
		}

		time.Sleep(1 * time.Second)
	}

	return fmt.Errorf("等待EIP %s 状态变更为 %s 超时", eipId, targetStatus)
}
