package ecs

import (
	"testing"

	"aliyun-manager/pkg/config"

	"github.com/alibabacloud-go/tea/tea"
)

// TestEIPManager_CreateEIP 测试EIP创建功能
func TestEIPManager_CreateEIP(t *testing.T) {
	// 创建测试配置
	testConfig := &config.ECSInstanceConfig{
		RegionId:                "cn-hangzhou",
		EnableEIP:               true,
		EIPBandwidth:            tea.Int(5),
		EIPChargeType:           "PayByTraffic",
		EIPName:                 "test-eip",
		EIPDescription:          "Test EIP created by unit test",
		InternetMaxBandwidthOut: tea.Int(10),
	}

	// 注意：这是一个模拟测试，实际测试需要真实的VPC客户端
	// 在实际环境中，您需要设置ACCESS_KEY_ID和ACCESS_KEY_SECRET环境变量

	// 测试配置验证
	if !testConfig.EnableEIP {
		t.Error("EIP应该被启用")
	}

	if testConfig.EIPChargeType != "PayByTraffic" {
		t.Error("EIP计费类型应该是PayByTraffic")
	}

	if *testConfig.EIPBandwidth != 5 {
		t.Error("EIP带宽应该是5Mbps")
	}

	// 测试EIP配置默认值设置
	testConfigWithDefaults := &config.ECSInstanceConfig{
		RegionId:                "cn-hangzhou",
		ImageId:                 "ubuntu_20_04_x64_20G_alibase_20210420.vhd",
		InstanceType:            "ecs.t5-lc1m1.small",
		SecurityGroupId:         "sg-test123",
		VSwitchId:               "vsw-test123",
		SystemDiskSize:          tea.Int(40),
		EnableEIP:               true,
		InternetMaxBandwidthOut: tea.Int(10),
	}

	// 验证配置
	err := testConfigWithDefaults.Validate()
	if err != nil {
		t.Errorf("配置验证失败: %v", err)
	}

	// 验证默认值设置
	if testConfigWithDefaults.EIPChargeType != "PayByTraffic" {
		t.Error("EIP计费类型默认值应该是PayByTraffic")
	}

	if testConfigWithDefaults.EIPBandwidth == nil {
		t.Error("EIP带宽应该使用InternetMaxBandwidthOut的值")
	}
}

// TestEIPManager_ValidateConfig 测试EIP配置验证
func TestEIPManager_ValidateConfig(t *testing.T) {
	tests := []struct {
		name        string
		config      *config.ECSInstanceConfig
		expectError bool
		errorMsg    string
	}{
		{
			name: "有效的EIP配置",
			config: &config.ECSInstanceConfig{
				RegionId:                "cn-hangzhou",
				ImageId:                 "ubuntu_20_04_x64_20G_alibase_20210420.vhd",
				InstanceType:            "ecs.t5-lc1m1.small",
				SecurityGroupId:         "sg-test123",
				VSwitchId:               "vsw-test123",
				InternetMaxBandwidthOut: tea.Int(5),
				SystemDiskSize:          tea.Int(40),
				EnableEIP:               true,
				EIPBandwidth:            tea.Int(5),
				EIPChargeType:           "PayByTraffic",
			},
			expectError: false,
		},
		{
			name: "无效的EIP计费类型",
			config: &config.ECSInstanceConfig{
				RegionId:                "cn-hangzhou",
				ImageId:                 "ubuntu_20_04_x64_20G_alibase_20210420.vhd",
				InstanceType:            "ecs.t5-lc1m1.small",
				SecurityGroupId:         "sg-test123",
				VSwitchId:               "vsw-test123",
				InternetMaxBandwidthOut: tea.Int(5),
				SystemDiskSize:          tea.Int(40),
				EnableEIP:               true,
				EIPBandwidth:            tea.Int(5),
				EIPChargeType:           "InvalidType",
			},
			expectError: true,
			errorMsg:    "eipChargeType 必须是 PayByTraffic 或 PayByBandwidth",
		},
		{
			name: "无效的EIP带宽",
			config: &config.ECSInstanceConfig{
				RegionId:                "cn-hangzhou",
				ImageId:                 "ubuntu_20_04_x64_20G_alibase_20210420.vhd",
				InstanceType:            "ecs.t5-lc1m1.small",
				SecurityGroupId:         "sg-test123",
				VSwitchId:               "vsw-test123",
				InternetMaxBandwidthOut: tea.Int(5),
				SystemDiskSize:          tea.Int(40),
				EnableEIP:               true,
				EIPBandwidth:            tea.Int(0),
				EIPChargeType:           "PayByTraffic",
			},
			expectError: true,
			errorMsg:    "eipBandwidth 必须大于0",
		},
		{
			name: "EIP未启用时的配置",
			config: &config.ECSInstanceConfig{
				RegionId:                "cn-hangzhou",
				ImageId:                 "ubuntu_20_04_x64_20G_alibase_20210420.vhd",
				InstanceType:            "ecs.t5-lc1m1.small",
				SecurityGroupId:         "sg-test123",
				VSwitchId:               "vsw-test123",
				InternetMaxBandwidthOut: tea.Int(5),
				SystemDiskSize:          tea.Int(40),
				EnableEIP:               false,
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()

			if tt.expectError {
				if err == nil {
					t.Errorf("期望出现错误，但没有错误")
				} else if err.Error() != tt.errorMsg {
					t.Errorf("错误消息不匹配，期望: %s, 实际: %s", tt.errorMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("不期望出现错误，但出现了错误: %v", err)
				}
			}
		})
	}
}

// TestCreateInstanceResult 测试实例创建结果结构
func TestCreateInstanceResult(t *testing.T) {
	result := &CreateInstanceResult{
		InstanceIds: []string{"i-test1", "i-test2"},
		EIPIds:      []string{"eip-test1", "eip-test2"},
		Success:     true,
		Error:       nil,
	}

	if len(result.InstanceIds) != 2 {
		t.Error("实例ID数量应该是2")
	}

	if len(result.EIPIds) != 2 {
		t.Error("EIP ID数量应该是2")
	}

	if !result.Success {
		t.Error("结果应该是成功的")
	}

	if result.Error != nil {
		t.Error("错误应该是nil")
	}
}

// TestEIPCreateResult 测试EIP创建结果结构
func TestEIPCreateResult(t *testing.T) {
	result := &EIPCreateResult{
		EIPId:     "eip-test123",
		IPAddress: "*************",
		Success:   true,
		Error:     nil,
	}

	if result.EIPId != "eip-test123" {
		t.Error("EIP ID不匹配")
	}

	if result.IPAddress != "*************" {
		t.Error("IP地址不匹配")
	}

	if !result.Success {
		t.Error("结果应该是成功的")
	}

	if result.Error != nil {
		t.Error("错误应该是nil")
	}
}

// BenchmarkEIPConfigValidation 基准测试EIP配置验证性能
func BenchmarkEIPConfigValidation(b *testing.B) {
	config := &config.ECSInstanceConfig{
		RegionId:                "cn-hangzhou",
		ImageId:                 "ubuntu_20_04_x64_20G_alibase_20210420.vhd",
		InstanceType:            "ecs.t5-lc1m1.small",
		SecurityGroupId:         "sg-test123",
		VSwitchId:               "vsw-test123",
		InternetMaxBandwidthOut: tea.Int(5),
		SystemDiskSize:          tea.Int(40),
		EnableEIP:               true,
		EIPBandwidth:            tea.Int(5),
		EIPChargeType:           "PayByTraffic",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = config.Validate()
	}
}
